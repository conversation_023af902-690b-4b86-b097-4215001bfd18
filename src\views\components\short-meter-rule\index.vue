<template>
  <div class="m-auto mt-6 mb-6 flex flex-col bg-bg_color p-5 w-[50%]">
    <div class="bg-bg_color pb-5">
      <TitleBar title="短米规则" />
      <div class="flex flex-col gap-3 form mt-4">
        <div class="header flex items-center gap-3">
          <div class="w-[39%] text-right text-base whitespace-nowrap">范围</div>
          <div class="text-base flex-1 text-right whitespace-nowrap pr-[15px]">单价浮动比例(%)</div>
          <div class="w-[30px]" />
        </div>
        <div class="flex-1">
          <el-scrollbar>
            <div class="flex items-center gap-5 mb-6" v-for="(item, index) in shortMeterRuleList" :key="index">
              <el-input-number class="!flex-1" :controls="false" v-model="item.minMeter" />
              <el-select class="!flex-1" v-model="item.minCompareOp" clearable filterable>
                <el-option
                  v-for="item in ShortMeterRuleOperatorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span class="text-base whitespace-nowrap">米数</span>
              <el-select class="!flex-1" v-model="item.maxCompareOp" clearable filterable>
                <el-option
                  v-for="item in ShortMeterRuleOperatorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input-number class="!flex-1" :controls="false" v-model="item.maxMeter" />
              <el-input-number class="!flex-1" v-model="item.unitPriceFloatRatio" :controls="false" />
              <div class="w-[30px] text-center cursor-pointer delete" @click="onDeleteItem(index, item)">
                <el-icon :size="14"><Delete /></el-icon>
              </div>
            </div>
          </el-scrollbar>
        </div>

        <div class="text-right pr-[40px]">
          <el-button @click="onAddItem()">添加</el-button>
        </div>
      </div>
    </div>
    <div class="text-right mt-3">
      <el-button size="large" @click="onToggleEditMode(false)">取消</el-button>
      <el-button size="large" type="primary" @click="onSaveAsyncConfig()">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { usePageStoreHook } from "@/store/modules/page";
import TitleBar from "@/components/TitleBar";
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import { Delete } from "@element-plus/icons-vue";
import { IShortMeterRule } from "@/models";
import { deleteShortMeterRuleById, getShortMeterRule } from "@/api/short-meter";
import { isAllEmpty } from "@pureadmin/utils";
import { useConfirm } from "@/utils/useConfirm";
import { createOrUpdateShortMeterRule } from "@/api";
import { ShortMeterRuleOperatorOptions } from "@/enums";

usePageStoreHook().setTitle(useRoute().meta?.title as string);
const isEdit = ref(false);
const shortMeterRuleList = ref<Array<IShortMeterRule>>([{}]);

onMounted(() => {
  handleGetShortMeterRule();
});

const onToggleEditMode = (editMode: boolean) => {
  isEdit.value = editMode;
  handleGetShortMeterRule();
};
const onSaveAsyncConfig = async () => {
  if (shortMeterRuleList.value.length > 0) {
    for (const item of shortMeterRuleList.value) {
      if (typeof item.minMeter != "number" || typeof item.maxMeter != "number") {
        ElMessage.warning("米数不能为空");
        return;
      }

      if (item.minMeter > item.maxMeter) {
        ElMessage.warning("开始米数不能大于结束米数");
        return;
      }

      if (isAllEmpty(item.minCompareOp) || isAllEmpty(item.maxCompareOp)) {
        ElMessage.warning("操作符不能为空");
        return;
      }
    }
  }

  await createOrUpdateShortMeterRule(shortMeterRuleList.value);
  await handleGetShortMeterRule();
  ElMessage.success("修改成功");
  isEdit.value = false;
};

const onAddItem = () => {
  shortMeterRuleList.value.push({});
};

const onDeleteItem = async (index: number, item: IShortMeterRule) => {
  if (isAllEmpty(item.id)) {
    shortMeterRuleList.value.splice(index, 1);
    return;
  }

  if (!(await useConfirm("删除短米规则成功后，数据将无法恢复", "确认删除短米规则"))) {
    return;
  }
  await deleteShortMeterRuleById(item.id);
  ElMessage.success("删除成功");
  shortMeterRuleList.value.splice(index, 1);
};

const handleGetShortMeterRule = async () => {
  const { data } = await getShortMeterRule();
  shortMeterRuleList.value = data;
};
</script>

<style scoped lang="scss">
.form {
  padding: 60px 140px;
  padding: 20px 40px;
  background: #fafafa;
  box-sizing: border-box;
  border: 1px solid #ebeef5;
  padding-bottom: 16px;

  border-radius: 4px;

  .label {
    font-size: 18px;
    font-weight: bold;
    line-height: 26px;
    letter-spacing: 0;
    color: #303133;
  }
}

.delete {
  &:hover {
    color: #f56c6c;
  }
}

.tip {
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.note {
  color: var(--el-text-color-secondary);
}
</style>
