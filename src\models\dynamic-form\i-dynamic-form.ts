/** 控件值信息 */
export interface IIdentityDetailList {
  id?: number;
  metadataModelDetailId?: number;
  identityId: number;
  identityValue: string;
  identityLabel?: string;
  maxLength?: number;
  minLength?: number;
  step?: number;
}
/** 控件信息 */
export interface IDataTypeIdentityDetail {
  id?: number;
  identityCode: string;
  identityName: string;
  identityDetailList?: IIdentityDetailList[];
}

/** 动态表表单数据 */
export interface IDynamicFormItem {
  /** 采集类型 */
  collectionType?: number;
  collectionTypeName?: string;
  groupCode?: string;
  groupName?: string;
  id?: string;
  /** 工序Id */
  processId?: string;
  /** 控件Id */
  identityId?: string;
  isShow?: boolean;
  itemCode?: string;
  remarks?: string;
  /** 是否必填 */
  required?: boolean;
  /** 数据格式 */
  requiredPre?: string;
  /** 数据格式要求 */
  datumOrganization?: string;
  /** 排序 */
  sort?: number;
  status?: boolean;
  /** 检测项目编号 */
  targetCode?: string;
  /** 检测项编号别名 */
  targetCodeAlias?: string;
  /** 检测项目名称 */
  targetName?: string;
  /** 检测值 */
  targetValue?: string;
  /** 检测label */
  targetValueLabel?: string;
  /** 检测项目别名 */
  targetNameAlias?: string;
  targetValueType?: string;
  /** 单位 */
  unit?: string;
  unitLength?: string;
  initValue?: string;
  initType?: string;

  // 文本
  /** 文本最大长度 */
  maxStrLength?: number;
  /** 是否使用正则 */
  useRegular?: boolean;
  /** 正则表达式 */
  regular?: string;

  // 数字输入框
  /** 是否限制最大值 */
  validMaxValue?: boolean;
  /** 最大值 */
  maxValue?: number;
  /** 是否包含最大值 */
  includeMaxValue?: boolean;
  /** 是否限制最小值 */
  validMinValue?: boolean;
  /** 最小值 */
  minValue?: number;
  /** 是否包含最小值 */
  includeMinValue?: boolean;
  /** 小数精度 */
  decimalDigits?: number;

  // 日期格式化
  format?: string;

  // 文件大小
  /** 文件类型，多选 */
  fileType?: string[];
  /** 文件大小 */
  fileSize?: number;
  /** 校验信息 */
  validated?: boolean;
  /** 控件信息 */
  dataTypeIdentityDetail?: IDataTypeIdentityDetail;
}

/** 控件值发生变化 */
export interface IControlValueChange {
  value: any;
  key: string;
  config?: IDynamicFormItem;
}
