<template>
  <el-form label-width="80px" :rules="rules" ref="formRef" :model="form">
    <el-form-item label="租户" prop="tenantId">
      <el-select
        v-if="!props.businessLicense?.tenantId"
        placeholder="请选择未授权租户"
        class="w-full"
        v-model="form.tenantId"
        filterable
        clearable
      >
        <el-option :label="item.name" :value="item.tenantId" v-for="item in tenantOptions" :key="item.tenantId" />
      </el-select>
      <span v-else>{{ props.businessLicense?.tenantName }}</span>
    </el-form-item>

    <el-form-item label="授权文件" prop="license">
      <el-upload
        class="w-full"
        ref="uploadRef"
        drag
        :auto-upload="false"
        accept=".bin"
        v-model:file-list="fileList"
        :on-change="onFileChange"
        :limit="1"
        :on-exceed="onExceed"
        :on-remove="onRemove"
      >
        <el-icon size="24"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          <div class="upload-text"><span>将授权文件拖到此处，或</span><em>点击上传</em></div>
          <div class="upload-tips">
            <span>仅支持bin格式文件.</span>
          </div>
        </div>
      </el-upload>
    </el-form-item>
    <div class="text-right">
      <el-button @click="onCancel()">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onAuth">确认</el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage, FormInstance, UploadFile } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { ref, onMounted, reactive, watchEffect } from "vue";
import { useSystemAuthStore } from "@/store/modules";
import { IBusinessLicense, IBusinessLicenseTenant } from "@/models";
import { requiredMessage } from "@/utils/form";

const systemAuthStore = useSystemAuthStore();
const emits = defineEmits<{
  (e: "onCancelAuth"): void;
  (e: "onAuthSuccess"): void;
}>();

const props = withDefaults(
  defineProps<{
    businessLicense?: IBusinessLicense;
  }>(),
  { businessLicense: undefined }
);

const uploadRef = ref();
const licenseFile = ref();
const saveLoading = ref();
const fileList = ref<Array<File>>();
const formRef = ref<FormInstance>();
const tenantOptions = ref<Array<IBusinessLicenseTenant>>();

const rules = {
  tenantId: [{ required: true, trigger: "change", message: requiredMessage("租户") }],
  license: [{ required: true, trigger: "change", validator: validatorLicense }]
};

const form = reactive<{
  license: string;
  tenantId: string;
}>({
  license: undefined,
  tenantId: undefined
});
const onAuth = useLoadingFn(handleAuth, saveLoading);
onMounted(async () => {
  tenantOptions.value = (await systemAuthStore.queryUnAuthTenant()).data;
});

watchEffect(() => {
  if (licenseFile.value) {
    formRef.value.resetFields(["license"]);
  }
});

watchEffect(() => {
  form.tenantId = props.businessLicense?.tenantId;
});

const onExceed = (files: Array<File>) => {
  uploadRef.value?.handleRemove(files);
  fileList.value = files;
  licenseFile.value = files?.[0];
};

const onRemove = () => {
  fileList.value = undefined;
  licenseFile.value = undefined;
};

const onFileChange = async (file: UploadFile) => {
  licenseFile.value = file.raw;
};

const onCancel = () => {
  emits("onCancelAuth");
};

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function handleAuth() {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }

  const reader = new FileReader();
  reader.readAsArrayBuffer(licenseFile.value);

  reader.onload = async function (e) {
    const ints = new Uint8Array(e.target.result as any);
    const license: string = new TextDecoder("gb2312").decode(ints);
    await systemAuthStore.businessLicenseVerify({ tenantId: form.tenantId, license });
    ElMessage.success("授权成功");
    emits("onAuthSuccess");
  };
}

function validatorLicense(rule: any, value: string, callback: Function) {
  if (!licenseFile.value) {
    callback("证书文件不能为空");
    return;
  }
  callback();
}
</script>

<style scoped lang="scss"></style>
