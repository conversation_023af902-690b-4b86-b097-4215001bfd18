<template>
  <!-- <div>
    <div class="flex items-center">
      <div class="w-[30px] flex items-center justify-center cursor-pointer" @click="onViewPreview()">
        <img :style="{ width: '16px', height: '16px' }" :src="EyeIcon" />
      </div>
      <div class="w-[30px] flex items-center justify-center cursor-pointer" @click="onDownload()">
        <el-icon><Download /></el-icon>
      </div>
    </div>
    <fileExcelPreview v-if="isExcel" :src="fileUrl" :name="fileName" v-model="filePreviewShow" />
    <FileImagePreview v-else :src="fileUrl" :name="fileName" v-model="filePreviewShow" />
  </div> -->
  <div />
</template>

<script setup lang="ts">
// import { downLoadFile } from "@/api/upload-file";
// import fileExcelPreview from "@/views/components/file-view-download/file-excel-preview.vue";
// import FileImagePreview from "@/views/components/file-view-download/file-image-preview.vue";
// import { downloadByData } from "@pureadmin/utils";
// import { computed, ref } from "vue";
// import { Download } from "@element-plus/icons-vue";
// import EyeIcon from "@/assets/img/eye.png";

// const props = withDefaults(
//   defineProps<{
//     fileId?: string;
//     fileName?: string;
//     fileUrl?: string;
//     fileType?: string;
//   }>(),
//   {}
// );

// const filePreviewShow = ref(false);
// const isExcel = computed(() => props.fileName?.endsWith(".xlsx") || props.fileName?.endsWith(".xls"));
// const onViewPreview = () => {
//   filePreviewShow.value = true;
// };

// const onDownload = async () => {
//   const blob = await downLoadFile(props.fileId);
//   downloadByData(blob, props.fileName, blob.type);
// };
</script>
