<template>
  <el-drawer
    v-model="modelValue"
    title="产品信息更新版本，点击切换查看指定版本单价"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-press-escape="false"
    direction="rtl"
    class="product-info-history-drawer"
    size="50%"
  >
    <ProductInfoHistory :versionId="versionId" @onSelectProductVersion="handleSelectProductVersion($event)" />
    <template #footer>
      <el-button @click="close()">取消</el-button>
      <el-button type="primary" @click="onConfirmSelect()">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed } from "vue";
import ProductInfoHistory from "./index.vue";
import { IProductVersion } from "@/models";
import { ElMessage } from "element-plus";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onSelectProductVersion", data: IProductVersion): void;
}>();

let selectProductVersion: IProductVersion = undefined;
const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    versionId?: string;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});
const handleSelectProductVersion = (data: IProductVersion) => {
  selectProductVersion = data;
};

const onConfirmSelect = () => {
  if (!selectProductVersion || !selectProductVersion.id) {
    ElMessage.warning("请选择版本");
    return;
  }
  emits("onSelectProductVersion", selectProductVersion);
  close();
};

function close() {
  modelValue.value = false;
}
</script>

<style lang="scss" scoped>
:deep(.product-info-history-drawer) {
  .el-drawer__body {
    background: #ffffff !important;
  }
}
</style>
<style lang="scss">
.product-info-history-drawer {
  .el-drawer__body {
    background: #ffffff !important;
  }
}
</style>
