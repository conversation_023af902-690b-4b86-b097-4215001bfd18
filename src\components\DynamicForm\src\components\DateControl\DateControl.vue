<template>
  <div class="date">
    <el-date-picker
      :type="dateType"
      v-model="currentDateValue"
      :disabled="!!config?.disable"
      @change="dateChange($event)"
      placeholder="选择日期"
    />
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash-unified";
import { ref, computed } from "vue";
import { EControlType } from "@/enums";

const props = defineProps({
  inputValue: {
    type: String,
    default: null
  },
  config: {
    type: Object,
    default: () => ({})
  }
});
const emits = defineEmits(["valueChange"]);

const currentDateValue = ref(cloneDeep(props.inputValue));
/** 日期时间控件类型 */
const dateType = computed(() => props?.config?.format || "date");
/**
 * 控件值更新
 */
const dateChange = value => {
  emits("valueChange", {
    value,
    key: EControlType.DateControl,
    config: props.config
  });
};
</script>

<style scoped lang="scss"></style>
