import { IBase } from "../i-base";
import { IProductModel } from "../product-model";

export interface IProduct extends IBase {
  /*** 盘重 */
  coilWeight?: string;
  /** 平方数  */
  crossSection: number;

  /** 型号Id */
  modelId?: string;

  /** 型号名称 */
  modelName?: string;
  /** 产品ID  */
  productCode?: string;

  /*** 盘名称	 */
  reelName?: string;
  /** 备注*/
  remark?: string;

  versionId?: string;

  /*** 单位 */
  unit?: string;

  /*** 型号	 */
  model?: IProductModel;

  /** 规格 */
  specification?: string;

  /** 电压等级 */
  voltage?: string;
}
