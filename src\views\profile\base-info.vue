<template>
  <div>
    <div class="flex-bc mb-8">
      <div class="flex flex-col">
        <span class="text-lg">个人信息</span>
        <span class="text-sm text-secondary">更新您的个人信息</span>
      </div>
    </div>
    <div class="p-5 flex">
      <div class="w-full mx-5">
        <el-form ref="formRef" :model="form" :rules="rules" size="large" label-width="100px" label-position="right">
          <el-form-item label="昵称" prop="nickname">
            <el-input placeholder="请输入昵称" v-model="form.nickname" />
          </el-form-item>

          <ElCollapse>
            <el-form-item label="联系电话" prop="mobile">
              <el-input placeholder="请输入联系电话" v-model="form.mobile" />
            </el-form-item>
          </ElCollapse>

          <el-form-item label="电子邮件" prop="email">
            <el-input placeholder="请输入电子邮件地址" v-model="form.email" />
          </el-form-item>

          <el-form-item label="个人简介" prop="remark">
            <el-input
              type="textarea"
              placeholder="请输入个人简介"
              show-word-limit
              clearable
              v-model="form.remark"
              :maxlength="15"
              resize="none"
            />
          </el-form-item>
          <el-form-item>
            <ElButton size="large" type="primary" @click="onConfirmEditPassword()">保存更改</ElButton>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { ElForm, ElMessage, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IAccountForm } from "@/models";
import { useAccountStore } from "@/store/modules";
import { MOBILE_REGEXP } from "@/consts";
import { useUserStore } from "@/store/modules/user";
import { postMessage } from "@/utils/browsingContextsCommunication";
import { TopicEnum } from "@/utils/browsingContextsCommunication/topic.enum";
import { IProfile } from "@/models/user";
import { cloneDeep } from "lodash-unified";

const accountStore = useAccountStore();
const userStore = useUserStore();
const formRef = ref<FormInstance>();
const form = reactive<Partial<IAccountForm>>({
  id: undefined,
  username: undefined,
  mobile: undefined,
  nickname: undefined,
  email: undefined,
  remark: undefined
});
const imageUrl = ref("");
const rules: FormRules = {
  username: [{ required: true, message: requiredMessage("用户名"), trigger: "change" }],
  mobile: [
    { required: true, message: "联系人手机不能为空", trigger: "change" },
    { required: true, message: "联系人手机号码格式不正确", trigger: "change", pattern: MOBILE_REGEXP }
  ],
  nickname: [{ required: true, message: requiredMessage("姓名"), trigger: "change" }],
  email: [{ type: "email", message: "邮箱格式不正确", trigger: "change" }]
};

watchEffect(() => {
  if (userStore.profile && Object.keys(userStore.profile).length) {
    const { id, username, mobile, nickname, email, remark } = userStore.profile;
    Object.assign(form, { id, username, mobile, nickname, email, remark });
    imageUrl.value = userStore.profile?.avatarInfo?.url
      ? userStore.profile.avatarInfo.url
      : userStore.profile.nickname.charAt(0);
  }
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

const onConfirmEditPassword = async () => {
  if (!(await validate())) {
    return;
  }
  await accountStore.editAccount(form);
  ElMessage.success("更新成功");
  const profile: IProfile = { ...userStore.profile, ...form };
  userStore.setUserProfile(profile);
  postMessage(TopicEnum.UPDATE_PROFILE, cloneDeep(profile));
};
</script>

<style scoped lang="scss">
.avatar {
  border-radius: 50%;
  width: 144px;
  height: 144px;

  img {
    border-radius: 50%;
  }

  .word-avatar {
    width: 144px;
    height: 144px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #dddddd;
  }
}
</style>
