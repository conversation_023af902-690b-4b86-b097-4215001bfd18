import { IEmployeeReq, IRole, IRoleForm } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/enterprise";

export const useRoleStore = defineStore({
  id: "cx-enterprise-role",
  state: () => ({
    roles: [] as Array<IRole>,
    roleForm: {} as IRoleForm
  }),
  actions: {
    async queryRoleList() {
      const res = await api.queryRoleList();
      this.roles = res.data;
    },

    async searchEmployee(params?: IEmployeeReq) {
      return await api.queryEmployee(params);
    },

    async addRole(data: IRoleForm) {
      return api.addRole(data);
    },

    async editRole(data: IRoleForm) {
      return api.editRole(data);
    },

    async deleteRole(id: string) {
      return api.deleteRole(id);
    },

    async bindEmployeeToRole(roleId: string, userIds: Array<string>) {
      return api.bindEmployeeToRole(roleId, userIds);
    },

    async removeEmployeeRole(roleId: string, userId: string) {
      return api.removeEmployeeRole(roleId, userId);
    },

    setRoleForm(role: Partial<IRoleForm>) {
      this.roleForm = role;
    }
  }
});
