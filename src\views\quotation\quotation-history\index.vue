<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="询价单位" prop="inquiryOrgId">
          <el-select clearable placeholder="请选择询价单位" v-model="state.params.inquiryOrgId" filterable>
            <el-option
              v-for="item in state.inquiryOrganizationList || []"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </ElFormItem>
        <ElFormItem label="销售人姓名" prop="inquiryStartTime">
          <el-input
            v-model="state.params.operatorName"
            class="!w-full fixed-width-input"
            placeholder="请输入操作人姓名"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="报价批次号" prop="inquiryStartTime">
          <el-input
            v-model="state.params.quotationBatchNo"
            class="!w-full fixed-width-input"
            placeholder="请输入报价批次号"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="所在部门" prop="inquiryStartTime">
          <el-input
            v-model="state.params.deptName"
            class="!w-full fixed-width-input"
            placeholder="请输入所在部门"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="询价开始时间" prop="inquiryStartTime">
          <el-date-picker
            v-model="state.params.inquiryStartTime"
            type="date"
            placeholder="请选择询价开始时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </ElFormItem>

        <ElFormItem label="询价结束时间" prop="inquiryStartTime">
          <el-date-picker
            v-model="state.params.inquiryEndTime"
            type="date"
            placeholder="请选择询价结束时间"
            value-format="YYYY-MM-DD 23:59:59"
          />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #quotationBatchNo="{ row }">
          <el-text @click="onRedirectDetail(row.id)" type="primary" link class="hover:cursor-pointer"
            >{{ row.quotationBatchNo }}
          </el-text>
        </template>
        <template #parseTokenUsage="{ row }">
          <ThousandSeparator :value="row.parseTokenUsage" />
        </template>

        <template #quotationAmount="{ row }">
          <ThousandSeparator :value="row.quotationAmount" />
        </template>
        <template #operatorName="{ row }">
          <CXEmployee :name="row.operatorName" />
        </template>

        <template #inquiryAttachmentName="{ row }">
          <template v-if="row.inquiryType === InquiryTypeEnum.file">
            <el-popover placement="top" :show-after="100" :popper-style="{ width: 'auto' }">
              <div class="flex flex-col gap-3">
                <div class="flex items-center" v-for="item in row.inquiryAttachmentList" :key="item.id">
                  <CxDownload :name="item.inquiryAttachmentName" :id="item.inquiryAttachmentId" />
                  <FilePreviewIcon
                    class="ml-2"
                    :file-id="item.inquiryAttachmentId"
                    :file-name="item.inquiryAttachmentName"
                    :file-url="item.inquiryAttachmentUrl"
                  />
                </div>
              </div>
              <template #reference>
                <CxDownload
                  :name="row.inquiryAttachmentList?.[0]?.inquiryAttachmentName"
                  :id="row.inquiryAttachmentList?.[0]?.inquiryAttachmentId"
                />
              </template>
            </el-popover>
          </template>
          <span v-else>{{ row.inquiryTextContent }}</span>
        </template>
        <template #quotationAttachmentId="{ row }">
          <FileViewAndDownload
            :file-name="row.quotationAttachmentName"
            :file-id="row.quotationAttachmentId"
            :file-url="row.quotationAttachmentUrl"
          >
            <CxDownload :name="row.quotationAttachmentName" :id="row.quotationAttachmentId"
          /></FileViewAndDownload>
        </template>

        <template #parseStatus="{ row }">
          <CxTag :type="TaskParseStatusEnumMapColor[row.parseStatus]">{{
            TaskParseStatusEnumMapDesc[row.parseStatus]
          }}</CxTag>
        </template>

        <template #operation="data">
          <div>
            <ElButton link type="primary" @click="onRedirectDetail(data.row.id)"> 详情 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="quotation-history">
import { onMounted, ref, reactive } from "vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryQuotationHistory } from "@/api/quotation/quotation-history";
import { IInquiryOrganization, IQuotationHistory, IQuotationHistoryReq } from "@/models";
import { ElButton } from "element-plus";
import CXEmployee from "@/components/cx-employee/index.vue";
import CxDownload from "@/components/cx-download/index.vue";
import { TaskParseStatusEnumMapColor, TaskParseStatusEnumMapDesc, InquiryTypeEnum } from "@/enums";
import CxTag from "@/components/CxTag/index.vue";
import { queryInquiryOrganizationList } from "@/api/basic/inquiry-organization";
import { useRouter } from "vue-router";
import ThousandSeparator from "@/components/thousand-separator/index.vue";
import FileViewAndDownload from "@/views/components/file-view-download/index.vue";
import FilePreviewIcon from "@/views/components/file-view-download/file-preview-icon.vue";

const router = useRouter();
const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IQuotationHistory>;
  params: IQuotationHistoryReq;
  inquiryOrganizationList: Array<IInquiryOrganization>;
}>({
  list: [],
  params: {},
  inquiryOrganizationList: []
});

onMounted(() => {
  requestList();
  handleQueryInquiryOrganization();
});

const onQuery = () => {
  pagination.currentPage = 1;
  requestList();
};

const onResetQuery = () => {
  pagination.currentPage = 1;
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onRedirectDetail = (id: string) => {
  router.push(`/quotation/history/detail/${id}`);
};

const requestList = useLoadingFn(async () => {
  let params: IQuotationHistoryReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  //   if (state.params.inquiryStartTime) {
  // params.inquiryStartTime=
  //   }

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryQuotationHistory(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);

const handleQueryInquiryOrganization = async () => {
  const { data } = await queryInquiryOrganizationList();
  state.inquiryOrganizationList = data;
};
</script>

<style scoped lang="scss">
.fixed-width-input {
  /* 使用 CSS 变量设置宽度，可以根据需要调整 */
  --input-width: 200px;
  min-width: var(--input-width);
  max-width: var(--input-width);
}

/* 确保输入框内部元素不会影响外部宽度 */
.fixed-width-input :deep(.el-input__wrapper) {
  width: 100%;
  box-sizing: border-box;
}

/* 确保清除按钮不会影响输入框宽度 */
.fixed-width-input :deep(.el-input__suffix) {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}
</style>
