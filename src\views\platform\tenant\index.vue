<template>
  <div class="bg-bg_color pt-[8px] px-6 flex justify-between">
    <ElForm :inline="true" :model="state.params" size="large" class="ml-6 flex-1">
      <ElFormItem label="综合搜索：" class="w-1/4">
        <ElInput class="w-full" clearable v-model="state.params.keyWords" placeholder="请输入企业名称/企业简称" />
      </ElFormItem>

      <ElFormItem label="状态：" class="w-1/5">
        <ElSelect v-model="state.params.status" placeholder="请选择状态" class="w-full" clearable filterable>
          <ElOption v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem>
        <ElButton size="large" type="primary" @click="onConfirmQuery()">搜索</ElButton>
        <ElButton size="large" @click="onResetQuery()">重置</ElButton>
      </ElFormItem>
    </ElForm>
    <add-tenant-dialog @post-save-success="queryTenant" />
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      size="large"
      :data="tenantStore.tenants"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="tenantStore.loading"
      @page-size-change="queryTenant"
      @page-current-change="queryTenant"
    >
      <template #tokenLimitEnabled="{ row }">
        <el-switch
          v-model="row.tokenLimitEnabled"
          disabled
          :active-value="true"
          :inactive-value="false"
          active-text="启用"
          inactive-text="禁用"
          inline-prompt
        />
      </template>
      <template #quotationSecondConfirm="{ row }">
        <el-switch
          v-model="row.quotationSecondConfirm"
          disabled
          :active-value="true"
          :inactive-value="false"
          active-text="启用"
          inactive-text="禁用"
          inline-prompt
        />
      </template>
      <template #operation="data">
        <div>
          <ElButton type="primary" link @click="onEditTenantModalVis(data.row)"> 编辑 </ElButton>
          <ElButton type="primary" link @click="onInitTenantModalVis(data.row)"> 初始化 </ElButton>
          <!-- <ElButton type="primary" link @click="onRedirectAuth(data.row)"> 对外授权 </ElButton> -->
          <!--<ElButton link type="danger" @click="onDeleteTenant(data.row)"> 删除 </ElButton>-->
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>

    <el-dialog
      :title="getTenantFormModalTitle()"
      destroy-on-close
      class="middle"
      align-center
      v-model="state.tenantFormModalVis"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-scrollbar class="content">
        <TenantForm ref="tenantFormRef" />
      </el-scrollbar>
      <template #footer>
        <el-button @click="onCancelEmployeeFormModal()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSaveTenant()">保存</el-button>
      </template>
    </el-dialog>
    <init-tenant-dialog ref="initTenantDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { useColumns } from "./columns";
import { reactive, ref, watch } from "vue";
import { IOption, ITenant, ITenantForm, ITenantReq } from "@/models";
import { ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import TenantForm from "./tenant-form.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { usePageStoreHook } from "@/store/modules/page";
import { useTenantStore } from "@/store/modules";
import { useRoute } from "vue-router";
import { useUserStore } from "@/store/modules/user";
import AddTenantDialog from "./add-tenant-dialog/index.vue";
import InitTenantDialog from "./init-tenant-dialog/index.vue";

usePageStoreHook().setTitle((useRoute().meta?.title as string) || "租户管理");

const stateOptions: Array<IOption> = [
  { label: "全部", value: -1 },
  { label: "启用", value: 0 },
  { label: "禁用", value: 1 }
];
const initTenantDialogRef = ref<InstanceType<typeof InitTenantDialog>>();

const { columns } = useColumns();
const { pagination } = useTableConfig();
const tenantStore = useTenantStore();
const userStore = useUserStore();
pagination.pageSize = 20;

const state = reactive<{
  tenantFormModalVis: boolean;
  isAddTenant: boolean;
  params: ITenantReq;
}>({
  tenantFormModalVis: false,
  isAddTenant: false,
  params: {}
});

watch(
  () => tenantStore.total,
  () => {
    pagination.total = tenantStore.total;
  },
  {
    immediate: true
  }
);

const saveLoading = ref<boolean>(false);
const tenantFormRef = ref<InstanceType<typeof TenantForm>>();

queryTenant();

const getTenantFormModalTitle = () => (state.isAddTenant ? "新增租户" : "编辑租户");
const handleSaveTenant = useLoadingFn(onAddTenant, saveLoading);

async function onAddTenant() {
  const formValue: ITenantForm | false = await tenantFormRef.value.getValidValue().catch(() => false);
  if (!formValue) {
    return;
  }
  if (!formValue.id) {
    await tenantStore.addTenant(formValue);
  } else {
    await tenantStore.editTenant(formValue);
    if (userStore.profile.tenantInfo.id === formValue.id) {
      userStore.setUserTenantInfo({ ...formValue, createTime: userStore.profile.tenantInfo.createTime });
    }
  }
  state.tenantFormModalVis = false;
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  pagination.currentPage = 1;
  queryTenant();
  resetAccountTenants();
}

const onInitTenantModalVis = async (data: ITenant) => {
  initTenantDialogRef.value.openDialog(data.id);
};

const onEditTenantModalVis = async (data: ITenant) => {
  state.tenantFormModalVis = true;
  state.isAddTenant = false;
  const tenant: ITenant = await tenantStore.getTenantDetailById(data.id);
  tenantStore.setTenantForm(tenant);
};

const _onDeleteTenant = async (data: ITenant) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await tenantStore.deleteTenant(data.id);
  ElMessage.success("删除成功");
  queryTenant();
  resetAccountTenants();
};

const onResetQuery = () => {
  state.params = {};
  pagination.currentPage = 1;
  queryTenant();
};

const onConfirmQuery = () => {
  pagination.currentPage = 1;
  queryTenant();
};

const onCancelEmployeeFormModal = () => {
  state.tenantFormModalVis = false;
};

function queryTenant() {
  tenantStore.queryTenant({
    ...state.params,
    status: state.params?.status < 0 ? undefined : state.params.status,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  });
}

async function resetAccountTenants() {
  userStore.accountTenants = [];
  await userStore.getAccountTenants;
}
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}

.content {
  height: 500px; /* 设置最大高度 */
}
</style>
