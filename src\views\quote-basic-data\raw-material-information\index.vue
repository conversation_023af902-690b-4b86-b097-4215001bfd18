<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="原材料类型：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.type" placeholder="请输入原材料类型" />
        </ElFormItem>
        <ElFormItem label="原材料名称：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.rawName" placeholder="请输入原材料名称" />
        </ElFormItem>
        <ElFormItem label="原材料型号：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.rawModel" placeholder="请输入原材料型号" />
        </ElFormItem>
        <ElFormItem label="原材料规格：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.rawSpec" placeholder="请输入原材料规格" />
        </ElFormItem>
        <ElFormItem label="物料编码：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.materialCode" placeholder="请输入物料编号" />
        </ElFormItem>

        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <div class="flex gap-2">
        <AddEditRawMaterialInformationDialog mode="add" @post-save-success="onQuery()">
          <template #trigger="{ openDialog }">
            <el-button class="mb-5" :icon="Plus" type="primary" @click="openDialog">新增</el-button>
          </template>
        </AddEditRawMaterialInformationDialog>
        <el-button class="mb-5" :icon="Delete" type="danger" :disabled="disabledBatchDelete" @click="onBatchDelete()"
          >批量删除</el-button
        >
      </div>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
        @selection-change="onSelectionChange($event)"
      >
        <template #latestCostPrice="{ row }"> {{ row.latestCost?.price }}/{{ row.latestCost?.productUnit }} </template>
        <template #status="{ row }">
          <CxTag :type="row.status ? 'success' : 'warning'">{{ row.status ? "启用" : "禁用" }}</CxTag>
        </template>
        <template #creatorName="{ row }">
          <CXEmployee :name="row.creatorName" />
        </template>
        <template #updaterName="{ row }">
          <CXEmployee :name="row.updaterName" />
        </template>
        <template #operation="data">
          <div>
            <ElButton link type="primary" @click="onViewDetail(data.row.id)"> 详情 </ElButton>
            <AddEditRawMaterialInformationDialog mode="edit" :id="data.row.id" @post-save-success="onQuery()">
              <template #trigger="{ openDialog }">
                <el-button link type="primary" @click="openDialog">编辑</el-button>
              </template>
            </AddEditRawMaterialInformationDialog>
            <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="raw-material-information">
import { onMounted, ref, reactive, computed } from "vue";
import { Plus, Delete } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  queryRawMaterialInformation,
  deleteRawMaterialInformationById,
  batchDeleteRawMaterialInformationById
} from "@/api/quote-basic-data/raw-material-information";
import { IRawMaterialInformation, IRawMaterialInformationReq } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import AddEditRawMaterialInformationDialog from "./add-edit-raw-material-information/dialog.vue";
import CxTag from "@/components/CxTag/index.vue";
import { useRouter } from "vue-router";
import CXEmployee from "@/components/cx-employee/index.vue";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const router = useRouter();
const loading = ref(false);
const state = reactive<{
  list: Array<IRawMaterialInformation>;
  selectedList: Array<IRawMaterialInformation>;
  params: IRawMaterialInformationReq;
}>({
  list: [],
  selectedList: [],
  params: {}
});

const disabledBatchDelete = computed(() => state.selectedList.length === 0);

onMounted(() => {
  requestList();
});

const onQuery = () => {
  requestList();
};

const onSelectionChange = (data: Array<IRawMaterialInformation>) => {
  state.selectedList = data;
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onBatchDelete = async () => {
  if (!(await useConfirm("确认批量删除后，数据将无法恢复", "确认批量删除"))) {
    return;
  }
  await batchDeleteRawMaterialInformationById(state.selectedList.map(item => item.id));
  ElMessage.success("删除成功");
  requestList();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteRawMaterialInformationById(id);
  ElMessage.success("删除成功");
  requestList();
};

const onViewDetail = (id: string) => {
  router.push(`/basic/material/${id}`);
};

const requestList = useLoadingFn(async () => {
  let params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryRawMaterialInformation(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
