<template>
  <div class="flex h-full" v-loading="loading">
    <div class="w-40 modules-box">
      <div class="modules text-base">
        <div
          class="item"
          :class="{ 'activate-module': activeIndex === index }"
          v-for="(item, index) in state.menuTree"
          :key="item.id"
          @click="scrollToModule(index)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="flex-1 permission-box flex flex-col">
      <div class="flex justify-between items-center pl-5 pb-2">
        <el-checkbox
          label="全部权限"
          v-model="state.allCheckAll"
          :indeterminate="state.isIndeterminate"
          @change="onCheckAllPermission($event)"
        />
        <ElButton
          v-auth="PermissionKey.enterprise.enterpriseRolePermissionEdit"
          type="primary"
          :loading="saveLoading"
          @click="handleSaveRoleMenu()"
          >保存</ElButton
        >
      </div>
      <div class="overflow-auto flex flex-col">
        <el-scrollbar class="flex-1" ref="scrollRef" @scroll="calcActiveIndex">
          <div ref="listRef" v-for="item in state.menuTree" :key="item.id">
            <div class="text-lg pl-5">{{ item.name }}</div>
            <div class="pl-5 pb-2" v-for="menu in item.children" :key="menu.id">
              <div class="flex items-center">
                <el-checkbox
                  v-model="menu.checkAll"
                  :label="menu.id"
                  @change="onCheckAllGroupPermissionChange($event, menu)"
                  :indeterminate="menu.isIndeterminate"
                >
                  <span class="font-semibold">{{ menu.name }}</span>
                </el-checkbox>
              </div>
              <div class="permission-group flex text-sm">
                <el-checkbox-group
                  class="flex flex-wrap"
                  v-model="menu.checkPermissions"
                  @change="onCheckPermissionChange($event, menu)"
                >
                  <el-checkbox
                    class="basis-1/3 !mr-0"
                    v-for="child in menu.children"
                    :key="child.id"
                    :label="child.id"
                    >{{ child.name }}</el-checkbox
                  >
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IMenu, IMenuExt } from "@/models/menu";
import { useMenuStore } from "@/store/modules";
import { cloneDeep } from "@pureadmin/utils";
import { CheckboxValueType, ElMessage } from "element-plus";
import { onMounted, reactive, watch, ref } from "vue";
import { PermissionKey } from "@/consts";
import { useLoadingFn } from "@/utils/useLoadingFn";
import type { ScrollbarInstance } from "element-plus";
import { useIntersectionObserver, useThrottleFn } from "@vueuse/core";

const props = withDefaults(
  defineProps<{
    roleId: string;
  }>(),
  {
    roleId: ""
  }
);
let stopArr: Array<() => void> = [];
const scrollRef = ref<ScrollbarInstance>();
const listRef = ref<Array<HTMLDivElement>>([]);
const activeIndex = ref(0);
const loading = ref(false);
const menuStore = useMenuStore();
const state = reactive<{
  allCheckAll: boolean;
  isIndeterminate: boolean;
  menuTree: Array<IMenuExt>;
}>({
  allCheckAll: undefined,
  isIndeterminate: undefined,
  menuTree: []
});
const saveLoading = ref<boolean>(false);
const handleSaveRoleMenu = useLoadingFn(onSaveRoleMenu, saveLoading);

onMounted(async () => {
  const menuTreeRes = await menuStore.queryMenuTree();
  state.menuTree = cloneDeep(menuTreeRes.data);
});

const getRoleMenuByRoleId = useLoadingFn(menuStore.getRoleMenuByRoleId, loading);

watch(
  () => props.roleId,
  async roleId => {
    if (roleId) {
      const roleMenu: Array<IMenu> = await getRoleMenuByRoleId(roleId);
      handleMenuChecked(roleMenu);
    }
  }
);

/**
 * @description: 滚动时计算出应该激活的index
 */
const calcActiveIndex = useThrottleFn(() => {
  if (stopArr.length) {
    return;
  }
  stopArr = listRef.value.map((div, index) => {
    const { stop } = useIntersectionObserver(div, ([{ isIntersecting }]) => {
      // 是否由顶部滚入
      const isTopScrollingIn = !isIntersecting && activeIndex.value === index;
      // 是否由底部滚入
      const isBottomScrollingOut = isIntersecting && activeIndex.value - 1 === index;
      if (isTopScrollingIn) {
        activeIndex.value += 1;
      } else if (isBottomScrollingOut) {
        activeIndex.value = index;
      }
    });
    return stop;
  });
}, 100);

/**
 * @description: 停止观察者行为
 */
const stopObserve = () => {
  stopArr.forEach(s => s());
  stopArr = [];
};

/**
 * @description: 滚动到对应模块
 */
const scrollToModule = (index: number) => {
  stopObserve();
  scrollRef.value.setScrollTop(listRef.value[index].offsetTop);
  activeIndex.value = index;
};

const onCheckAllPermission = (check: CheckboxValueType) => {
  const _check: boolean = check as boolean;
  state.menuTree = state.menuTree.map(module => {
    module.children = module.children.map(p => {
      p.checkAll = _check;
      p.isIndeterminate = check ? false : undefined;
      p.checkPermissions = _check ? p.children.map(x => x.id) : [];
      return p;
    });
    return module;
  });
  handleDetectionPermissionCheckAll();
};

const onCheckAllGroupPermissionChange = (val: CheckboxValueType, page: IMenuExt) => {
  page.checkPermissions = val ? page.children.map(x => x.id) : [];
  page.isIndeterminate = false;
  handleDetectionPermissionCheckAll();
};

const onCheckPermissionChange = (keys: Array<CheckboxValueType>, menu: IMenuExt) => {
  menu.checkAll = keys.length === menu.children.length;
  menu.isIndeterminate = keys.length > 0 && keys.length < menu.children.length;

  handleDetectionPermissionCheckAll();
};

const handleDetectionPermissionCheckAll = () => {
  let checkAllCount = 0;
  let permissionGroupCount = 0;
  let permissionKeyChecked = false;
  state.menuTree.forEach(item => {
    permissionGroupCount += item.children.length;
    checkAllCount += item.children.filter(x => x.checkAll).length;
    if (!permissionKeyChecked) {
      permissionKeyChecked = item.children.some(x => x.checkPermissions?.length);
    }
  });
  state.allCheckAll = checkAllCount > 0 && checkAllCount === permissionGroupCount;

  // 每个页面的checkbox没有权限
  if (checkAllCount <= 0 && permissionKeyChecked) {
    state.isIndeterminate = true;
  } else {
    state.isIndeterminate = !checkAllCount ? undefined : !(checkAllCount > 0 && checkAllCount === permissionGroupCount);
  }
};

const handleMenuChecked = async (roleMenus: Array<IMenu>) => {
  resetPermissionKey();
  if (!Array.isArray(roleMenus) || roleMenus.length === 0) {
    return;
  }
  state.menuTree = state.menuTree.map(module => {
    module.children = module.children.map(page => {
      const roleMenu: IMenu = roleMenus.find(x => x.id === page.id);
      if (roleMenu) {
        page.checkPermissions = roleMenus.filter(x => x.parentId === page.id)?.map(x => x.id);
        page.checkAll = page.checkPermissions.length === page.children.length;
        page.isIndeterminate = page.checkPermissions.length > 0 && page.checkPermissions.length < page.children.length;
      }
      return page;
    });

    return module;
  });
  handleDetectionPermissionCheckAll();
};

async function onSaveRoleMenu() {
  if (!props.roleId) {
    ElMessage.warning("请选择角色");
    return;
  }

  let menuIds: Array<string> = [];
  state.menuTree.forEach(m => {
    m.children.forEach(p => {
      if (Array.isArray(p.checkPermissions) && p.checkPermissions.length) {
        menuIds = menuIds.concat(p.checkPermissions).concat(p.id);
        if (!menuIds.includes(p.parentId)) {
          menuIds.push(p.parentId);
        }
      }
    });
  });
  await menuStore.editRoleMenu({ roleId: props.roleId, menuIds });
  ElMessage.success("更新成功");
}

const resetPermissionKey = () => {
  state.menuTree = state.menuTree.map(item => {
    item.children = item.children.map(child => {
      child.checkPermissions = [];
      child.checkAll = false;
      child.isIndeterminate = undefined;
      return child;
    });
    return item;
  });
  state.allCheckAll = false;
  state.isIndeterminate = undefined;
};
</script>

<style scoped lang="scss">
.modules-box {
  border-right: 1px solid var(--border-color-light);

  .module-function {
    padding: 5px 10px 10px;
    text-shadow: 0 0 0.5px currentColor;
  }

  .modules {
    .item {
      padding: 5px 10px;
      cursor: pointer;

      &:hover {
        cursor: pointer;
        color: var(--el-color-primary);
      }
    }

    .activate-module {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      border-right: 2px solid var(--el-color-primary);
    }
  }
}

.permission-box {
  .permission-group {
    margin-left: 25px;

    .el-checkbox-group {
      width: 100%;
    }
  }
}
</style>
