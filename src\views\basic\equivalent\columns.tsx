import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "产品分类",
      prop: "categoryName",
      slot: "categoryName"
    },
    {
      label: "型号",
      prop: "modelName"
    },
    {
      label: "规格",
      prop: "specification"
    },
    {
      label: "单位",
      prop: "unit",
      slot: "unit"
    },
    {
      label: "等效命名",
      prop: "equivalentName"
    },
    {
      label: "备注",
      prop: "remark"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char6
    }
  ];
  return { columns };
}
