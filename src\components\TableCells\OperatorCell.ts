import { VNode, h, withDirectives } from "vue";
import { ElButton, ButtonProps, ElTooltip } from "element-plus";
import { auth, track } from "@/directives";
import { DirectiveArguments } from "vue";

export interface ITableOperator {
  name: string;
  action: (event: MouseEvent) => void;
  props: Partial<ButtonProps>;
  permissionKey?: string | Array<string>;
  trackKey?: string;
  class?: string;
  tooltip?: string;
}

export const OperatorCell = (operators: Array<ITableOperator>) => {
  const actions = operators.map(operator => {
    const btnNode = h(
      ElButton,
      {
        key: Math.random(),
        link: true,
        onClick: operator.action,
        class: operator.class,
        ...operator.props
      },
      () => operator.name
    );
    let vNode: VNode;

    // 如果有tooltip，包裹tooltip VNode
    if (operator.tooltip) {
      vNode = h(
        ElTooltip,
        {
          content: operator.tooltip
        },
        btnNode
      );
    } else {
      vNode = btnNode;
    }

    // 添加指令
    const permissionKey = operator.permissionKey;
    const trackKey = operator.trackKey;
    const directives: DirectiveArguments = [];
    if (trackKey) directives.push([track, trackKey]);
    if (permissionKey) directives.push([auth, permissionKey]);
    directives.length && withDirectives(btnNode, directives);
    return vNode;
  });
  return h("div", actions);
};
