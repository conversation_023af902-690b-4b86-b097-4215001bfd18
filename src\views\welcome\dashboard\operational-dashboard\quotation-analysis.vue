<template>
  <div class="flex flex-col h-full p-5">
    <Headline title="报价单分析">
      <template #extra>
        <el-radio-group v-model="period" @change="onChange($event as string)">
          <el-radio-button label="day">日</el-radio-button>
          <el-radio-button label="month">月</el-radio-button>
        </el-radio-group>
      </template>
    </Headline>
    <ChartAnalysis class="flex-1" ref="chartAnalysisRef" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import ChartAnalysis from "./components/chart-analysis.vue";
import Headline from "./components/headline.vue";
import { IQuotationAnalysisReq } from "@/models";
import { getQuotationAnalysisList } from "@/api/dashboard";
import dayjs from "dayjs";
import { fullDateFormat } from "@/consts";
import { DateShowModeEnum } from "@/enums";

defineExpose({ handleGetQuotationAnalysisList });
const period = ref("day");
const chartAnalysisRef = ref<InstanceType<typeof ChartAnalysis>>();
const now = dayjs();
const params: IQuotationAnalysisReq = {};
onMounted(() => {
  onChange("day");
});

const onChange = (value: string) => {
  if (value === "month") {
    params.startDay = now.startOf("year").format(fullDateFormat);
    params.endDay = now.endOf("day").format(fullDateFormat);
    params.showMode = DateShowModeEnum.month;
  } else {
    params.startDay = now.subtract(7, "day").format(fullDateFormat);
    params.endDay = now.endOf("day").format(fullDateFormat);
    params.showMode = DateShowModeEnum.day;
  }

  handleGetQuotationAnalysisList();
};

async function handleGetQuotationAnalysisList() {
  let { data: list } = await getQuotationAnalysisList(params);

  const maxAmount = list.reduce((max, item) => Math.max(max, item.quotationAnalysisAmount), -Infinity);

  let _mode = "万";
  if (Math.abs(maxAmount) >= 100000000) {
    _mode = "亿";
  } else if (Math.abs(maxAmount) >= 10000000) {
    _mode = "千万";
  }

  switch (_mode) {
    case "万":
      list = list.map(item => ({
        ...item,
        quotationAnalysisAmount: Math.round((item.quotationAnalysisAmount / 10000 + Number.EPSILON) * 100) / 100
      }));
      break;
    case "千万":
      list = list.map(item => ({
        ...item,
        quotationAnalysisAmount: Math.round((item.quotationAnalysisAmount / 10000000 + Number.EPSILON) * 100) / 100
      }));
      break;
    case "亿":
      list = list.map(item => ({
        ...item,
        quotationAnalysisAmount: Math.round((item.quotationAnalysisAmount / 100000000 + Number.EPSILON) * 100) / 100
      }));
      break;

    default:
      break;
  }

  chartAnalysisRef.value.refreshChart(list, _mode);
}
</script>

<style lang="scss" scoped></style>
