<template>
  <div>
    <el-dialog
      v-model="modelValue"
      title="粘贴型号规格"
      top="8vh"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="90%"
    >
      <SpecificationGrid />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import SpecificationGrid from "./index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onSaveSuccess"): void;
}>();

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});
const loading = ref(false);
const handleSaveBtn = useLoadingFn(onSave, loading);

/**
 *  保存按钮点击事件
 */
async function onSave() {
  emits("onSaveSuccess");
}
function closeDialog() {
  modelValue.value = false;
}
</script>
