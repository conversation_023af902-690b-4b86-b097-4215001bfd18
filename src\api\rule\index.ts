import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IIRuleTest, IIRuleTestResult, IListResponse, IResponse, IRuleDefinition } from "@/models";
import { IRuleDefinitionReq } from "@/models";
import { RuleScopeEnum } from "@/enums";

/**  查规则定义 */
export const queryRuleDefinition = (data: IRuleDefinitionReq, ruleScope: RuleScopeEnum) => {
  const url: string = withApiGateway(`admin-api/business/ruleEngine/page?ruleScope=${ruleScope}`);
  return http.post<IRuleDefinitionReq, IListResponse<IRuleDefinition>>(url, { data });
};

/** 新增规则定义 */
export const createRuleDefinition = (data: IRuleDefinition, ruleScope: RuleScopeEnum) => {
  const url: string = withApiGateway(`admin-api/business/ruleEngine/create?ruleScope=${ruleScope}`);
  return http.post<IRuleDefinition, IRuleDefinition>(url, { data });
};

/** 更新规则定义 */
export const updateRuleDefinition = (data: IRuleDefinition, ruleScope: RuleScopeEnum) => {
  const url: string = withApiGateway(`admin-api/business/ruleEngine/update?ruleScope=${ruleScope}`);
  return http.put<IRuleDefinition, IRuleDefinition>(url, { data });
};

/** 删除规则定义 */
export const deleteRuleDefinition = (id: string, ruleScope: RuleScopeEnum) => {
  const url: string = withApiGateway(`admin-api/business/ruleEngine/delete?id=${id}&ruleScope=${ruleScope}`);
  return http.delete<IRuleDefinition, IRuleDefinition>(url);
};

/** 获取规则定义 根据Id */
export const getRuleDefinitionById = (id: string, ruleScope: RuleScopeEnum) => {
  const url: string = withApiGateway(`admin-api/business/ruleEngine/get?id=${id}&ruleScope=${ruleScope}`);
  return http.get<void, IResponse<IRuleDefinition>>(url);
};

/** 同步规则 */
export const refreshRuleEngine = (ruleScope: RuleScopeEnum) => {
  const url: string = withApiGateway(`admin-api/business/ruleEngine/refresh?ruleScope=${ruleScope}`);
  return http.post<any, IResponse<boolean>>(url, { data: {} });
};

/** 规则测试 */
export const testRuleEngine = (data: IIRuleTest, ruleScope: RuleScopeEnum) => {
  const url: string = withApiGateway(`admin-api/business/ruleEngine/testRule?ruleScope=${ruleScope}`);
  return http.post<IIRuleTest, IResponse<Array<IIRuleTestResult>>>(url, { data });
};
