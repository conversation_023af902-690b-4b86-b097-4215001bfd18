<template>
  <div class="background-flow-animation">
    <div v-for="n in particleCount" :key="n" class="flow-particle" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, computed } from "vue";
import { gsap } from "gsap";
import GSAPTimeline from "gsap";

const props = withDefaults(
  defineProps<{
    particleCount?: number;
    minSpread?: number;
    maxSpread?: number;
    minSize?: number;
    maxSize?: number;
    speed?: number;
  }>(),
  {
    particleCount: 100,
    minSpread: 100,
    maxSpread: 200,
    minSize: 1,
    maxSize: 8,
    speed: 4
  }
);

const particleCount = computed(() => {
  return Math.max(10, Math.min(props.particleCount, 300));
});

const animations: GSAPTimeline[] = [];

const randomRange = (min: number, max: number) => {
  return Math.random() * (max - min) + min;
};
onMounted(() => {
  const container = document.querySelector(".background-flow-animation") as HTMLDivElement;
  const particles = container.querySelectorAll(".flow-particle");

  // 计算精确中心点
  const containerRect = container.getBoundingClientRect();
  const centerX = (containerRect.width * 8) / 12;
  const centerY = (containerRect.height * 4) / 12;

  particles.forEach((particle, index) => {
    if (particle instanceof HTMLElement) {
      // 黄金角度分布
      const goldenAngle = Math.PI * (3 - Math.sqrt(5));
      const t = index / particleCount.value;

      // 随机扩散半径
      const spread = randomRange(props.minSpread, props.maxSpread);
      const radius = Math.sqrt(t) * spread;
      const angle = goldenAngle * index;

      // 起始位置
      const startX = centerX;
      const startY = centerY;

      // 目标位置计算
      const targetX = centerX + radius * Math.cos(angle);
      const targetY = centerY + radius * Math.sin(angle);

      // 随机粒子大小
      const particleSize = randomRange(props.minSize, props.maxSize);
      particle.style.width = `${particleSize}px`;
      particle.style.height = `${particleSize}px`;

      // 随机不透明度
      const opacity = 0.9 + Math.random() * 0.1;

      // 初始位置和透明度
      gsap.set(particle, {
        x: startX,
        y: startY,
        scale: 0.1,
        opacity: 0
      });

      // 计算调整后的速度
      const adjustedSpeed = props.speed;

      // 散开动画
      const startDelay = randomRange(0, 5); // 随机延迟启动

      const animation: GSAPTimeline = gsap
        .timeline({
          repeat: -1,
          repeatDelay: 0.5,
          delay: startDelay,
          onRepeat: () => {
            gsap.set(particle, {
              x: centerX,
              y: centerY,
              opacity: 0,
              scale: 0.1
            });
          }
        })
        .to(particle, {
          x: targetX,
          y: targetY,
          opacity: opacity,
          scale: 1,
          duration: adjustedSpeed,
          ease: "power1.in"
        })
        .to(particle, {
          opacity: 0,
          duration: 0,
          delay: 0,
          ease: "power1.in"
        });

      animations.push(animation);
    }
  });
});

onUnmounted(() => {
  animations.forEach(anim => anim.kill());
});
</script>

<style scoped>
.background-flow-animation {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
}

.flow-particle {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
}
</style>
