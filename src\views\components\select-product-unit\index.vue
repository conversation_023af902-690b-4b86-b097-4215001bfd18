<template>
  <el-select class="!w-full" v-model="modelValue" placeholder="请选择计量单位" :disabled="disabled">
    <el-option v-for="item in productUnitList" :key="item.id" :label="item.description" :value="item.code" />
  </el-select>
</template>

<script setup lang="ts">
import { queryAllProductUnit } from "@/api/quote-basic-data/product-unit";
import { IProductUnit } from "@/models/quote-basic-data";
import { computed, onMounted, ref } from "vue";

const emits = defineEmits<{
  (e: "update:modelValue", val?: string): void;
}>();

const productUnitList = ref<Array<IProductUnit>>([]);

const props = withDefaults(
  defineProps<{
    modelValue?: string;
    disabled?: boolean;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: string) {
    emits("update:modelValue", val);
  }
});

onMounted(async () => {
  const { data } = await queryAllProductUnit();
  productUnitList.value = data;
});
</script>

<style scoped lang="scss"></style>
