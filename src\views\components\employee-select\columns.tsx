import { SexEnum } from "@/enums";
import { IEmployee } from "@/models";
import { formatEnum } from "@/utils/format";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "",
      type: "selection"
    },
    {
      label: "用户名",
      prop: "username"
    },
    {
      label: "姓名",
      prop: "nickname"
    },
    {
      label: "手机号",
      prop: "mobile"
    },
    {
      label: "邮箱",
      prop: "email"
    },
    {
      label: "性别",
      prop: "sex",
      formatter: (row: IEmployee) => formatEnum(row.sex, SexEnum, "SexEnum")
    },
    {
      label: "状态",
      prop: "status",
      cellRenderer(data: TableColumnRenderer) {
        const status: boolean = data.row.status;
        return status ? (
          <CxTag size="small" type="danger">
            禁用
          </CxTag>
        ) : (
          <CxTag size="small" type="success">
            启用
          </CxTag>
        );
      }
      // formatter: (row: IEmployee) => formatEnum(row.status, StatusEnum, "StatusEnum")
    }
  ];
  return { columns };
}
