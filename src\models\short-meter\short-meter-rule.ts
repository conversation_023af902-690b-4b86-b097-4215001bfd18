import { ShortMeterRuleOperatorEnum } from "@/enums";
import { IBase } from "../i-base";

export interface IShortMeterRule extends IBase {
  /**
   * 结束比较符号 <为0 <=为1
   */
  maxCompareOp?: ShortMeterRuleOperatorEnum;
  /**
   * 结束米数
   */
  maxMeter?: number;
  /**
   * 起始比较符号 <为0 <=为1
   */
  minCompareOp?: ShortMeterRuleOperatorEnum;
  /**
   * 起始米数
   */
  minMeter?: number;
  /**
   * 单价浮动比例（百分比值，允许负值）
   */
  unitPriceFloatRatio?: number;
}
