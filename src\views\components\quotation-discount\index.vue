<template>
  <div class="flex flex-col gap-2">
    <div class="flex items-center discount-item">
      <span class="label"> 折扣1</span>
      <el-input v-model="state.discount1" placeholder="请输入折扣">
        <template #append>%</template>
      </el-input>
    </div>
    <div class="flex items-center discount-item">
      <span class="label"> 折扣2</span>
      <el-input v-model="state.discount2" placeholder="请输入折扣">
        <template #append>%</template>
      </el-input>
    </div>
    <div class="flex items-center discount-item">
      <span class="label"> 折扣3</span>
      <el-input v-model="state.discount3" placeholder="请输入折扣">
        <template #append>%</template>
      </el-input>
    </div>
    <div class="flex items-center discount-item">
      <span class="label"> 折扣4</span>
      <el-input v-model="state.discount4" placeholder="请输入折扣">
        <template #append>%</template>
      </el-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IQuotationInquirySetDiscount } from "@/models";
import { reactive } from "vue";

defineExpose({
  getdiscountValue
});

const state = reactive<IQuotationInquirySetDiscount>({});
function getdiscountValue() {
  return state;
}
</script>

<style scoped lang="scss">
.discount-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px 90px;
  gap: 56px;
  background-color: #fafafa;

  .label {
    font-size: 18px;
    color: #303133;
    white-space: nowrap;
  }
}
</style>
