<template>
  <div class="flex flex-col p-5 h-full">
    <Headline title="销售人员数据排行" />
    <div class="flex flex-col flex-1 overflow-hidden">
      <el-scrollbar>
        <div class="ranking-list">
          <div v-for="(item, index) in rankingData" :key="index" class="ranking-item">
            <div class="ranking-info">
              <div class="ranking-number" :class="getNumberClass(index)">
                <template v-if="index < 3">
                  <img :src="getRankingImage(index)" alt="ranking" class="ranking-medal" />
                </template>
                <template v-else>
                  {{ index + 1 }}
                </template>
              </div>
              <div class="ranking-name">{{ item.userName }}</div>
            </div>
            <div class="progress-bar">
              <div class="progress-inner" :style="{ width: getProgressWidth(item.count) }" />
            </div>
            <div class="ranking-account">
              <div class="ranking-count font-numeric">{{ item.count }} 单</div>
              <el-divider direction="vertical" />
              <div class="ranking-amount font-numeric">￥{{ formatNumber(item.amount) }}</div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import BronzeMedal from "@/assets/img/dashboard/bronze-medal.png"; // 铜牌
import GoldMedal from "@/assets/img/dashboard/gold-medal.png"; // 金牌
import SilverMedal from "@/assets/img/dashboard/silver-medal.png"; // 银牌
import { formatNumber } from "@/utils/format";
import { ref, watch } from "vue";
import Headline from "./components/headline.vue";
import { getSalespersonRankList } from "@/api/dashboard";
import { IDateRange } from "@/models";

defineExpose({ handleSalespersonRankList });

const rankingData = ref([]);

const props = withDefaults(
  defineProps<{
    dateRange: IDateRange;
  }>(),
  {}
);

watch(
  () => props.dateRange,
  dateRange => {
    if (dateRange) {
      handleSalespersonRankList(dateRange);
    }
  },
  { immediate: true }
);

// 获取排名图片
const getRankingImage = (index: number) => {
  const rankImages = [GoldMedal, SilverMedal, BronzeMedal];
  return rankImages[index];
};

// 获取排名样式类
const getNumberClass = (index: number) => {
  if (index < 3) return "rank-medal";
  return "rank-normal";
};

// 计算进度条宽度
const getProgressWidth = (count: number) => {
  const maxCount = Math.max(...rankingData.value.map(item => item.count));
  return `${(count / maxCount) * 100}%`;
};

async function handleSalespersonRankList(data: IDateRange) {
  const { data: list } = await getSalespersonRankList(data);
  rankingData.value = list;
}
</script>

<style lang="scss" scoped>
.ranking-list {
  padding: 0 10px;
  width: 100%;
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 6px 0;
}

.ranking-info {
  display: flex;
  align-items: center;
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-right: 20px;
  color: #ffffff;
}

.ranking-medal {
  width: 24px;
  height: 24px;
}

.rank-medal {
  display: flex;
  align-items: center;
  justify-content: center;
}

.rank-normal {
  color: var(--el-text-color-primary);
}

.ranking-name {
  font-size: 14px;
  color: var(--el-text-color-primary);
  width: 80px;
}

.ranking-account {
  display: flex;
  align-items: center;
}

.progress-bar {
  width: 280px;
  height: 6px;
  background-color: #f5f5f5;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 10px;
}

.progress-inner {
  height: 100%;
  background-color: var(--el-color-primary);
  border-radius: 3px;
}

.ranking-count {
  font-size: 14px;
  color: var(--el-color-primary);
  width: 60px;
  text-align: right;
}

.ranking-amount {
  font-size: 14px;
  color: var(--el-text-color-primary);
  width: 85px;
  text-align: right;
}
</style>
