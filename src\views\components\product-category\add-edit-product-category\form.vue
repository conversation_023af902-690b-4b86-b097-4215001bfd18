<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" clearable placeholder="请输入分类名称" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IProductCategoryForm } from "@/models";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});

const form = reactive<IProductCategoryForm>({});
const formRef = ref<FormInstance>();

const rules: FormRules = { name: [{ required: true, trigger: "change", message: "分类名称不能为空" }] };

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IProductCategoryForm) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}
</script>

<style scoped></style>
