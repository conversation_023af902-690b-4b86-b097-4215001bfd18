<template>
  <div>
    <el-row :gutter="4">
      <el-col :span="6">
        <el-input
          class="!h-8"
          type="number"
          clearable
          placeholder="空缺为无限小"
          :disabled="disabled"
          v-model="input.leftValue"
          @input="handleInput($event, 'leftValue')"
        >
          <template #suffix>
            <span v-if="isPercentage">%</span>
          </template>
        </el-input>
      </el-col>
      <el-col :span="4">
        <comparison-symbol-selector :disabled="disabled" v-model="input.leftSymbol" />
      </el-col>
      <el-col :span="4">
        <el-input class="!h-8" disabled clearable v-model="input.midValue" />
      </el-col>
      <el-col :span="4">
        <comparison-symbol-selector :disabled="disabled" v-model="input.rightSymbol" />
      </el-col>
      <el-col :span="6">
        <el-input
          class="!h-8"
          type="number"
          clearable
          placeholder="空缺为无限大"
          :disabled="disabled"
          v-model="input.rightValue"
          @input="handleInput($event, 'rightValue')"
        >
          <template #suffix>
            <span v-if="isPercentage">%</span>
          </template>
        </el-input>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from "vue";
import ComparisonSymbolSelector from "./comparison-symbol-selector.vue";
import { ComparisonSymbolEnum, InputFormualModelVal } from "./types";

/**
 * 公式输入组件
 */

const props = defineProps<{
  modelValue?: InputFormualModelVal;
  isPercentage?: boolean;
  disabled?: boolean;
}>();

const emits = defineEmits<{
  (e: "update:modelValue", value: InputFormualModelVal): void;
}>();

const maxLength = 20;

const input = reactive(
  Object.assign(
    {
      leftValue: "",
      leftSymbol: ComparisonSymbolEnum.LessThanValue as number,
      midValue: "",
      rightSymbol: ComparisonSymbolEnum.LessThanValue as number,
      rightValue: ""
    },
    props.modelValue
  )
);

/**
 * @description: 输入时矫正长度
 */
function handleInput(s: string, type: "leftValue" | "rightValue") {
  if (s.length > maxLength) {
    input[type] = s.slice(0, maxLength);
  }
}

watch(
  input,
  v => {
    emits("update:modelValue", v);
  },
  { deep: true }
);

watch(
  () => props.modelValue,
  v => {
    if (typeof v === "object") {
      Object.assign(input, v);
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
:deep(input::-webkit-inner-spin-button, input::-webkit-outer-spin-button) {
  -webkit-appearance: none !important;
  margin: 0;
}
</style>
