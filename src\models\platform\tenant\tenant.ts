import { StatusEnum } from "@/enums";

export interface ITenant {
  id?: string;
  /** 简称 */
  name: string;
  /** 企业名称 */
  comName: string;

  /** 联系人 */
  contactName: string;

  /** 联系手机 */
  contactMobile: string;

  /** 联系邮箱 */
  contactEmail: string;

  /** 状态 */
  status: StatusEnum;

  createTime: Date;

  /** 地址 */
  address: string;

  tokenLimitEnabled?: boolean;
  tokenLimitCount?: number;
  tokenUsedCount?: number;
}
