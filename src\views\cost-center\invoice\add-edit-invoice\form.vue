<template>
  <el-form ref="formRef" :model="form" label-position="top">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="订单编号"> 2025060134654</el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="类型">
          <CXTag>对公</CXTag>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="转账金额" prop="rechargeAmount">
          <el-input-number
            v-model="form.rechargeAmount"
            clearable
            :min="0"
            :controls="false"
            placeholder="请选择转账金额"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="转账时间" prop="rechargeTime">
          <el-date-picker v-model="form.rechargeTime" type="date" placeholder="请选择转账时间" />
        </el-form-item>
      </el-col>

      <el-col :span="6">
        <el-form-item label="转账时间" prop="rechargeTime">
          <el-date-picker v-model="form.rechargeTime" type="date" placeholder="请选择转账时间" />
        </el-form-item>
      </el-col>

      <el-col :span="6">
        <el-form-item label="付款方-开户名称" prop="rechargeTime">
          <el-input placeholder="请输入付款方-开户名称" />
        </el-form-item>
      </el-col>

      <el-col :span="6">
        <el-form-item label="付款方-开户行" prop="rechargeTime">
          <el-input placeholder="请输入付款方-开户行" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="付款方-银行帐号" prop="rechargeTime">
          <el-input placeholder="请输入付款方-银行帐号" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="转账凭证" prop="rechargeTime">
          <el-upload
            class="w-full"
            ref="uploadRef"
            drag
            :auto-upload="false"
            :multiple="true"
            :on-change="onChangeUploadFile"
            :on-remove="onRemoveFile"
          >
            <el-icon class="upload-icon" :size="48"><upload-filled class="text-5xl" /></el-icon>
            <div>将转账凭证拖到此处，或 <span class="text-primary">点击上传</span></div>
          </el-upload>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            placeholder="请输入备注"
            v-model="form.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, UploadFile, UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { IFile, IInvoiceForm } from "@/models";
import CXTag from "@/components/CxTag/index.vue";
import { uploadFile } from "@/api/upload-file";
import { UploadFilled } from "@element-plus/icons-vue";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});

const uploadRef = ref<UploadInstance>();
const form = reactive<IInvoiceForm>({});
const formRef = ref<FormInstance>();
let uploadRawFileArr: Array<UploadRawFile> = [];

const onChangeUploadFile: UploadProps["onChange"] = uploadFile => {
  uploadRawFileArr.push(uploadFile.raw);
};

const onRemoveFile: UploadProps["onRemove"] = (uploadFile: UploadFile) => {
  uploadRawFileArr = uploadRawFileArr.filter(x => x.uid !== uploadFile.uid);
};

/**  处理上传图片 */
async function onSelectUploadFile(file: File) {
  const formData = new FormData();
  formData.append("file", file);
  return await uploadFile(formData).then(res => res.data);
}

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IInvoiceForm) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
async function getFormValue() {
  const formValue = Object.assign({}, form);
  if (Array.isArray(uploadRawFileArr) && uploadRawFileArr.length) {
    const fileResult: Array<IFile> = await Promise.all(uploadRawFileArr.map(x => onSelectUploadFile(x)));
    formValue.certificateIds = fileResult.map(x => x.id);
  }
  return formValue;
}
</script>

<style scoped></style>
