import { LicenseEnum } from "@/enums";
export interface IBusinessLicense {
  isAuthorization: boolean;
  licenseRequestContent: string;
  authorization?: ILicense;
  errorMessage: string;
  supplierCode?: string;
  tenantId: string;
  tenantName: string;
}

export interface ILicense {
  expireDate: Date;
  companyName: string;
  edition: LicenseEnum;
  editionName: string;
  categoryCodes: Array<string>;
  categoryNames: Array<string>;
  supplierCodes?: Array<string>;
  workbenchFlag: boolean;
  inventoryFlag: boolean;
  permissionFlag: boolean;
  logRecordFlag: boolean;
  quickPlanSchedulingFlag: boolean;
  quickOrderCreateFlag: boolean;
  dataCheckFlag: boolean;
  oneKeySyncFlag: boolean;
  openApiFlag: boolean;
  authorizationDate: string;
  authorizationBy: string;
  featureList: Array<IFeatureItem>;
  remark: string;
  subCategoryCodes: Array<string>;
  subCategoryNames: Array<string>;
  accountNumber: number;
  tenantNumber: number;
  expireDays: number;
  deviceNumber: number;

  /** IOT EIP */
  integrationChannel: Array<string>;
}

export interface IFeatureItem {
  displayFlag: string;
  displayName: string;
  flag: boolean;
  name: string;
}
