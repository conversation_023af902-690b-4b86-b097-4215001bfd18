import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

/**
 * 动作类型枚举
 */
export enum ActionTypeEnum {
  /**
   * 不处理
   */
  NO_ACTION = 0,

  /**
   * 完整替换
   */
  REPLACE_ALL = 1,

  /**
   * 匹配替换
   */
  REPLACE_MATCH = 2,

  /**
   * 追加后缀
   */
  APPEND_SUFFIX = 3,

  /**
   * 追加前缀
   */
  PREPEND_PREFIX = 4,

  /**
   * 表达式
   */
  EXPRESSION = 5
}

/**
 * 动作类型的描述映射
 */
export const ActionTypeEnumMapDesc: Record<ActionTypeEnum, string> = {
  [ActionTypeEnum.NO_ACTION]: "不处理",
  [ActionTypeEnum.REPLACE_ALL]: "固定值替换（全量）",
  [ActionTypeEnum.REPLACE_MATCH]: "固定值替换（替换匹配部分）",
  [ActionTypeEnum.APPEND_SUFFIX]: "追加后缀",
  [ActionTypeEnum.PREPEND_PREFIX]: "追加前缀",
  [ActionTypeEnum.EXPRESSION]: "表达式"
};

export const ActionTypeOptions: Array<IOption> = mapDescToOptions<number>(ActionTypeEnumMapDesc);
