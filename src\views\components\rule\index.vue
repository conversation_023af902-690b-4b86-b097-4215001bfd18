<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1" @submit.prevent>
        <ElFormItem label="规则名称：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.ruleName" placeholder="请输入规则名称" />
        </ElFormItem>

        <ElFormItem label="状态：">
          <el-radio-group v-model="state.params.enabled" @change="onQuery()">
            <el-radio border :label="true">已启用</el-radio>
            <el-radio border :label="false">未启用</el-radio>
          </el-radio-group>
        </ElFormItem>

        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <div>
        <RuleDefinitionFormDialog mode="add" :ruleScope="props.ruleScope" @post-save-success="onQuery()">
          <template #trigger="{ openDialog }">
            <el-button class="mb-5" :icon="Plus" type="primary" @click="openDialog()">新增</el-button>
          </template>
        </RuleDefinitionFormDialog>
        <el-button class="mb-5 ml-2" type="primary" @click="onRefresh()">同步规则</el-button>
        <TestRuleDialog :ruleScope="props.ruleScope">
          <template #trigger="{ openDialog }">
            <el-button class="mb-5 ml-2" type="warning" @click="openDialog()">测试</el-button>
          </template>
        </TestRuleDialog>
      </div>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #priority="{ row }">
          {{ row.priority }}
        </template>
        <template #matchFieldStrategy="{ row }">
          {{ MatchFieldStrategyEnumMapDesc[row.matchFieldStrategy] }}
        </template>
        <template #displayStrategy="{ row }">
          {{ FinialDisplayStrategyEnumMapDesc[row.displayStrategy] }}
        </template>
        <template #displayStrategyVoltage="{ row }">
          {{ FinialDisplayStrategyEnumMapVoltageDesc[row.displayStrategyVoltage] }}
        </template>
        <template #enabled="{ row }">
          <CXTag :type="row.enabled ? 'success' : 'info'">{{ row.enabled ? "已启用" : "未启用" }}</CXTag>
        </template>
        <template #operation="data">
          <div>
            <RuleDefinitionFormDialog
              :ruleScope="props.ruleScope"
              mode="edit"
              :id="data.row.id"
              @post-save-success="onQuery()"
            >
              <template #trigger="{ openDialog }">
                <el-button type="primary" link @click="openDialog()">编辑</el-button>
              </template>
            </RuleDefinitionFormDialog>
            <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="inquiry-unit">
import { onMounted, ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IRuleDefinition, IRuleDefinitionReq } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import CXTag from "@/components/CxTag/index.vue";
import { deleteRuleDefinition, queryRuleDefinition, refreshRuleEngine } from "@/api/rule";
import RuleDefinitionFormDialog from "./rule-definition-form/dialog.vue";
import {
  RuleScopeEnum,
  MatchFieldStrategyEnumMapDesc,
  FinialDisplayStrategyEnumMapDesc,
  FinialDisplayStrategyEnumMapVoltageDesc
} from "@/enums";
import TestRuleDialog from "./test-rule/dialog.vue";

const props = withDefaults(
  defineProps<{
    ruleScope: RuleScopeEnum;
  }>(),
  {}
);

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IRuleDefinition>;
  params: IRuleDefinitionReq;
  id?: string;
}>({
  list: [],
  params: {}
});

onMounted(() => {
  requestList();
});

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteRuleDefinition(id, props.ruleScope);
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params: IRuleDefinitionReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    ruleName: ""
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryRuleDefinition(params, props.ruleScope);
  state.list = data.list;
  pagination.total = data.total;
}, loading);

const onRefresh = async () => {
  await refreshRuleEngine(props.ruleScope);
  ElMessage.success("同步规则成功");
};
</script>
