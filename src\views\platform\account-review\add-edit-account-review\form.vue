<template>
  <div>
    <div class="card">
      <el-descriptions class="info" :column="3">
        <el-descriptions-item class-name="value" label-class-name="label" label="姓名">{{
          accountReview?.username
        }}</el-descriptions-item>
        <el-descriptions-item class-name="value" label-class-name="label" label="手机号码">{{
          accountReview?.phone
        }}</el-descriptions-item>
        <el-descriptions-item class-name="value" label-class-name="label" label="所属企业">{{
          accountReview?.enterprise
        }}</el-descriptions-item>
        <el-descriptions-item class-name="value" label-class-name="label" label="所属部门">{{
          accountReview?.department
        }}</el-descriptions-item>
        <el-descriptions-item class-name="value" label-class-name="label" label="岗位">{{
          accountReview?.position
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" label-position="left">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="审核状态" prop="reviewStatus">
            <el-radio-group v-model="form.reviewStatus">
              <el-radio
                v-for="item in accountRegisterReviewStatusOptions"
                border
                :label="item.value"
                :key="item.value"
                >{{ item.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item
            label="所在企业"
            prop="tenantId"
            :rules="[{ required: tenantIdRequired, message: '请选择所在企业', trigger: ['change', 'blur'] }]"
          >
            <el-select class="w-full" v-model="form.tenantId" placeholder="请选择所在企业" clearable filterable>
              <el-option v-for="item in tenants || []" :key="item.id" :label="item.comName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="审核备注" prop="reviewRemark">
            <el-input type="textarea" :rows="2" v-model="form.reviewRemark" clearable placeholder="请输入审核备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IAccountReview, IAccountReviewForm, ITenant } from "@/models";
import { queryTenant } from "@/api/platform/tenant";
import { AccountRegisterReviewStatusEnum, AccountRegisterReviewStatusOptions, TenantStatusEnum } from "@/enums";
import { getAccountReviewById } from "@/api/platform/account-review";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});

const props = defineProps<{
  id?: string;
}>();

const accountRegisterReviewStatusOptions = AccountRegisterReviewStatusOptions.filter(
  x => x.value !== `${AccountRegisterReviewStatusEnum.PendingReview}`
);
const form = reactive<IAccountReviewForm>({
  reviewStatus: undefined
});
const formRef = ref<FormInstance>();
const accountReview = ref<IAccountReview>();
const tenants = ref<Array<ITenant>>([]);
const tenantIdRequired = ref(false);

const rules: FormRules = {
  reviewStatus: [{ required: true, trigger: "change", message: "审核状态不能为空" }]
};

onMounted(() => {
  if (props.id) {
    handleGetAccountReviewById(props.id);
  }
  handleQueryTenant();
});

watch(
  () => form.reviewStatus,
  reviewStatus => {
    form.tenantId = null;
    formRef.value.clearValidate(["tenantId"]);
    if (reviewStatus != AccountRegisterReviewStatusEnum.ReviewPassed) {
      tenantIdRequired.value = false;
      return;
    }
    tenantIdRequired.value = true;
  }
);
/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IAccountReviewForm) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

async function handleQueryTenant() {
  const { data } = await queryTenant({ status: TenantStatusEnum.ENABLE, pageSize: 100 });
  tenants.value = data.list;
}

async function handleGetAccountReviewById(id: string) {
  const { data } = await getAccountReviewById(id);
  accountReview.value = data;
}
</script>

<style scoped lang="scss">
.card {
  border: 1px solid #ebeef5;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;

  .info {
    :deep(.el-descriptions__body) {
      .el-descriptions__table {
        tbody {
          tr {
            td {
              .label {
                font-size: 14px;
                font-weight: normal;
                line-height: 22px;
                letter-spacing: 0;
                color: #303133;
                margin-right: 16px;
              }

              .value {
                font-size: 14px;
                line-height: 22px;
                letter-spacing: 0;
                color: #606266;
              }
            }
          }

          tr:nth-of-type(2) {
            padding: 16px 0;
            display: block;
          }
        }
      }
    }
  }
}
</style>
