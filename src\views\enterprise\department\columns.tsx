import { TableWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";
import { IEmployee } from "@/models";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "用户名",
      prop: "username"
    },
    {
      label: "姓名",
      prop: "nickname"
    },
    {
      label: "手机号",
      prop: "mobile"
    },
    {
      label: "邮箱",
      prop: "email"
    },
    {
      label: "在职状态",
      prop: "onJobStatus",
      width: TableWidth.type,
      formatter: (row: IEmployee) => (row.onJobStatus ? "在职" : "离职")
    },
    {
      label: "状态",
      prop: "status",
      cellRenderer(data: TableColumnRenderer) {
        const status: boolean = data.row.status;
        return status ? <CxTag type="danger">禁用</CxTag> : <CxTag type="success">启用</CxTag>;
      }
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.simpleOperation
    }
  ];
  return { columns };
}
