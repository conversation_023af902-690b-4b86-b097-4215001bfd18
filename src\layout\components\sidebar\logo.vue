<script setup lang="ts">
import { useNav } from "@/layout/hooks/useNav";
import { useEpThemeStoreHook } from "@/store/modules/epTheme";
import { computed } from "vue";
import Logo from "@/assets/svg/logo_collapse.svg?component";
import LogoCollapse from "@/assets/svg/logo_collapse.svg?component";
import LogoLight from "@/assets/svg/logo_collapse.svg?component";
import ELogo from "@/assets/svg/e_logo.svg?component";
import ELogoLightCollapse from "@/assets/svg/e_logo_collapse.svg?component";
import ELogoCollapse from "@/assets/svg/e_logo_light_collapse.svg?component";
import ELogoLight from "@/assets/svg/e_logo_light.svg?component";
import { useConfigStore } from "@/store/modules";

const props = defineProps({
  collapse: Boolean
});

const { title } = useNav();
const configStore = useConfigStore();
const theme = computed(() => useEpThemeStoreHook().epTheme);
const isLight = computed(() => theme.value === "light");
const expandLogoComponent = computed(() => {
  if (configStore.getIsNotEAccess) {
    return isLight.value ? Logo : LogoLight;
  }
  return isLight.value ? ELogo : ELogoLight;
});
const collapsedLogoComponent = computed(() => {
  if (configStore.getIsNotEAccess) {
    return LogoCollapse;
  }
  return isLight.value ? ELogoLightCollapse : ELogoCollapse;
});
const logoHeightClass = computed(() => (configStore.getIsNotEAccess ? "h-6" : "h-7"));
</script>

<template>
  <div class="sidebar-logo-container" :class="{ collapses: props.collapse }">
    <transition name="sidebarLogoFade">
      <router-link key="props.collapse" :title="title" class="sidebar-logo-link" to="/">
        <component :is="collapsedLogoComponent" v-show="props.collapse" :class="logoHeightClass" />
        <component :is="expandLogoComponent" v-show="!props.collapse" :class="logoHeightClass" />
        <span v-if="!props.collapse" class="sidebar-logo-text font-medium">AI报价软件</span>
      </router-link>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.sidebar-logo-container {
  width: 100%;
  height: 66px;
  overflow: hidden;
  position: relative;

  .sidebar-logo-link {
    height: 100%;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding-left: 13px;

    svg {
      flex-shrink: 0;
    }

    img {
      height: 28px;
      display: inline-block;
    }
  }

  .sidebar-logo-text {
    white-space: nowrap;
    font-size: 16px;
    color: #fff;
    margin-left: 4px;
  }

  &.collapses {
    .sidebar-logo-link {
      padding: 0 13px;
    }
  }
}
</style>
