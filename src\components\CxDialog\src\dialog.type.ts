import { AppContext } from "vue";
import { type Component, Ref, VNode } from "vue";
import { ElButton, ElDialog } from "element-plus";

type ElDialogType = InstanceType<typeof ElDialog>;
export type DialogSlot<T> = ({ instance }: { instance: T }) => Array<VNode>;
export type OnClickCallback<T> = (instance: T) => (false | void | {}) | Promise<false | void | {}>;

export interface DialogAction extends Partial<InstanceType<typeof ElButton>> {
  title: string;
}

export interface DialogOptions<T> extends Partial<Omit<ElDialogType, "modelValue" | "onUpdate:modelValue">> {
  content: string | VNode | Component;
  footer?: Array<DialogAction> | DialogSlot<T>;
  onOk?: OnClickCallback<T>;
  onCancel?: OnClickCallback<T>;
  class?: string;
}

export interface DialogInstance<T> {
  instanceRef: Ref<T>;
  close: () => void;
}

export interface Dialog {
  _context: AppContext;
  create<T = any>(options: DialogOptions<T>, appContext?: null | AppContext): DialogInstance<T>;
}
