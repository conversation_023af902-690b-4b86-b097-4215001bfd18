<template>
  <div class="text">
    <el-input
      :model-value="inputValue"
      :disabled="!!config?.disable"
      placeholder="请输入"
      @input="inputChange($event)"
      @blur="blurChange($event)"
    />
  </div>
</template>

<script setup lang="ts">
import { EControlType } from "@/enums";
import { trim } from "lodash-unified";

const props = defineProps({
  inputValue: {
    type: String,
    default: ""
  },
  config: {
    type: Object,
    default: () => ({})
  }
});

const emits = defineEmits(["valueChange"]);
/**
 * 控件值更新
 */
const inputChange = value => {
  emits("valueChange", {
    value: trim(value),
    key: EControlType.TextControl,
    config: props.config
  });
};

/**
 * 失焦事件
 */
const blurChange = (ev: FocusEvent) => {
  const value = ev.target["value"] || null;
  emits("valueChange", {
    value,
    key: EControlType.TextControl,
    config: props.config
  });
};
</script>

<style scoped lang="scss"></style>
