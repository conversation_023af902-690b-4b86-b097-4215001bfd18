export enum QuotationInquiryParseEnum {
  /**
   * WEB端
   */
  WEB = 0,

  /**
   * 小程序
   */
  MINI_PROGRAM = 1,

  /**
   * APP
   */
  APP = 2
}

/**
 * 客户端类型描述映射
 */
export const QuotationInquiryParseEnumMapDesc: Record<QuotationInquiryParseEnum, string> = {
  [QuotationInquiryParseEnum.WEB]: "WEB端",
  [QuotationInquiryParseEnum.MINI_PROGRAM]: "小程序",
  [QuotationInquiryParseEnum.APP]: "APP"
};

/**
 * 客户端类型颜色映射
 */
export const QuotationInquiryParseEnumMapColor: Record<QuotationInquiryParseEnum, string> = {
  [QuotationInquiryParseEnum.WEB]: "blue",
  [QuotationInquiryParseEnum.MINI_PROGRAM]: "green",
  [QuotationInquiryParseEnum.APP]: "purple"
};
