<template>
  <div>
    <div>
      <div class="mb-2">输入</div>
      <el-input
        v-model="state.rule"
        :autosize="{ minRows: 4, maxRows: 6 }"
        type="textarea"
        placeholder="请输入一条原始询价单的文本内容用于规则验证"
      />
    </div>
    <div class="text-right mt-4">
      <!-- <el-checkbox v-model="state.containUnEnable">包含未启用的规则</el-checkbox> -->
      <el-button class="ml-4" type="primary" :loading="loading" @click="handleonExecuteTest()">执行测试</el-button>
    </div>
    <div>
      <div class="mb-2">验证输出：</div>
      <div class="flex flex-col gap-2">
        <el-card v-for="(item, index) in state.testResult || []" :key="index">
          <div v-for="(result, index) in item.debugList" :key="index">{{ result }}</div>
          <div class="flex mt-2">
            <div>匹配到规则：</div>
            <div class="flex flex-wrap">
              <el-button
                v-for="rule in item.matchedRules"
                :key="rule"
                type="primary"
                link
                @click="onShowRuleForm(rule)"
              >
                {{ rule }}
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <RuleDefinitionFormDialog
      ref="ruleDefinitionFormDialogRef"
      :ruleScope="props.ruleScope"
      mode="edit"
      :id="state.ruleId"
    />
  </div>
</template>

<script setup lang="ts">
import { testRuleEngine } from "@/api/rule";
import { RuleScopeEnum } from "@/enums";
import { IIRuleTestResult } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";
import { reactive, ref } from "vue";
import RuleDefinitionFormDialog from "../rule-definition-form/dialog.vue";
import { nextTick } from "vue";

const props = defineProps<{
  ruleScope: RuleScopeEnum;
}>();

const loading = ref();
const ruleDefinitionFormDialogRef = ref<InstanceType<typeof RuleDefinitionFormDialog>>();
const state = reactive<{
  rule?: string;
  containUnEnable?: boolean;
  result?: string;
  testResult: Array<IIRuleTestResult>;
  ruleId?: string;
}>({
  testResult: []
});

const handleonExecuteTest = useLoadingFn(onExecuteTest, loading);

async function onExecuteTest() {
  state.testResult = [];
  if (!state.rule) {
    ElMessage.warning("请输入规则");
    return;
  }
  const { data } = await testRuleEngine({ rawContent: state.rule }, props.ruleScope);
  if (!Array.isArray(data) || data.length === 0) {
    state.testResult = [];
    ElMessage.warning("未匹配到规则");
    return;
  }
  state.testResult = data;
}

const onShowRuleForm = (ruleId: string) => {
  state.ruleId = ruleId;
  nextTick(() => {
    ruleDefinitionFormDialogRef.value.openDialog();
  });
};
</script>

<style scoped lang="scss"></style>
