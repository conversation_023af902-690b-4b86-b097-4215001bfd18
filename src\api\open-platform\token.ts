import {
  IListResponse,
  IOpenPlatformToken,
  IOpenPlatformTokenForm,
  IOpenPlatformTokenReq,
  IOpenPlatformTokenScope,
  IResponse
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

export const queryTokens = (params: IOpenPlatformTokenReq) => {
  return http.get<IOpenPlatformTokenReq, IListResponse<IOpenPlatformToken>>(
    withApiGateway("admin-api/system/oauth2-client/page"),
    { params }
  );
};

export const queryTokenScopes = () => {
  return http.get<void, IResponse<Array<IOpenPlatformTokenScope>>>(
    withApiGateway("admin-api/system/oauth2-client/scopes")
  );
};

export const addToken = (data: IOpenPlatformTokenForm) => {
  return http.post<IOpenPlatformTokenForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/oauth2-client/create"),
    { data },
    { showErrorInDialog: true }
  );
};

export const generateToken = () => {
  return http.get<void, IResponse<string>>(withApiGateway("admin-api/system/oauth2-client/secret"), null, {
    showErrorInDialog: true
  });
};

export const editToken = (data: IOpenPlatformTokenForm) => {
  return http.put<IOpenPlatformTokenForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/oauth2-client/update"),
    { data },
    { showErrorInDialog: true }
  );
};

export const deleteToken = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway("admin-api/system/oauth2-client/delete"), {
    params: { id }
  });
};

export const getTokenDetailById = (id: string) => {
  return http.get<string, IResponse<IOpenPlatformToken>>(withApiGateway("admin-api/system/oauth2-client/get"), {
    params: { id }
  });
};
