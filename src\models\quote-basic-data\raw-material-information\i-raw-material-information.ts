import { IBase, IRawMaterialInformationCost } from "@/models";

export interface IRawMaterialInformation extends IBase {
  /**
   * 类型
   */
  type?: string;
  /**
   * 物料编码
   */
  materialCode?: string;
  /**
   * 原材料名称
   */
  rawName?: string;
  /**
   * 原材料型号
   */
  rawModel?: string;
  /**
   * 原材料规格
   */
  rawSpec?: string;

  /** 状态 ，true 启用，false  禁用 */
  status?: boolean;

  latestCost?: IRawMaterialInformationCost;
}
