<template>
  <div class="cx-play-wrap" id="cx-player" />
</template>

<script setup lang="ts">
import { useEventListener } from "@vueuse/core";
import { onBeforeUnmount, onMounted, ref } from "vue";
import Xgplayer, { Events } from "xgplayer";
// import { HlsPlugin } from "xgplayer-hls";
import "xgplayer/dist/index.min.css";

const player = ref<Xgplayer>();

defineExpose({ player });

const props = withDefaults(
  defineProps<{
    lang?: string;
    fluid?: boolean;
    url?: string;
    poster?: string;
    marginControls?: boolean;
    height?: number;
    width?: number;
    volume?: number;
    isLive?: boolean;
    onError?: (err: any) => void;
    autoplay: boolean;
  }>(),
  {
    lang: "zh-cn",
    fluid: true,
    marginControls: false,
    volume: 0,
    isLive: false,
    autoplay: true
  }
);

const emits = defineEmits<{
  (event: "errorHandling", err: any, player: Xgplayer): void;
}>();

onMounted(() => {
  player.value = new Xgplayer({
    id: "cx-player",
    //  plugins: [HlsPlugin],
    ...props
  });

  // 播放器错误监听
  player.value.on(Events.ERROR, error => {
    emits("errorHandling", error, player.value);
  });

  useEventListener(document, "contextmenu", e => {
    e.preventDefault();
  });
});

onBeforeUnmount(() => {
  player?.value.destroy();
  player.value = null;
});
</script>

<style>
.cx-play-wrap .xgplayer-controls {
  background: linear-gradient(0deg, #000 0%, rgb(0 0 0 / 0%) 100%);
}
</style>
