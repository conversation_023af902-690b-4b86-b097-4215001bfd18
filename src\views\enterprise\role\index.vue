<template>
  <div class="flex h-full py-5 px-6 gap-5">
    <div class="w-[280px] bg-bg_color p-5 overflow-hidden">
      <div class="flex-bc add-role">
        <ElInput v-model="filterText" placeholder="输入角色名称" clearable>
          <template #prefix>
            <el-icon class="el-input__icon"><Search /></el-icon>
          </template>
        </ElInput>
        <el-button v-auth="PermissionKey.enterprise.enterpriseRoleCreate" @click="onAddRole()" class="add-button">
          <el-icon><Plus /></el-icon>
        </el-button>
      </div>
      <el-scrollbar max-height="calc(100% - 40px)">
        <ElTree
          node-key="id"
          ref="treeRef"
          highlight-current
          :props="treeProps"
          :current-node-key="state.defaultCheckedCurrentNodeKey"
          :default-expanded-keys="state.treeExpandedKeys"
          :expand-on-click-node="false"
          :data="roleStore.roles"
          @current-change="onCurrentChange"
          @node-expand="onNodeExpand"
          @node-collapse="onNodeCollapse"
          :filter-node-method="filterNode"
        >
          <template #default="{ data }">
            <div :class="['dept-custom-tree-node', { 'activate-operation': data.id === roleState.activateRoleId }]">
              <div class="name">{{ data.name }}</div>

              <el-popover
                placement="bottom"
                trigger="hover"
                @show="onRoleOperationShow(data)"
                @hide="onRoleOperationHide()"
                v-if="data.type === RoleTypeEnum.custom"
              >
                <div class="flex flex-col">
                  <div v-auth="PermissionKey.enterprise.enterpriseRoleEdit" class="flex items-center item-action mb-2">
                    <el-icon><EditPen /></el-icon>
                    <div class="ml-2" @click.stop="onEditRole(data)">编辑</div>
                  </div>
                  <div
                    v-auth="PermissionKey.enterprise.enterpriseRoleDelete"
                    class="flex items-center item-action item-action-del"
                  >
                    <el-icon><Delete /></el-icon>
                    <div class="ml-2" @click.stop="onDeleteRole(data)">删除</div>
                  </div>
                </div>
                <template #reference>
                  <ElIcon
                    class="edit-icon"
                    v-auth="[
                      PermissionKey.enterprise.enterpriseRoleEdit,
                      PermissionKey.enterprise.enterpriseRoleDelete
                    ]"
                  >
                    <MoreFilled />
                  </ElIcon>
                </template>
              </el-popover>
            </div>
          </template>
        </ElTree>
      </el-scrollbar>
      <div class="roles">
        <div class="item" />
      </div>
    </div>
    <div class="bg-bg_color px-5 pt-3 pb-5 flex-1 overflow-hidden">
      <el-tabs v-model="state.activeTab" class="h-full flex flex-col role-tabs">
        <el-tab-pane label="角色员工" name="employee" class="flex flex-col h-full">
          <div class="mb-3 text-right">
            <ElButton
              v-auth="PermissionKey.enterprise.enterpriseRoleUserCreate"
              type="primary"
              @click="onBindEmployeeToRole()"
              :icon="Plus"
              >选择员工</ElButton
            >
          </div>
          <PureTable
            class="flex-1 overflow-hidden pagination"
            row-key="id"
            size="large"
            :data="employeeStore.employees"
            :columns="columns"
            :pagination="pagination"
            showOverflowTooltip
            :loading="employeeStore.loading"
            @page-size-change="onPageSizeChange()"
            @page-current-change="onPageCurrentPage()"
          >
            <template #operation="data">
              <ElButton
                link
                type="danger"
                v-auth="PermissionKey.enterprise.enterpriseRoleUserRemove"
                @click="onRemoveDepartmentEmployee(data.row)"
                >移除</ElButton
              >
            </template>
            <template #empty>
              <CxEmpty />
            </template>
          </PureTable>
        </el-tab-pane>
        <Auth :value="PermissionKey.enterprise.enterpriseRolePermissionView">
          <el-tab-pane label="角色权限" name="permission" class="h-full">
            <Permission :role-id="roleState.activateRole?.id" />
          </el-tab-pane>
        </Auth>
      </el-tabs>
    </div>
  </div>
  <ElDialog
    title="选择员工"
    align-center
    class="middle"
    destroy-on-close
    v-model="employeeState.modalVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onCloseSelectEmployeeModal()"
  >
    <EmployeeSelect
      ref="employeeSelectRef"
      @onSelectEmployee="handleSelectEmployee($event)"
      :params="state.queryEmployeeParams"
    />
    <template #footer>
      <el-button @click="onCancelSelectEmployeeModal()">取消</el-button>
      <el-button
        type="primary"
        :loading="saveBindEmployeeLoading"
        :disabled="state.selectedEmployees?.length === 0"
        @click="handleBindEmployee()"
        >保存</el-button
      >
    </template>
  </ElDialog>

  <ElDialog
    :title="roleModalTitle"
    align-center
    destroy-on-close
    class="small"
    v-model="roleState.roleModalVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <RoleForm ref="roleFormRef" />

    <template #footer>
      <el-button @click="onCancelAddRoleModal()">取消</el-button>
      <el-button type="primary" :loading="saveDepartmentLoading" @click="handleSaveRole()">保存</el-button>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
import { ElButton, ElTree, ElInput, ElIcon, ElMessage } from "element-plus";
import { useColumns } from "./columns";
import CxEmpty from "@/components/CxEmpty";
import { computed, reactive, ref, watch, onBeforeMount } from "vue";
import { MoreFilled, EditPen, Search, Plus, Delete } from "@element-plus/icons-vue";
import EmployeeSelect from "@/views/components/employee-select/index.vue";
import { useEmployeeStore, useRoleStore } from "@/store/modules";
import RoleForm from "./role-form.vue";
import { IEmployee, IEmployeeReq, IRole, IRoleForm } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useEmployeeHook, useRoleHook } from "./hooks";
import { useConfirm } from "@/utils/useConfirm";
import { RoleTypeEnum } from "@/enums";
import Permission from "./permission/index.vue";
import { PermissionKey } from "@/consts";
import { useTableConfig } from "@/utils/useTableConfig";

usePageStoreHook().setTitle((useRoute().meta?.title as string) || "角色管理");

const {
  employeeState,
  deleteRoleEmployee,
  bindEmployeeToRole,
  onCloseSelectEmployeeModal,
  onBindEmployeeToRoleModalVis
} = useEmployeeHook();

const {
  roleState,
  onConfirmSaveRole,
  onCancelAddRoleModal,
  onDeleteRole,
  onAddRole,
  onEditRole,
  onChooseRole,
  onRoleOperationShow,
  onRoleOperationHide
} = useRoleHook();

const employeeStore = useEmployeeStore();
const roleStore = useRoleStore();
const { columns } = useColumns();
const { pagination } = useTableConfig();
pagination.hideOnSinglePage = true;
pagination.pageSize = 100;
const treeProps = { label: "name" };

const state = reactive<{
  selectedEmployees: Array<IEmployee>;
  queryEmployeeParams: IEmployeeReq;
  treeExpandedKeys: Array<string>;
  activeTab: "employee" | "permission";
  defaultCheckedCurrentNodeKey: string;
}>({
  selectedEmployees: [],
  queryEmployeeParams: {
    excludeRole: true
  },
  treeExpandedKeys: [],
  activeTab: "employee",
  defaultCheckedCurrentNodeKey: undefined
});

roleStore.queryRoleList();
const filterText = ref("");

const employeeSelectRef = ref<InstanceType<typeof EmployeeSelect>>();

const saveBindEmployeeLoading = ref<boolean>(false);
const roleFormRef = ref<InstanceType<typeof RoleForm>>();
const saveDepartmentLoading = ref<boolean>(false);

const handleSaveRole = useLoadingFn(onSaveRole, saveDepartmentLoading);
const handleBindEmployee = useLoadingFn(onConfirmBindEmployee, saveBindEmployeeLoading);

const treeRef = ref<InstanceType<typeof ElTree>>();

watch(filterText, val => {
  treeRef.value!.filter(val);
});

watch(
  () => roleState.activateRole?.id,
  roleId => {
    if (roleState.activateRole?.id) {
      pagination.currentPage = 1;
      employeeStore.queryEmployee({ roleId, pageSize: pagination.pageSize, pageNo: pagination.currentPage });
    }
  }
);

watch(
  () => roleStore.roles,
  () => {
    if (roleState.activateRole?.id) {
      return;
    }

    if (!Array.isArray(roleStore.roles) || roleStore.roles.length === 0) {
      return;
    }
    onChooseRole(roleStore.roles[0]);
    state.defaultCheckedCurrentNodeKey = roleStore.roles[0].id;
  },
  { immediate: true }
);

watch(
  () => employeeStore.total,
  total => {
    pagination.total = total;
  }
);

onBeforeMount(() => {
  roleState.activateRole = undefined;
});

const roleModalTitle = computed(() => (roleState.activateRole?.id ? "编辑角色" : "新增角色"));

const filterNode = (value: string, data: IRole) => {
  if (!value) return true;
  return data.name?.includes(value);
};

const onCurrentChange = (role: IRole) => {
  onChooseRole(role);
};

const onNodeExpand = (data: IRole) => {
  if (!state.treeExpandedKeys.includes(data.id)) {
    state.treeExpandedKeys.push(data.id);
  }
};
const onNodeCollapse = (data: IRole) => {
  state.treeExpandedKeys = state.treeExpandedKeys.filter(x => x !== data.id);
};

const onCancelSelectEmployeeModal = () => {
  state.selectedEmployees = [];
  onCloseSelectEmployeeModal();
};

const onBindEmployeeToRole = () => {
  if (!roleState.activateRole || Object.keys(roleState.activateRole).length === 0) {
    ElMessage.warning("请选择角色");
    return;
  }

  state.queryEmployeeParams.roleId = roleState.activateRole.id;
  onBindEmployeeToRoleModalVis();
};

const onRemoveDepartmentEmployee = async (employee: IEmployee) => {
  if (!roleState.activateRole || Object.keys(roleState.activateRole).length === 0) {
    ElMessage.warning("请选择角色");
    return;
  }

  if (!(await useConfirm("确认移除后，数据将无法恢复", "确认移除"))) {
    return;
  }
  await deleteRoleEmployee(roleState.activateRole.id, employee.id);
  pagination.currentPage = 1;
  employeeStore.queryEmployee(filterParams());
};

const handleSelectEmployee = (employees: Array<IEmployee>) => {
  state.selectedEmployees = employees;
};

async function onConfirmBindEmployee() {
  const selectedEmployeeIds: Array<string> = state.selectedEmployees?.map(x => x.id);
  if (!Array.isArray(selectedEmployeeIds) || selectedEmployeeIds.length === 0) {
    return;
  }
  await bindEmployeeToRole(roleState.activateRole.id, selectedEmployeeIds);
  pagination.currentPage = 1;
  employeeStore.queryEmployee(filterParams());
}

async function onSaveRole() {
  const role: IRoleForm | false = await roleFormRef.value.getValidValue().catch(() => false);
  if (!role) {
    return;
  }
  filterText.value = "";
  await onConfirmSaveRole(role);
  treeRef.value.setCurrentKey(roleState.activateRole?.id);
}

const onPageSizeChange = () => {
  employeeStore.queryEmployee(filterParams());
};

const onPageCurrentPage = () => {
  employeeStore.queryEmployee(filterParams());
};

const filterParams = () => {
  return {
    roleId: roleState.activateRole.id,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };
};
</script>

<style scoped lang="scss">
.add-role {
  padding-bottom: 12px;

  .add-button {
    margin-left: 4px;
    padding: 5px 8px;
  }
}

.dept-custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  width: 100%;
  line-height: 2;
  overflow: hidden;

  .edit-icon {
    visibility: hidden;
    position: absolute;
    z-index: 2;
    right: 8px;
  }

  &:hover {
    .edit-icon {
      visibility: visible;
      color: var(--el-color-primary);
    }
  }

  &.activate-operation {
    .edit-icon {
      visibility: visible;
      color: var(--el-color-primary);
    }
  }
}

.item-action {
  &:hover {
    cursor: pointer;
    color: var(--el-color-primary);
  }

  &.item-action-del {
    color: var(--el-color-danger);
  }
}

.role-tabs {
  :deep(.el-tabs__content) {
    flex: 1;
  }
}

.el-tree-node__content {
  padding: 8px 0;
}

:deep(.el-tree-node.is-current .name) {
  color: var(--el-color-primary);
}

:deep(.el-tree-node__content) {
  height: 28px;
}

:deep(.el-tree-node__expand-icon) {
  padding: 0;
}
</style>
