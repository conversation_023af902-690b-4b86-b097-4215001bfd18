<template>
  <div class="word-cloud-container" ref="cloudContainer" :style="{ height: `${containerHeight}px` }">
    <div class="word-cloud-wrapper" ref="wordCloudWrapper">
      <div
        v-for="word in displayedWords"
        :key="word.id"
        class="word-item"
        :style="{
          transform: `translate(${word.left}px, ${word.top}px)`,
          opacity: word.opacity,
          fontSize: `${word.fontSize}px`,
          transition: 'opacity 0.5s ease, transform 0.5s ease',
          width: `${word.width}px` // 确保宽度也应用
        }"
      >
        {{ word.text }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import { v4 as uuidv4 } from "uuid";
import { usePurchaseOrderSyncStore } from "@/store/modules/quotation-order-sync";

// 定义接口
interface WordCloudConfig {
  wordCount: number;
  columnCount: number;
  scrollSpeed: number;
  wordList?: string[]; // 允许传入自定义词语列表
  minFontSize?: number;
  maxFontSize?: number;
  wordPadding?: number;
  horizontalOffsetRange?: number;
  opacityInactive?: number;
  opacityFaded?: number;
  opacityActive?: number;
  fadeInDuration?: number;
}

interface WordItem {
  id: string;
  text: string;
  opacity: number;
  width: number;
  height: number;
  left: number;
  top: number;
  fontSize: number;
}

const props = withDefaults(
  defineProps<{
    config?: WordCloudConfig;
  }>(),
  {
    config: () => ({
      wordCount: 100,
      columnCount: 4,
      scrollSpeed: 1,
      wordList: [
        "ZC-YJV22-8.7/10kV 340 m",
        "WDZB1-YJV23-0.6/1kV 446 m",
        "YJV-0.6/1kV 145 m",
        "ZC-KVVP2-22-450/750V 145 m",
        "ZC-YJV22-0.6/1kV 2358 m"
      ],
      minFontSize: 12,
      maxFontSize: 16,
      wordPadding: 15,
      horizontalOffsetRange: 20,
      opacityInactive: 0,
      opacityFaded: 0.5,
      opacityActive: 1,
      fadeInDuration: 50
    })
  }
);
const store = usePurchaseOrderSyncStore();
const cloudContainer = ref<HTMLDivElement | null>(null);
const wordCloudWrapper = ref<HTMLDivElement | null>(null);
const allWords = ref<WordItem[]>([]);
const displayedWords = ref<WordItem[]>([]);
const containerHeight = ref(200);
const isAnimationPaused = ref(false);
const scrollPosition = ref(0);

let animationFrameId: number | null = null;
let scrollTimeout: number | null = null;

const generatedWords = computed(() => {
  if (props.config.wordList && props.config.wordList.length > 0) {
    const repeatedWords: string[] = [];
    while (repeatedWords.length < props.config.wordCount) {
      repeatedWords.push(...props.config.wordList);
    }
    return repeatedWords.slice(0, props.config.wordCount);
  } else {
    // Fallback to your original word generation if no wordList is provided
    const baseWords = [
      "ZC-YJV22-8.7/10kV 340 m",
      "WDZB1-YJV23-0.6/1kV 446 m",
      "YJV-0.6/1kV 145 m",
      "ZC-KVVP2-22-450/750V 145 m",
      "ZC-YJV22-0.6/1kV 2358 m"
    ];
    const words: string[] = [];
    while (words.length < props.config.wordCount) {
      words.push(...baseWords);
    }
    return words.slice(0, props.config.wordCount);
  }
});

const applyWaterfallLayout = () => {
  if (!cloudContainer.value || !wordCloudWrapper.value) return;

  const containerWidth = cloudContainer.value.clientWidth - 200;
  const columnCount = props.config.columnCount;
  const columnWidth = containerWidth / columnCount;
  const columnHeights = new Array(columnCount).fill(0);
  const newWords: WordItem[] = [];

  generatedWords.value.forEach((text, _index) => {
    const columnIndex = columnHeights.indexOf(Math.min(...columnHeights));
    const fontSize =
      Math.floor(Math.random() * (props.config.maxFontSize! - props.config.minFontSize! + 1)) +
      props.config.minFontSize!;

    const tempSpan = document.createElement("span");
    tempSpan.style.visibility = "hidden";
    tempSpan.style.position = "absolute";
    tempSpan.style.fontSize = `${fontSize}px`;
    tempSpan.textContent = text;
    document.body.appendChild(tempSpan);
    const textWidth = tempSpan.offsetWidth;
    const textHeight = tempSpan.offsetHeight;
    document.body.removeChild(tempSpan);

    const horizontalOffset = (
      Math.random() * props.config.horizontalOffsetRange! * 2 -
      props.config.horizontalOffsetRange!
    ).toFixed(2);

    const baseLeft = columnIndex * columnWidth;
    const left = baseLeft + (columnWidth - textWidth) / 2 + parseFloat(horizontalOffset);
    const _top = columnHeights[(columnIndex + columnCount) % columnCount]; // 避免在一列中堆积

    newWords.push({
      id: uuidv4(),
      text,
      opacity: props.config.opacityInactive!,
      width: textWidth,
      height: textHeight,
      left,
      top: columnHeights[(columnIndex + columnCount) % columnCount],
      fontSize
    });

    columnHeights[(columnIndex + columnCount) % columnCount] +=
      textHeight + props.config.wordPadding! + Math.random() * 5;
  });

  allWords.value = newWords;
  wordCloudWrapper.value.style.height = `${Math.max(...columnHeights)}px`;

  // Initial fade-in
  displayedWords.value = allWords.value.map((word, _index) => ({
    ...word,
    opacity: 0
  }));
  displayedWords.value.forEach((word, index) => {
    setTimeout(() => {
      word.opacity = props.config.opacityFaded!;
    }, index * props.config.fadeInDuration!);
  });

  // Reset scroll position after layout
  scrollPosition.value = 0;
  if (cloudContainer.value) {
    cloudContainer.value.scrollTop = 0;
  }
};

const updateWordOpacities = () => {
  if (!cloudContainer.value) return;

  const containerTop = scrollPosition.value;
  const containerHeight = cloudContainer.value.clientHeight;
  const containerBottom = containerTop + containerHeight;
  const middleStart = containerTop + containerHeight * 0.25;
  const middleEnd = containerTop + containerHeight * 0.75;

  displayedWords.value.forEach(word => {
    const elTop = word.top;
    const elBottom = elTop + word.height;

    if (elTop >= containerTop && elBottom <= containerBottom) {
      if (elTop >= middleStart && elBottom <= middleEnd) {
        word.opacity = props.config.opacityActive!;
      } else {
        word.opacity = props.config.opacityFaded!;
      }
    } else {
      word.opacity = props.config.opacityInactive!;
    }
  });
};

const animateScroll = () => {
  if (isAnimationPaused.value || !cloudContainer.value || !wordCloudWrapper.value || !store.dialogVisible) {
    if (animationFrameId) cancelAnimationFrame(animationFrameId);
    return;
  }

  scrollPosition.value += props.config.scrollSpeed!;
  cloudContainer.value.scrollTop = scrollPosition.value;
  updateWordOpacities();

  if (scrollPosition.value >= wordCloudWrapper.value.clientHeight - cloudContainer.value.clientHeight) {
    applyWaterfallLayout();
    scrollPosition.value = 0;
    cloudContainer.value.scrollTop = 0;
    // Restart animation after a short delay to avoid immediate relayout
    setTimeout(() => {
      animationFrameId = requestAnimationFrame(animateScroll);
    }, 500);
  } else {
    animationFrameId = requestAnimationFrame(animateScroll);
  }
};

const startAnimation = () => {
  isAnimationPaused.value = false;
  if (!animationFrameId) {
    // Delay start slightly to ensure layout is done
    scrollTimeout = window.setTimeout(() => {
      animationFrameId = requestAnimationFrame(animateScroll);
    }, 500);
  }
};

const stopAnimation = () => {
  isAnimationPaused.value = true;
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
  if (scrollTimeout) {
    clearTimeout(scrollTimeout);
    scrollTimeout = null;
  }
};

const resetWordCloud = () => {
  stopAnimation();
  allWords.value = [];
  displayedWords.value = [];
  scrollPosition.value = 0;
  if (cloudContainer.value) {
    cloudContainer.value.scrollTop = 0;
  }
  setTimeout(applyWaterfallLayout, 100);
  setTimeout(startAnimation, 600);
};

onMounted(() => {
  setTimeout(applyWaterfallLayout, 100);
  setTimeout(startAnimation, 600);
});

onUnmounted(() => {
  stopAnimation();
});

defineExpose({
  resetWordCloud
});

watch(
  () => props.config,
  () => {
    resetWordCloud();
  },
  { deep: true }
);
</script>
<style scoped lang="scss">
.word-cloud-container {
  backdrop-filter: saturate(50%) blur(4px);
  position: relative;
  width: 100%;
  overflow: hidden;
}

.word-cloud-wrapper {
  position: relative;
  margin: 0 100px;
  height: auto; /* 动态高度 */
  width: calc(100% - 200px);
}

.word-item {
  position: absolute;
  display: inline-block;
  text-align: left;
  margin: 0; /* 移除 margin，使用 padding 或统一间距控制 */
  padding: 3px 6px;
  color: #a8c3dc;
  opacity: 0; /* 初始透明 */
  transition: opacity 0.5s ease-in-out, transform 0.3s ease-in-out;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  will-change: opacity, transform;
}
</style>
