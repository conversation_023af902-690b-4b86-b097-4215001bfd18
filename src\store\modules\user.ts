import { defineStore } from "pinia";
import { store } from "@/store";
import { userType } from "./types";
import { routerArrays } from "@/layout/types";
import { resetRouter, router } from "@/router";
import { editAvatar, editPassword, getLogin, getProfile, loginOut, refreshAccessToken, switchTenant } from "@/api/user";
import { queryTenantsByCurrentUser } from "@/api/platform/tenant";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { type DataInfo, getToken, removeToken, setToken } from "@/utils/auth";
import { IAuth, IProfile, IUpdatePassword } from "@/models/user";
import { ITenant } from "@/models";
import { useSystemAuthStore } from "./system-auth";

export const useUserStore = defineStore({
  id: "cx-user",
  state: (): userType => ({
    // 用户信息
    profile: {} as IProfile,
    isManualLogOut: false as boolean
  }),

  getters: {
    getProfile: async state => {
      if (!state.profile || Object.keys(state.profile).length === 0) {
        if (getToken()) {
          state.profile = (await getProfile()).data;
        }
      }
      return state.profile;
    },

    getAccountTenants: async state => {
      if (!Array.isArray(state.accountTenants) || state.accountTenants.length === 0) {
        state.accountTenants = (await queryTenantsByCurrentUser()).data;
      }
      return state.accountTenants;
    },
    getAuth: state => {
      return state.profile?.menuIds?.map(x => x.permission);
    }
  },
  actions: {
    async updateProfile(): Promise<void> {
      this.profile = (await getProfile()).data;
    },
    clearProfile(): void {
      this.profile = undefined;
      this.accountTenants = undefined;
    },
    /** 登入 */
    async loginByUsername(username: string, password: string) {
      const auth: IAuth = (await getLogin(username, password)).data;
      await setTokenByAuth(auth);
    },

    /** 登入 */
    async loginByWeChat(data: IAuth) {
      await setTokenByAuth(data);
    },

    /** 前端登出（不调用接口） */
    logOut() {
      this.isManualLogOut = true;
      loginOut().then(() => {
        this.clearProfile();
        removeToken();
        useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
        resetRouter();
        useSystemAuthStore().setBusinessLicenseAuth();
        this.isManualLogOut = false;
        router.push("/login");
      });
    },
    /** 刷新`token` */
    async refreshAccessToken(refreshToken: string) {
      const auth: IAuth = (await refreshAccessToken(refreshToken)).data;
      await setTokenByAuth(auth);
    },

    async switchTenant(tenantId: string) {
      const auth: IAuth = (await switchTenant(tenantId)).data;
      await setTokenByAuth(auth);
      window.location.href = "/";
    },

    async editPassword(data: IUpdatePassword) {
      return editPassword(data);
    },

    async editAvatar(data: FormData) {
      return editAvatar(data);
    },

    setUserTenantInfo(tenantInfo: ITenant) {
      this.profile.tenantInfo = tenantInfo;
    },
    setAccountTenants(tenants: Array<ITenant>) {
      this.accountTenants = tenants;
    },
    setUserProfile(profile: IProfile) {
      this.profile = profile;
    },
    setManualLogOut(isManualLogOut: boolean) {
      this.isManualLogOut = isManualLogOut;
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}

async function setTokenByAuth(auth: IAuth): Promise<void> {
  const { accessToken, refreshToken, expiresTime } = auth;
  const token: DataInfo<Date> = {
    accessToken,
    refreshToken,
    expires: new Date(expiresTime)
  };
  setToken(token);
}
