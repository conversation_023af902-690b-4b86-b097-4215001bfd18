<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="模板">
          <el-upload
            class="w-full"
            ref="uploadRef"
            :file-list="fileList"
            drag
            :auto-upload="false"
            :limit="1"
            :on-exceed="onExceed"
            :on-change="onChangeUploadFile"
            :on-remove="onRemoveFile"
            accept=".zip,.xlsx,.xls,.csv"
          >
            <el-icon class="upload-icon" :size="30"><upload-filled /></el-icon>
            <div>拖动上传 or <span class="text-primary">点击上传</span></div>
            <template #tip>
              <span class="el-upload__tip">支持上传.zip,.xlsx,.xls,.csv</span>
            </template>
          </el-upload>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="item in QuotationTemplateStatusOption"
              :value="parseFloat(item.value)"
              :label="parseFloat(item.value)"
              :key="item.value"
              border
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" :rows="2" type="textarea" clearable placeholder="请输入备注" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import {
  ElMessage,
  FormInstance,
  FormRules,
  genFileId,
  UploadInstance,
  UploadProps,
  UploadRawFile,
  UploadUserFile
} from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { IQuotationTemplate, IQuotationTemplateForm, QuotationTemplateStatusOption } from "@/models";
import { uploadFile } from "@/api/upload-file";
import { StatusEnum } from "@/enums";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  emptyFormValue
});

const form = reactive<IQuotationTemplateForm>({
  status: StatusEnum.ENABLE
});
const formRef = ref<FormInstance>();
const uploadRef = ref<UploadInstance>();
const fileList = ref<Array<UploadUserFile>>();
let uploadRawFile: UploadRawFile = null;

const rules: FormRules = {
  status: [{ required: true, trigger: "change", message: "状态不能为空" }]
};

const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRawFile = file;
  uploadRef.value!.handleStart(file);
};

const onChangeUploadFile: UploadProps["onChange"] = uploadFile => {
  form.inquiryAttachmentId = null;
  uploadRawFile = uploadFile.raw;
};

const onRemoveFile: UploadProps["onRemove"] = () => {
  form.inquiryAttachmentId = null;
  uploadRawFile = null;
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }

  if (!uploadRawFile && !form.inquiryAttachmentId) {
    ElMessage.warning("请上传模板文件");
    return false;
  }

  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IQuotationTemplate) {
  fileList.value = [
    {
      name: v.inquiryAttachmentName,
      url: v.inquiryAttachmentUrl
    }
  ];
  Object.assign(form, v);
}
function emptyFormValue() {
  uploadRawFile = null;
  fileList.value = [];
  form.inquiryAttachmentId = null;
  form.status = StatusEnum.ENABLE;
  formRef.value.resetFields();
}

/**
 * @description: 获取表单值
 */
async function getFormValue() {
  if (!uploadRawFile && !form.inquiryAttachmentId) {
    ElMessage.warning("请上传模板文件");
    return;
  }

  if (uploadRawFile) {
    const formData = new FormData();
    formData.append("file", uploadRawFile);
    const { data } = await uploadFile(formData);
    form.inquiryAttachmentId = data.id;
  }

  return Object.assign({}, form);
}
</script>

<style scoped lang="scss">
.upload-icon {
  color: var(--el-color-primary);
}
</style>
