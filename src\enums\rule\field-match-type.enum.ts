import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

/**
 * 枚举：字段匹配类型
 */
export enum FieldMatchTypeEnum {
  /**
   * 不匹配
   */
  NONE = 0,

  /**
   * 完全匹配
   */
  EQUALS = 1,

  /**
   * 部分匹配
   */
  PARTIAL = 2,

  /**
   * 正则匹配
   */
  REGEX = 3,

  /**
   * 表达式匹配
   */
  EXPRESSION = 4
}

/**
 * 字段匹配类型的描述映射
 */
export const FieldMatchTypeEnumMapDesc: Record<FieldMatchTypeEnum, string> = {
  [FieldMatchTypeEnum.NONE]: "不匹配",
  [FieldMatchTypeEnum.EQUALS]: "等于",
  [FieldMatchTypeEnum.PARTIAL]: "包含",
  [FieldMatchTypeEnum.REGEX]: "正则",
  [FieldMatchTypeEnum.EXPRESSION]: "表达式"
};

export const ModeMatchlRule: Record<string, Array<IOption>> = {
  builtInMatching: mapDescToOptions<number>(FieldMatchTypeEnumMapDesc).filter(x =>
    [FieldMatchTypeEnum.EQUALS, FieldMatchTypeEnum.PARTIAL, FieldMatchTypeEnum.REGEX].includes(x.value as number)
  )
};

/**
 * 字段匹配类型的描述映射
 */
export const FieldMatchTypeEnumMapOperator: Record<FieldMatchTypeEnum, string> = {
  [FieldMatchTypeEnum.NONE]: "!=",
  [FieldMatchTypeEnum.EQUALS]: "=",
  [FieldMatchTypeEnum.PARTIAL]: "like",
  [FieldMatchTypeEnum.REGEX]: "regex",
  [FieldMatchTypeEnum.EXPRESSION]: "expression"
};
