import { IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "./util";

type Result = {
  success: boolean;
  data: Array<DynamicRouteConfigsTable>;
};

export const getAsyncRoutes = (mock = false) => {
  if (mock) {
    return http.request<Result>("get", "/getAsyncRoutes").then(res => {
      ascending(res.data);
      return res;
    });
  }
  const url = withApiGateway("admin-api/system/routeMap/tree");
  return http.get<void, IResponse<Array<DynamicRouteConfigsTable>>>(url).then(res => {
    ascending(res.data);
    return res;
  });
};

function ascending(routes: Array<DynamicRouteConfigsTable>) {
  if (!Array.isArray(routes) || !routes.length) {
    return;
  }
  routes.forEach(route => {
    deleteEmptyChildren(route);
    route.meta.rank = route.meta.rank ?? Infinity;
    ascending(route.children);
  });
  routes.sort((a, b) => a.meta.rank - b.meta.rank);
}

function deleteEmptyChildren(route: DynamicRouteConfigsTable) {
  if (!Array.isArray(route.children) || !route.children.length) {
    Reflect.deleteProperty(route, "children");
  }
}
