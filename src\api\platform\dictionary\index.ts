import {
  IDictionary,
  IDictionaryForm,
  IDictionaryOption,
  IDictionaryOptionForm,
  IDictionaryOptionReq,
  IResponse
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../../util";

export const queryAllDictionary = () => {
  return http.get<void, IResponse<Array<IDictionary>>>(withApiGateway("admin-api/system/user-dict/getDictInfoList"));
};

export const editDictionary = (data: IDictionaryForm) => {
  return http.post<IDictionaryForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/user-dict/saveDictInfo"),
    {
      data
    },
    { showErrorInDialog: true }
  );
};

export const addDictionary = (data: IDictionaryForm) => {
  return http.post<IDictionaryForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/user-dict/saveDictInfo"),
    {
      data
    },
    { showErrorInDialog: true }
  );
};

export const addDictionaryOption = (data: IDictionaryOptionForm) => {
  return http.post<IDictionaryOptionForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/user-dict/saveDictDetail"),
    { data },
    { showErrorInDialog: true }
  );
};

export const editDictionaryOption = (data: IDictionaryOptionForm) => {
  return http.post<IDictionaryOptionForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/user-dict/saveDictDetail"),
    { data },
    { showErrorInDialog: true }
  );
};

export const getDictionaryOptions = (data: IDictionaryOptionReq) => {
  return http.post<IDictionaryOptionReq, IResponse<Array<IDictionaryOption>>>(
    withApiGateway("admin-api/system/user-dict/getDictDetail"),
    { data }
  );
};

/** 根据 parentCode 查询字典项（区分租户） */
export const getTenantDictionaryOptions = (data: IDictionaryOptionReq) => {
  return http.post<IDictionaryOptionReq, IResponse<Array<IDictionaryOption>>>(
    withApiGateway("admin-api/system/user-dict/getDictDetailByTenant"),
    { data }
  );
};
