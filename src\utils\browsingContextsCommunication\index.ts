import { TopicEnum } from "./topic.enum";
import { useUserStore } from "@/store/modules/user";
import { IProfile } from "@/models/user";

const BROADCAST_CHANNEL_NAME = "cx_channel";
const bc = new BroadcastChannel(BROADCAST_CHANNEL_NAME);

function postMessage(topic: TopicEnum.LOGIN);
function postMessage(topic: TopicEnum.SWITCH_TENANT);
function postMessage(topic: TopicEnum.UPDATE_PROFILE, data: IProfile);
function postMessage(topic: TopicEnum, data?: IProfile) {
  bc.postMessage({ topic, data });
}

if (!bc.onmessage) {
  bc.onmessage = function (ev: MessageEvent) {
    const { topic, data } = ev.data;
    switch (topic) {
      case TopicEnum.LOGIN:
        window.location.reload();
        break;
      case TopicEnum.SWITCH_TENANT:
        window.location.href = "/";
        break;
      case TopicEnum.UPDATE_PROFILE:
        useUserStore().setUserProfile(data);
        break;
    }
  };
}

export { postMessage };
