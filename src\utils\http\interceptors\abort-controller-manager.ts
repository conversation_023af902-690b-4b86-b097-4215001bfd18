import { RequestDuplicate<PERSON>ey } from "../types";

class AbortControllerManager {
  private abortControllerMap = new Map<RequestDuplicateKey, AbortController>();

  public generateSignal(key: RequestDuplicate<PERSON>ey) {
    if (!key) {
      return;
    }
    const controller = new AbortController();
    this.abortControllerMap.set(key, controller);
    return controller.signal;
  }

  public abort(key: RequestDuplicateKey) {
    if (!key) {
      return;
    }
    if (this.abortControllerMap.has(key)) {
      this.abortControllerMap.get(key).abort();
    }
  }

  public delete(key: RequestDuplicate<PERSON>ey) {
    if (!key) {
      return;
    }
    this.abortControllerMap.delete(key);
  }
}

export const abortControllerManager = new AbortControllerManager();
