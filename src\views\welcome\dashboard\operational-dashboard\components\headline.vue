<template>
  <header class="w-full flex items-center justify-between mb-4">
    <div class="headline">{{ title }}</div>
    <slot name="extra" />
  </header>
</template>

<script setup lang="ts">
import { computed } from "vue";
const props = defineProps<{
  title: string;
}>();

const title = computed(() => props.title);
</script>

<style lang="scss" scoped>
.headline {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}
</style>
