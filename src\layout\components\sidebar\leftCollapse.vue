<script setup lang="ts">
import { ref } from "vue";
import { useNav } from "@/layout/hooks/useNav";
// import MenuFold from "@iconify-icons/ri/menu-fold-fill";

interface Props {
  isActive: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false
});

const visible = ref(false);
const { tooltipEffect } = useNav();

const emit = defineEmits<{
  (e: "toggleClick"): void;
}>();

const toggleClick = () => {
  emit("toggleClick");
};

const transform = () => {
  return props.isActive ? "icon-fold-left" : "icon-fold-right";
};
</script>

<template>
  <div class="container">
    <el-tooltip
      placement="right"
      :visible="visible"
      :effect="tooltipEffect"
      :content="props.isActive ? '点击折叠' : '点击展开'"
    >
      <FontIcon
        :icon="transform()"
        @click="toggleClick"
        class="text-secondary icon-text"
        @mouseenter="visible = true"
        @mouseleave="visible = false"
      />
    </el-tooltip>
  </div>
</template>

<style lang="scss" scoped>
.container {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 1rem;
  cursor: pointer;
}

.icon-text {
  cursor: pointer;
  font-size: 20px;
}
</style>
