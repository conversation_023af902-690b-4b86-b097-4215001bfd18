import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse, ITokenUsageReq, ITokenUsage, IEmployeeUsageToken, IFile } from "@/models";

/** 获取员工 token 用量汇总 */
export const queryTokenUsageForEmployeeDaily = (params?: ITokenUsageReq) => {
  const url: string = withApiGateway("admin-api/business/tokenUsage/employeeDaily");
  return http.get<ITokenUsageReq, IResponse<Array<IEmployeeUsageToken>>>(url, {
    params
  });
};

/** 员工 token 用量汇总-导出 */
export const exportEmployeeDailyUsageTokenExport = (params?: ITokenUsageReq) => {
  const url: string = withApiGateway("admin-api/business/tokenUsage/employeeDailyExport");
  return http.get<ITokenUsageReq, IResponse<IFile>>(url, {
    params
  });
};

/** 获取企业每日 token 用量 */
export const queryTokenUsageForCompanyDaily = (params?: ITokenUsageReq) => {
  const url: string = withApiGateway("admin-api/business/tokenUsage/companyDaily");
  return http.get<ITokenUsageReq, IResponse<Array<ITokenUsage>>>(url, {
    params
  });
};

/** 企业每日 token 用量-导出 */
export const exportCompanyDailyUsageTokenExport = (params?: ITokenUsageReq) => {
  const url: string = withApiGateway("admin-api/business/tokenUsage/companyDailyExport");
  return http.get<ITokenUsageReq, IResponse<IFile>>(url, {
    params
  });
};
