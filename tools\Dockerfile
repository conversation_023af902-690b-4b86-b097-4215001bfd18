FROM hub-internal.cxist.com:5000/byzan/nginx:byz-nightly

# Copy default nginx config
COPY ./default.conf /etc/nginx/conf.d/default.template

# Remove default nginx website
RUN rm -rf /usr/share/nginx/html/*
COPY ./dist/ /usr/share/nginx/html

ENV API_HOST http://*************:30515
ENV WEBSOCKET_HOST http://*************:30515

COPY ./startup.sh /usr/local/bin
RUN chmod +x /usr/local/bin/startup.sh

WORKDIR /etc/nginx/conf.d

ENTRYPOINT startup.sh
