import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "原材料类型",
      prop: "bomRaw.type"
    },
    {
      label: "原材料名称",
      prop: "bomRaw.name"
    },
    {
      label: "原材料型号",
      prop: "bomRaw.model"
    },
    {
      label: "原材料规格",
      prop: "bomRaw.specification"
    },

    {
      label: "单价成本",
      prop: "price"
    },
    {
      label: "计量单位",
      prop: "productUnit"
    },
    {
      label: "版本号",
      prop: "versionNum"
    },
    {
      label: "是否最新版本",
      prop: "isLatest"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char11
    }
  ];
  return { columns };
}
