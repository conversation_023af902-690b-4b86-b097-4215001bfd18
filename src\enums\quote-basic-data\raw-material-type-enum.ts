export enum RawMaterialTypeEnum {
  /** 导体原料 */
  CONDUCTOR = "CONDUCTOR",
  /** 绝缘材料 */
  INSULATION = "INSULATION",
  /** 护套材料 */
  SHEATH = "SHEATH",
  /** 填充材料 */
  FILLER = "FILLER",
  /** 铠装材料 */
  ARMOR = "ARMOR"
}

/**
 * 描述映射
 */
export const RawMaterialTypeEnumMapDesc: Record<RawMaterialTypeEnum, string> = {
  [RawMaterialTypeEnum.CONDUCTOR]: "导体原料",
  [RawMaterialTypeEnum.INSULATION]: "绝缘材料",
  [RawMaterialTypeEnum.SHEATH]: "护套材料",
  [RawMaterialTypeEnum.FILLER]: "填充材料",
  [RawMaterialTypeEnum.ARMOR]: "铠装材料"
};

/**
 * 颜色映射
 */
export const RawMaterialTypeEnumMapColor: Record<RawMaterialTypeEnum, string> = {
  [RawMaterialTypeEnum.CONDUCTOR]: "primary",
  [RawMaterialTypeEnum.INSULATION]: "success",
  [RawMaterialTypeEnum.SHEATH]: "warning",
  [RawMaterialTypeEnum.FILLER]: "danger",
  [RawMaterialTypeEnum.ARMOR]: "info"
};
