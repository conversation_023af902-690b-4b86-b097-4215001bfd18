<template>
  <div class="content">
    <div class="title">{{ title }}</div>
    <div class="today-value">
      <span v-if="type == 'money'">￥</span>{{ todayValue }} <span v-if="type == 'count'">单</span>
    </div>
    <div class="yesterday-value">
      昨日: <span v-if="type == 'money'">￥</span>{{ yesterdayValue }}<span v-if="type == 'count'"> 单</span>
    </div>
    <div class="icon">
      <img :src="icon" alt="icon" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatNumber } from "@/utils/format";
import { computed } from "vue";

const props = defineProps<{
  title: string;
  todayValue: number;
  unit?: string;
  yesterdayValue: number;
  icon: string;
  type?: string;
}>();

const title = computed(() => props.title);
const type = computed(() => props.type);
const todayValue = computed(() =>
  props.type == "money" ? formatNumber(props.todayValue, props.type) : props.todayValue
);
const yesterdayValue = computed(() =>
  props.type == "money" ? formatNumber(props.yesterdayValue, props.type) : props.yesterdayValue
);
const icon = computed(() => props.icon);
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  z-index: 0;
  padding: 20px;
  background: var(--el-bg-color);
}

.title {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 2px;
}

.today-value {
  overflow: auto;
  color: var(--text-primary);
  font-size: 28px;
  font-weight: 600;
}

.yesterday-value {
  overflow: auto;
  margin-top: 8px;
  color: var(--el-text-color-placeholder);
  font-size: 13px;
}

.icon {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: -1;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
