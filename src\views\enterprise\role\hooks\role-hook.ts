import { TenantStatusEnum } from "@/enums";
import { IRole, IRoleForm } from "@/models";
import { useRoleStore } from "@/store/modules";
import { useConfirm } from "@/utils/useConfirm";
import { ElMessage } from "element-plus";
import { reactive } from "vue";

export const useRoleHook = () => {
  const roleState = reactive<{
    roleModalVisible: boolean;
    activateRoleId: string;
    activateRole?: IRole;
  }>({
    roleModalVisible: false,
    activateRoleId: undefined,
    activateRole: undefined
  });
  const roleStore = useRoleStore();

  const setRoleModalVisible = (visible: boolean) => {
    roleState.roleModalVisible = visible;
  };

  const onRoleOperationShow = (data: IRole) => {
    roleState.activateRoleId = data.id;
  };

  const onRoleOperationHide = () => {
    roleState.activateRoleId = undefined;
  };

  const onConfirmSaveRole = async (role: IRoleForm) => {
    if (!role.id) {
      await roleStore.addRole(role);
    } else {
      await roleStore.editRole(role);
      roleState.activateRole = { ...role };
    }

    ElMessage.success(role.id ? "编辑成功" : "新增成功");
    roleState.roleModalVisible = false;
    await roleStore.queryRoleList();
  };

  const onCancelAddRoleModal = () => {
    roleState.roleModalVisible = false;
  };

  const onDeleteRole = async (role: IRole) => {
    if (!role || Object.keys(role).length === 0) {
      ElMessage.warning("请选择角色");
      return;
    }

    if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
      return;
    }

    await roleStore.deleteRole(role.id);
    ElMessage.success("删除成功");
    if (role.id === roleState.activateRole?.id) {
      roleState.activateRole = undefined;
    }
    await roleStore.queryRoleList();
  };

  const onAddRole = () => {
    roleStore.setRoleForm({ status: TenantStatusEnum.ENABLE });
    roleState.activateRole = undefined;
    roleState.roleModalVisible = true;
  };

  const onEditRole = (role?: IRole) => {
    if (!role || Object.keys(role).length === 0) {
      ElMessage.warning("请选择角色");
      return;
    }
    roleStore.setRoleForm({ ...role });
    roleState.roleModalVisible = true;
  };

  const onChooseRole = (role: IRole) => {
    roleState.activateRole = role;
  };
  return {
    roleState,
    setRoleModalVisible,
    onConfirmSaveRole,
    onCancelAddRoleModal,
    onDeleteRole,
    onAddRole,
    onEditRole,
    onChooseRole,
    onRoleOperationShow,
    onRoleOperationHide
  };
};
