<template>
  <div class="overflow-hidden w-full flex flex-1 p-5">
    <div class="bg-bg_color p-5 w-[220px]">
      <ProductCategoryList ref="categoryRef" @onSelectCategory="handleSelectCategory($event)" />
    </div>
    <div class="overflow-hidden flex-1 flex flex-col ml-5">
      <div class="bg-bg_color pt-5 flex justify-between pr-5">
        <ElForm :inline="true" :model="state.params" class="flex-1">
          <ElFormItem label="型号：">
            <ElInput class="!w-[200px]" clearable v-model="state.params.modelName" placeholder="请输入型号" />
          </ElFormItem>
          <ElFormItem label="规格：">
            <ElInput class="!w-[200px]" clearable v-model="state.params.specification" placeholder="请输入规格" />
          </ElFormItem>

          <ElFormItem>
            <ElButton type="primary" @click="onQuery()">搜索</ElButton>
            <ElButton @click="onResetQuery()">重置</ElButton>
          </ElFormItem>
        </ElForm>
        <div class="mb-5">
          <!-- <el-button
            v-auth="PermissionKey.meta.metaPproductImportExport"
            :icon="Download"
            @click="state.productVersionDrawerVisible = true"
            >导出</el-button
          >-->
          <el-button
            v-auth="PermissionKey.meta.metaPproductImportExport"
            class="mr-3"
            :icon="FolderOpened"
            type="primary"
            @click="onShowFileImportDialogVisible()"
            >导入</el-button
          >
          <AddEditProductInfoDialog
            mode="add"
            :category="state.selectCategory"
            :versionId="state.productVersion?.id"
            @postSaveSuccess="handleSaveSuccess()"
          >
            <template #trigger="{ openDialog }">
              <el-button v-auth="PermissionKey.meta.metaPproductCreate" :icon="Plus" type="primary" @click="openDialog"
                >新增</el-button
              >
            </template>
          </AddEditProductInfoDialog>
        </div>
      </div>

      <div class="bg-bg_color mt-5 p-5 flex flex-col flex-1 overflow-hidden relative">
        <div class="mb-5 h-[24px] flex items-center justify-between">
          <div>
            <span class="text-base mr-2">选中分类：</span>
            <CXTag v-if="state.selectCategory?.id" type="primary" closable @Close="handleCloseCategoryTag()">{{
              state.selectCategory?.name
            }}</CXTag>
          </div>
          <div class="flex items-center cursor-pointer" v-if="state.productVersion?.id" @click="onShowProductVersion()">
            <span class="text-base mr-2">版本号:</span>
            <CXTag type="info">{{ state.productVersion?.versionNumber }}</CXTag>
          </div>
        </div>
        <PureTable
          class="flex-1 overflow-hidden pagination"
          row-key="id"
          :data="state.list"
          :columns="columns"
          size="large"
          :loading="loading"
          showOverflowTooltip
          v-model:pagination="pagination"
          @page-current-change="onPageCurrentChange"
          @page-size-change="onPageSizeChange"
        >
          <template #categoryName="{ row }"> {{ row.product?.model?.category?.name }} </template>
          <template #productCode="{ row }"> {{ row.product?.productCode }} </template>
          <template #modelName="{ row }"> {{ row.product?.modelName }} </template>
          <template #voltage="{ row }"> {{ row.product?.model?.voltage }} </template>
          <template #crossSection="{ row }"> {{ row.product?.crossSection }} </template>
          <template #unit="{ row }"> {{ row.product?.unit }} </template>
          <template #specification="{ row }"> {{ row.product?.specification }} </template>
          <template #isFromSGCC="{ row }">
            <el-checkbox :checked="row.product?.isFromSGCC" disabled />
          </template>
          <template #operation="data">
            <div>
              <AddEditProductInfoDialog
                v-auth="PermissionKey.meta.metaPproductEdit"
                mode="edit"
                :id="data.row.id"
                @postSaveSuccess="handleSaveSuccess()"
              >
                <template #trigger="{ openDialog }">
                  <el-button link type="primary" @click="openDialog">编辑</el-button>
                </template>
              </AddEditProductInfoDialog>
              <ElButton
                link
                type="danger"
                v-auth="PermissionKey.meta.metaPproductDelete"
                @click="onDelete(data.row.id)"
              >
                删除
              </ElButton>
            </div>
          </template>
          <template #empty>
            <CxEmptyData />
          </template>
        </PureTable>
      </div>
    </div>
    <ProductVersionDrawer
      v-model="state.productVersionDrawerVisible"
      :versionId="state.productVersion?.id"
      @onSelectProductVersion="handleSelectProductVersion($event)"
    />
    <FileImportDialog v-model="state.fileImportDialogVisible" @onImportSuccess="onQuery()" />
  </div>
</template>

<script setup lang="ts" name="product-info">
import { ref, reactive, onMounted } from "vue";
import { Plus, FolderOpened } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryProductInfo, deleteProductInfoById } from "@/api/basic/product-info";
import { IProductCategory, IProductInfo, IProductInfoReq, IProductVersion } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import AddEditProductInfoDialog from "./add-edit-product-info/dialog.vue";
import ProductCategoryList from "@/views/components/product-category/product-category-list.vue";
import ProductVersionDrawer from "@/views/components/product-info-history/drawer.vue";
import CXTag from "@/components/CxTag/index.vue";
import { getLatestVersion } from "@/api/product-version";
import FileImportDialog from "./file-import/index.vue";
import { PermissionKey } from "@/consts/permission-key";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const categoryRef = ref<InstanceType<typeof ProductCategoryList>>();
const state = reactive<{
  list: Array<IProductInfo>;
  params: IProductInfoReq;
  selectCategory?: IProductCategory;
  productVersionDrawerVisible: boolean;
  productVersion?: IProductVersion;
  fileImportDialogVisible: boolean;
}>({
  list: [],
  params: {},
  productVersion: {},
  productVersionDrawerVisible: false,
  fileImportDialogVisible: false
});

onMounted(async () => {
  await handleGetLatestVersion();
  onQuery();
});

const handleSaveSuccess = () => {
  categoryRef.value.handleQueryAllCategory();
  onQuery();
};

const handleCloseCategoryTag = () => {
  handleSelectCategory(null);
};

const handleSelectCategory = (category: IProductCategory) => {
  state.selectCategory = category;
  pagination.currentPage = 1;
  onQuery();
};

const onShowFileImportDialogVisible = () => {
  state.fileImportDialogVisible = true;
};

const onShowProductVersion = () => {
  state.productVersionDrawerVisible = true;
};

const handleSelectProductVersion = (version?: IProductVersion) => {
  state.productVersion = version;
  pagination.currentPage = 1;
  onQuery();
};

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteProductInfoById(id);
  ElMessage.success("删除成功");
  requestList();
};

const handleGetLatestVersion = async () => {
  const { data } = await getLatestVersion();
  if (data?.id) {
    state.productVersion = data;
  }
};

const requestList = useLoadingFn(async () => {
  let params: IProductInfoReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.selectCategory?.id) {
    params.productCategoryId = state.selectCategory.id;
  }

  if (state.productVersion?.id) {
    params.versionId = state.productVersion?.id;
  }

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryProductInfo(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
