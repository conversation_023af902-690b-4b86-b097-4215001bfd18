<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1" label-width="90px">
        <ElFormItem label="姓名：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.username" placeholder="请输入姓名" />
        </ElFormItem>
        <ElFormItem label="手机号：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.phone" placeholder="请输入手机号" />
        </ElFormItem>
        <ElFormItem label="所属企业：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.enterprise" placeholder="请输入所属企业" />
        </ElFormItem>
        <ElFormItem label="所在部门：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.department" placeholder="请输入所在部门" />
        </ElFormItem>
        <ElFormItem label="岗位：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.position" placeholder="请输入岗位" />
        </ElFormItem>
        <ElFormItem label="邮箱：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.email" placeholder="请输入邮箱" />
        </ElFormItem>
        <ElFormItem label="审核状态：">
          <el-radio-group v-model="state.params.reviewStatus">
            <el-radio v-for="item in AccountRegisterReviewStatusOptions" border :label="item.value" :key="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </ElFormItem>

        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #reviewStatus="{ row }">
          <CXTag :type="AccountRegisterReviewStatusEnumMapColor[row.reviewStatus]">{{
            AccountRegisterReviewStatusEnumMapDesc[row.reviewStatus]
          }}</CXTag>
        </template>
        <template #registerSource="{ row }">
          <CXTag :type="AccountRegisterSourceEnumEnumMapColor[row.registerSource]">{{
            AccountRegisterSourceEnumMapDesc[row.registerSource]
          }}</CXTag>
        </template>
        <template #operation="data">
          <div v-if="data.row.reviewStatus != AccountRegisterReviewStatusEnum.ReviewPassed">
            <AddEditAccountReviewDialog mode="edit" :id="data.row.id" @post-save-success="onQuery()">
              <template #trigger="{ openDialog }">
                <el-button link type="primary" @click="openDialog">审核</el-button>
              </template>
            </AddEditAccountReviewDialog>
            <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="account-review">
import { onMounted, ref, reactive } from "vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryAccountReview, deleteAccountReviewById } from "@/api/platform/account-review";
import { IAccountReview } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import AddEditAccountReviewDialog from "./add-edit-account-review/dialog.vue";
import CXTag from "@/components/CxTag/index.vue";
import {
  AccountRegisterReviewStatusEnumMapDesc,
  AccountRegisterReviewStatusEnumMapColor,
  AccountRegisterReviewStatusOptions,
  AccountRegisterReviewStatusEnum,
  AccountRegisterSourceEnumMapDesc,
  AccountRegisterSourceEnumEnumMapColor
} from "@/enums";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IAccountReview>;
  params: { [key: string]: string };
}>({
  list: [],
  params: {}
});

onMounted(() => {
  requestList();
});

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteAccountReviewById(id);
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryAccountReview(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
