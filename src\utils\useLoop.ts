/**
 *
 * @param fn Loop call function
 * @param period  Loop call period
 * @param continueAfterFail Whether continue call function after fail
 * @param immediate Immediate call function
 */
export function useLoop(fn: Function, period: number, continueAfterFail = true, immediate = true) {
  let timeoutHandle: ReturnType<typeof setTimeout>;
  let isStart = false;

  async function execFn() {
    try {
      await fn();
    } catch (e) {
      console.error(e);
      if (!continueAfterFail) {
        return;
      }
    }
    if (!isStart) {
      return;
    }
    execNextFn(period);
  }

  function execNextFn(timeout: number) {
    timeoutHandle = setTimeout(() => execFn(), timeout);
  }

  function startLoop() {
    if (isStart) {
      return;
    }
    isStart = true;
    execNextFn(immediate ? 0 : period);
  }

  function stopLoop() {
    isStart = false;
    if (timeoutHandle) {
      clearTimeout(timeoutHandle);
      timeoutHandle = null;
    }
  }

  return {
    startLoop,
    stopLoop
  };
}
