import { IBase, IProduct, IProductModel } from "@/models";

export interface IEquivalent extends IBase {
  /*** 产品分类名称 */
  categoryName?: string;

  /** 型号名称 */
  modelName?: string;

  /** 产品ID  */
  productCode?: string;

  /** 电压等级 */
  voltage?: string;

  /** 平方数  */
  crossSection?: number;

  /*** 规格 */
  specification?: string;

  /** 等效命名（别名） */
  equivalentName?: string;

  /** 备注  */
  remark?: string;

  product?: IProduct;

  productModel?: IProductModel;
}
