<template>
  <div class="control-list">
    <section class="control-content" :class="validateClass">
      <Component
        :is="currentComponent"
        v-bind:inputValue="getControlValue"
        v-bind:config="rowData"
        @valueChange="controlChange($event)"
      />
    </section>
  </div>
</template>

<script setup lang="ts">
import TextControl from "./components/TextControl/TextControl.vue";
import NumberTextControl from "./components/NumberTextControl/NumberTextControl.vue";
import DateControl from "./components/DateControl/DateControl.vue";
import SelectControl from "./components/SelectControl/SelectControl.vue";
import GroupRadio from "./components/GroupRadioControl/GroupRadioControl.vue";
import { computed } from "vue";
import { useTableForm } from "./form-value-hooks";
import { cloneDeep } from "lodash-unified";
import { EControlType } from "@/enums";

const props = withDefaults(
  defineProps<{
    editMode: boolean;
    isAdd: boolean;
    rowData: Record<string, unknown>;
    formValue: Record<string, unknown>;
    tableControlKeyWords: string;
    formControlKeyWords: string;
  }>(),
  {}
);

const emits = defineEmits(["formValueChange"]);
// 提取控制值及下拉列表
const tableControlKeyWords = computed(() => props.tableControlKeyWords);
const formControlKeyWords = computed(() => props.formControlKeyWords);
const { getControlValueHook } = useTableForm();
const rowTypeData = cloneDeep(props.rowData);

const currentComponent = computed(() => {
  const controlKey = rowTypeData[tableControlKeyWords.value] || "";
  return controlKey === EControlType.TextControl
    ? TextControl
    : controlKey === EControlType.NumberTextControl
    ? NumberTextControl
    : controlKey === EControlType.SelectControl
    ? SelectControl
    : controlKey === EControlType.DateControl
    ? DateControl
    : controlKey === EControlType.RadioControl
    ? GroupRadio
    : null;
});

/**
 * 表单控件数据更新
 */
const controlChange = data => {
  emits("formValueChange", data);
};

/**
 * 抽取不同控件的key
 */
const getControlValue = computed(() => {
  return getControlValueHook(props.rowData, props.formValue, formControlKeyWords.value);
});

/**
 * 验证提示类
 */
const validateClass = computed(() => {
  return [!props.rowData?.validated ? "validate-error" : ""];
});
</script>

<style scoped lang="scss">
.control-list {
  width: 100%;

  :deep(.el-select),
  :deep(.el-input) {
    width: 100%;
  }
}
</style>
