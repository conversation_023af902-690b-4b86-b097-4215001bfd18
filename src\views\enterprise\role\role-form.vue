<template>
  <div class="px-5">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="right" label-width="80px" @submit.prevent>
      <el-form-item label="状态" prop="status">
        <el-switch
          clearable
          v-model="form.status"
          :active-value="0"
          :inactive-value="1"
          active-text="启用"
          inactive-text="禁用"
          inline-prompt
        />
      </el-form-item>
      <el-form-item label="角色名称" prop="name">
        <el-input placeholder="请输入角色名称" v-model="form.name" clearable />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          clearable
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="2"
          :maxlength="200"
          :show-word-limit="true"
          resize="none"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IRoleForm } from "@/models";
import { useRoleStore } from "@/store/modules";

defineExpose({
  validate,
  getValidValue
});

const roleStore = useRoleStore();

const formRef = ref<FormInstance>();
const form = reactive<IRoleForm>({
  id: undefined,
  name: undefined,
  code: undefined,
  remark: undefined,
  status: 0
});
const rules = reactive<FormRules>({
  name: [{ required: true, message: requiredMessage("角色名称"), trigger: "change" }]
});

watchEffect(() => {
  if (roleStore.roleForm && Object.keys(roleStore.roleForm).length) {
    Object.assign(form, roleStore.roleForm);
  }
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IRoleForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}
</script>

<style scoped></style>
