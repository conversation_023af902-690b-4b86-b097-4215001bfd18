import { createProdMockServer } from "vite-plugin-mock/es/createProdMockServer";

const modules: Record<string, any> = import.meta.glob("../mock/*.ts", {
  eager: true
});
const mockModules = [];

Object.keys(modules).forEach(key => {
  mockModules.push(...modules[key].default);
});

export function setupProdMockServer() {
  createProdMockServer(mockModules);
  extendXMLHttpRequestUpload();
}

function extendXMLHttpRequestUpload() {
  // @ts-ignore
  const xhr = new window._XMLHttpRequest();
  // @ts-ignore
  window.XMLHttpRequest.prototype.upload = xhr.upload;
}
