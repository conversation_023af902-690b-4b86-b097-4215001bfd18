<template>
  <div class="bg-bg_color p-10 overflow-hidden w-[960px] m-auto mt-5 mb-5 h-full">
    <el-form ref="formRef" :model="form" :rules="rules" size="large" label-width="100px" :disabled="disabledEditState">
      <div>
        <TitleBar class="mb-4" title="汇款信息" />
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="开户名称" prop="accountName">
              <el-input placeholder="请输入开户名称" v-model="form.accountName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户银行" prop="bankName">
              <el-input placeholder="请输入开户银行" v-model="form.bankName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行帐号" prop="bankAccountNumber">
              <el-input placeholder="请输入银行帐号" v-model="form.bankAccountNumber" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider />
        <TitleBar class="mb-4" title="开票信息" />
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="发票抬头" prop="invoiceTitle">
              <el-input placeholder="请输入发票抬头" v-model="form.invoiceTitle" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纳税人识别号" prop="taxpayerIdentificationNumber">
              <el-input placeholder="请输入纳税人识别号" v-model="form.taxpayerIdentificationNumber" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                placeholder="请输入备注"
                v-model="form.remark"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="mt-5 text-right">
      <div v-auth="PermissionKey.enterprise.enterpriseInfoEdit">
        <template v-if="disabledEditState">
          <ElButton type="primary" size="large" @click="onToggleEditState(false)">修改</ElButton>
        </template>
      </div>
      <template v-if="!disabledEditState">
        <ElButton size="large" @click="onToggleEditState(true)">取消</ElButton>
        <ElButton type="primary" size="large" @click="onSaveEditEnterpriseInfo()">保存</ElButton>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { ElForm, ElMessage, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IRemittanceInfo } from "@/models";
import { PermissionKey } from "@/consts";
import TitleBar from "@/components/TitleBar";
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
import { getRemittanceInfo, createOrUpdateRemittanceInfo } from "@/api/enterprise/remittance-info";

usePageStoreHook().setTitle((useRoute().meta?.title as string) || "企业信息");

const formRef = ref<FormInstance>();
const disabledEditState = ref<boolean>(true);
const form = reactive<IRemittanceInfo>({
  id: undefined,
  accountName: undefined,
  bankName: undefined,
  bankAccountNumber: undefined,
  invoiceTitle: undefined,
  taxpayerIdentificationNumber: undefined,
  remark: undefined
});

const rules: FormRules = {
  accountName: [{ required: true, message: requiredMessage("开户名称"), trigger: "change" }],
  bankName: [{ required: true, message: requiredMessage("开户银行"), trigger: "change" }],
  bankAccountNumber: [{ required: true, message: requiredMessage("银行帐号"), trigger: "change" }]
};

onMounted(() => {
  handleGetRemittanceInfo();
});
async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

const onToggleEditState = (state: boolean) => {
  if (state) {
    handleGetRemittanceInfo();
  }
  disabledEditState.value = state;
};

const onSaveEditEnterpriseInfo = async () => {
  if (!(await validate())) {
    return;
  }
  await createOrUpdateRemittanceInfo(form);
  ElMessage.success("编辑成功");
  disabledEditState.value = true;
};

const handleGetRemittanceInfo = async () => {
  const { data } = await getRemittanceInfo();
  Object.assign(form, data);
};
</script>

<style scoped></style>
