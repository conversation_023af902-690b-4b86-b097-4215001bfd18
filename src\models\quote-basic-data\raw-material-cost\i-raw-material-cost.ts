import { ProductUnitEnum } from "@/enums/quote-basic-data";
import { IBase, IRawMaterialInformation } from "@/models";

export interface IRawMaterialCost extends IBase {
  /**
   * 版本号
   */
  versionNum?: string;
  /**
   * 所属原材料id
   */
  bomRawId?: string;

  bomRaw?: IRawMaterialInformation;

  /**
   * 单价成本
   */
  price?: number;
  /**
   * 计量单位
   */
  productUnit?: ProductUnitEnum;
  /**
   * 是否最新版本
   */
  isLatest?: boolean;
}
