<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top" label-width="120px">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="产品分类名称" prop="categoryName">
          <ProductCategorySelect v-model="form.categoryName" @onSelectCategory="handleSelectCategory($event)" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="型号名称" prop="modelName">
          <ProductModelSelect
            v-model="form.modelName"
            :placeholder="modelNamePlaceholder"
            :productCategoryId="state.productCategory?.id"
            @onSelectModel="handleSelectModel($event)"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="电压等级" prop="voltage">
          <el-input v-model="form.voltage" clearable placeholder="请输入电压等级" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格" prop="specification">
          <el-input v-model="form.specification" clearable placeholder="请输入规格" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="等效命名(别名)" prop="equivalentName">
          <el-input v-model="form.equivalentName" clearable placeholder="请输入等效命名(别名)" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" clearable type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IEquivalentForm, IProductCategory, IProductModel } from "@/models";
import ProductCategorySelect from "@/views/components/product-category/product-category-select.vue";
import ProductModelSelect from "@/views/components/model-select/index.vue";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  emptyFormValue
});

const form = reactive<IEquivalentForm>({});
const formRef = ref<FormInstance>();
const state = reactive<{
  productCategory?: IProductCategory;
  productModel?: IProductModel;
}>({});

const rules: FormRules = {
  categoryName: [{ required: true, trigger: "change", message: "产品分类名称不能为空" }],
  modelName: [{ required: true, trigger: "change", message: "型号名称不能为空" }],
  productCode: [{ required: true, trigger: "change", message: "产品Id不能为空" }],
  equivalentName: [{ required: true, trigger: "change", message: "等效命名(别名)不能为空" }],
  voltage: [{ trigger: "change", validator: checkVoltageSpecification }],
  specification: [{ trigger: "change", validator: checkVoltageSpecification }]
};

const modelNamePlaceholder = computed(() => {
  return form.categoryName ? "请选择产品型号" : "请选择产品分类";
});

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

function initFormValue(v: IEquivalentForm) {
  Object.assign(form, v);
}

function emptyFormValue() {
  formRef.value.resetFields();
}

function getFormValue() {
  return Object.assign({}, form);
}

const handleSelectCategory = (productCategory: IProductCategory) => {
  state.productCategory = productCategory;
  if (!productCategory) {
    state.productModel = undefined;
  }
};

const handleSelectModel = (productModel: IProductModel) => {
  state.productModel = productModel;
};

function checkVoltageSpecification(rule: any, value: string, callback: Function) {
  if (!form.voltage && !form.specification) {
    callback(new Error("电压或规格必须至少填写一项"));
  } else {
    formRef.value?.clearValidate(["voltage", "specification"]);
    callback();
  }
}
</script>
