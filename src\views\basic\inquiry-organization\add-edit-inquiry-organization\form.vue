<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="询价单位" prop="name">
          <el-input v-model="form.name" clearable placeholder="请输入询价单位" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="可见范围" prop="visibilityScope">
          <el-radio-group v-model="form.visibilityScope" @change="onVisibilityScopeChange()">
            <el-radio :label="InquiryOrganizationVisibilityScopeEnum.All" border>{{
              InquiryOrganizationVisibilityScopeEnumMapDesc[InquiryOrganizationVisibilityScopeEnum.All]
            }}</el-radio>
            <el-radio :label="InquiryOrganizationVisibilityScopeEnum.Department" border>{{
              InquiryOrganizationVisibilityScopeEnumMapDesc[InquiryOrganizationVisibilityScopeEnum.Department]
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item
          label="指定部门及子部门"
          prop="visibleDepartmentIds"
          :rules="[{ required: departmentRequired, message: '请选择指定部门及子部门', trigger: ['change', 'blur'] }]"
        >
          <el-tree-select
            clearable
            filterable
            multiple
            :check-strictly="true"
            class="w-full"
            placeholder="请选择指定部门及子部门"
            v-model="form.visibleDepartmentIds"
            :data="departmentTree"
            :render-after-expand="false"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" :rows="2" type="textarea" clearable placeholder="请输入备注" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IDepartmentTree, IInquiryOrganizationForm } from "@/models";
import { queryDepartmentTree } from "@/api/enterprise/department";
import { InquiryOrganizationVisibilityScopeEnumMapDesc, InquiryOrganizationVisibilityScopeEnum } from "@/enums";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  emptyFormValue
});

const form = reactive<IInquiryOrganizationForm>({});
const formRef = ref<FormInstance>();
const departmentTree = ref<Array<IDepartmentTree>>();
const departmentRequired = ref(false);
const rules: FormRules = {
  name: [{ required: true, trigger: "change", message: "询价单位不能为空" }],
  visibilityScope: [{ required: true, trigger: "change", message: "请选择可见范围" }]
};

onMounted(async () => {
  const { data } = await queryDepartmentTree();
  departmentTree.value = data;
});
/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

const onVisibilityScopeChange = () => {
  form.visibleDepartmentIds = [];
  formRef.value.clearValidate(["visibleDepartmentIds"]);
  departmentRequired.value = form.visibilityScope === InquiryOrganizationVisibilityScopeEnum.Department;
};

/**
 * @description: 初始化表单
 */
function initFormValue(v: IInquiryOrganizationForm) {
  Object.assign(form, v);
}

function emptyFormValue() {
  formRef.value.resetFields();
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}
</script>

<style scoped></style>
