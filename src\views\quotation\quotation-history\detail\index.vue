<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color px-6 pt-3 pb-4">
      <div class="flex items-center">
        <div class="flex justify-between items-center" @click="onBack()">
          <el-icon color="#30313"><Back /></el-icon>
          <div class="back">返回</div>
        </div>
        <div class="quotation-no">报价记录：{{ state.quotationInquiryProgress?.quotationBatchNo }}</div>
        <ElButton
          class="absolute right-10"
          v-if="state.showAgainQuotation"
          :icon="Download"
          type="primary"
          v-auth="PermissionKey.quotation.quotationManagementExport"
          @click="onQuotationInquiryExport()"
          >导出报价单</ElButton
        >
      </div>
    </div>

    <div v-show="!state.showAgainQuotation" class="flex flex-col flex-1">
      <div class="flex bg-bg_color px-6 items-center justify-between pb-3">
        <el-descriptions class="risk-info" :column="5">
          <el-descriptions-item class-name="value" label-class-name="label" label="询价时间">{{
            state.quotationInquiryProgress?.inquiryTime
          }}</el-descriptions-item>
          <el-descriptions-item class-name="value" label-class-name="label" label="询价单位">{{
            state.quotationInquiryProgress?.inquiryOrgName
          }}</el-descriptions-item>
          <el-descriptions-item class-name="value" label-class-name="label" label="销售人员"
            ><CxEmployee :name="state.quotationInquiryProgress?.operatorName"
          /></el-descriptions-item>
          <el-descriptions-item class-name="value" label-class-name="label" label="产品单价版本">
            <div class="text-primary">{{ state.quotationInquiryProgress?.productPriceVersionNumber }}</div>
          </el-descriptions-item>

          <el-descriptions-item class-name="value" label-class-name="label" label="token用量">
            <div class="text-primary">
              {{ formatThousands(state.quotationInquiryProgress?.parseTokenUsage || "0") }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item class-name="value" label-class-name="label" label="报价单">
            <div
              class="flex items-center gap-1 cursor-pointer"
              v-if="state.quotationInquiryProgress?.quotationAttachmentId"
            >
              <img class="w-4 h-4" :src="ExcelIcon" />

              <FileViewAndDownload
                :file-name="state.quotationInquiryProgress?.quotationAttachmentName"
                :file-id="state.quotationInquiryProgress?.quotationAttachmentId"
                :file-url="state.quotationInquiryProgress?.quotationAttachmentUrl"
              >
                <CXDownload
                  :name="state.quotationInquiryProgress?.quotationAttachmentName"
                  :id="state.quotationInquiryProgress?.quotationAttachmentId"
                />
              </FileViewAndDownload>
            </div>
          </el-descriptions-item>
          <el-descriptions-item :span="4" class-name="inquiry-attachmen" label-class-name="label" label="询价单">
            <div class="flex items-center gap-1 cursor-pointer">
              <img class="w-4 h-4" :src="ExcelIcon" />

              <FileViewAndDownload
                v-for="item in state.quotationInquiryProgress?.inquiryAttachmentList"
                :key="item.inquiryAttachmentId"
                :id="item.inquiryAttachmentId"
                :file-name="item.inquiryAttachmentName"
                :file-id="item.inquiryAttachmentId"
                :file-url="item.inquiryAttachmentUrl"
              >
                <CXDownload :name="item.inquiryAttachmentName" :key="item.inquiryAttachmentId" />
              </FileViewAndDownload>
            </div>
          </el-descriptions-item>
        </el-descriptions>
        <template v-if="isCurrentAccount">
          <ElButton
            v-auth="PermissionKey.quotation.quotationHistoryrReQuote"
            v-if="parsed"
            type="primary"
            @click="onContinueEdit()"
            >继续编辑</ElButton
          >
          <ElButton
            v-auth="PermissionKey.quotation.quotationHistoryrReQuote"
            v-if="parsed"
            type="primary"
            @click="onAgainQuotation()"
            >重新报价</ElButton
          >
        </template>
      </div>
      <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative" v-loading="loading">
        <div class="detail">报价明细</div>
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              header-class="cx-quotation_header cx-quotation_header-original-row"
              :columns="columns"
              :data="state.list"
              :width="width"
              :height="height"
              row-key="id"
              fixed
            >
              <template #cell="{ column, rowData, rowIndex }">
                <template v-if="column.key === 'rowIndex'"> {{ rowIndex + 1 }} </template>
                <template v-else-if="column.key === 'modelName'">
                  <el-tooltip
                    class="box-item"
                    :effect="
                      `${rowData.modelName}${rowData.voltageLevel}` ===
                      `${rowData.aiParseModel}${rowData.aiParseVoltage}`
                        ? 'dark'
                        : 'customized'
                    "
                    :show-after="100"
                    :content="`${rowData.aiParseModel}-${rowData.aiParseVoltage}`"
                    placement="top"
                  >
                    <template #content>
                      <div>
                        <template v-if="rowData.aiParseModel">{{ rowData.aiParseModel }}</template>
                        <template v-if="rowData.aiParseModel && rowData.aiParseVoltage">-</template>
                        <template v-if="rowData.aiParseVoltage">{{ rowData.aiParseVoltage }}</template>
                      </div>
                    </template>
                    <div>
                      <template v-if="rowData.modelName">{{ rowData.modelName }}</template>
                      <template v-if="rowData.modelName && rowData.voltageLevel">-</template>
                      <template v-if="rowData.voltageLevel">{{ rowData.voltageLevel }}</template>
                    </div>
                  </el-tooltip>
                </template>
                <template v-else-if="column.key === 'excelRowIndex'">
                  <div>{{ rowData.excelSheet }}-{{ rowData.excelRowIndex }}</div>
                </template>
                <template v-else-if="column.key === 'priceMatchRecord'">
                  <el-popover placement="top" :show-after="100" :popper-style="{ width: 'auto' }">
                    <template #reference>
                      <div
                        class="columnRawContent text-ellipsis line-clamp-2 pr-1"
                        :class="{ 'match-no-price': !rowData.isMatchPrice }"
                      >
                        {{ rowData.priceMatchRecord }}
                      </div>
                    </template>
                    <div class="max-w-[500px] leading-5">{{ rowData.priceMatchRecord }}</div>
                  </el-popover>
                </template>
                <template v-else-if="column.key === 'columnRawContent'">
                  <el-popover placement="top" :show-after="100" :popper-style="{ width: 'auto' }">
                    <template #reference>
                      <div class="columnRawContent text-ellipsis line-clamp-2 pr-1">
                        {{ rowData.columnRawContent }}
                      </div>
                    </template>
                    <div class="max-w-[500px] leading-5">{{ rowData.columnRawContent }}</div>
                  </el-popover>
                </template>

                <template v-else-if="column.key === 'matchStatus'">
                  <span v-if="rowData.matchStatus === MatchStatusEnum.NOT_NEED_MATCH">--</span>
                  <template v-else>
                    <CxTag v-if="rowData.matchStatus" :type="MatchStatusEnumMapColor[rowData.matchStatus]">{{
                      MatchStatusEnumMapDesc[rowData.matchStatus]
                    }}</CxTag>
                  </template>
                </template>
                <template v-else>
                  {{ rowData[column.key] }}
                </template>
              </template>
              <template #empty>
                <CxEmptyData />
              </template>
            </el-table-v2>
          </template>
        </el-auto-resizer>
      </div>
      <ProductVersionDrawer
        v-model="state.productVersionDrawerVisible"
        :versionId="state.quotationInquiryProgress?.productPriceVersionId"
        @onSelectProductVersion="handleSelectProductVersion($event)"
      />
    </div>
    <template v-if="state.buildAgainQuotation">
      <AgainQuotation
        ref="againQuotationRef"
        :task-id="state.taskId"
        v-show="state.showAgainQuotation"
        @onQuotationInquiryExport="handleQuotationInquiryExport($event)"
      />
    </template>
  </div>
</template>

<script setup lang="ts" name="riskRecord">
import { onMounted, ref, reactive, computed, nextTick } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IProductVersion, IQuotationInquiryDetail, IQuotationInquiryExport, IQuotationInquiryProgress } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import CxEmployee from "@/components/cx-employee/index.vue";
import { Back, Download } from "@element-plus/icons-vue";
import ExcelIcon from "@/assets/img/excel-icon.png";
import ProductVersionDrawer from "@/views/components/product-info-history/drawer.vue";
import AgainQuotation from "../again-quotation/index.vue";
import { useRoute, useRouter } from "vue-router";
import {
  getQuotationInquiryParseDetailList,
  getQuotationInquiryDetail,
  quotationInquiryExport,
  reQuoteByVersion
} from "@/api/quotation/quotation-inquiry";
import { MatchStatusEnumMapColor, MatchStatusEnumMapDesc, TaskParseStatusEnum } from "@/enums";
import CxTag from "@/components/CxTag/index.vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import CXDownload from "@/components/cx-download/index.vue";
import { useConfirm } from "@/utils/useConfirm";
import { PermissionKey } from "@/consts/permission-key";
import { useUserStore } from "@/store/modules/user";
import { formatThousands } from "@/utils/format";
import { MatchStatusEnum } from "@/enums";
import FileViewAndDownload from "@/views/components/file-view-download/index.vue";

const route = useRoute();
const router = useRouter();
const loading = ref(false);
let reQuote = false;
const { columns } = useColumns();
const userStore = useUserStore();
const againQuotationRef = ref<InstanceType<typeof AgainQuotation>>();
let quotationInquiryExportParams: IQuotationInquiryExport;
const state = reactive<{
  list: Array<IQuotationInquiryDetail>;
  params: { [key: string]: string };
  id?: string;
  taskId?: string;
  riskRecordHandleDialogVisible: boolean;
  productVersionDrawerVisible: boolean;
  selectProductVersion?: IProductVersion;
  showAgainQuotation: boolean;
  buildAgainQuotation: boolean;
  quotationInquiryProgress: IQuotationInquiryProgress;
}>({
  list: [],
  params: {},
  riskRecordHandleDialogVisible: false,
  productVersionDrawerVisible: false,
  showAgainQuotation: false,
  buildAgainQuotation: false,
  quotationInquiryProgress: {}
});

const parsed = computed(() => state.quotationInquiryProgress?.parseStatus === TaskParseStatusEnum.PARSED);
const isCurrentAccount = computed(() => userStore.profile?.id === state.quotationInquiryProgress?.operatorId);

onMounted(() => {
  state.taskId = route.params.id as string;
  handleGetQuotationInquiryDetail();
  handleGetQuotationInquiryParseDetailList();
});

const onBack = async () => {
  if (!state.showAgainQuotation) {
    router.back();
    return;
  }
  state.showAgainQuotation = false;
  if (reQuote) {
    await handleGetQuotationInquiryParseDetailList();
  }
};

const handleSelectProductVersion = async (data: IProductVersion) => {
  loading.value = true;
  await reQuoteByVersion({ taskId: state.taskId, versionId: data.id });
  // await handleGetQuotationInquiryParseDetailList();
  state.buildAgainQuotation = true;
  state.showAgainQuotation = true;
  reQuote = true;
  state.selectProductVersion = data;
  nextTick(() => {
    againQuotationRef.value?.quotationInquiryParseDetailList();
    loading.value = false;
  });

  await handleGetQuotationInquiryDetail();
};

const onAgainQuotation = () => {
  state.productVersionDrawerVisible = true;
};

const onContinueEdit = () => {
  router.push(`/quotation/management?taskId=${state.taskId}`);
};

const handleGetQuotationInquiryParseDetailList = useLoadingFn(async () => {
  const { data } = await getQuotationInquiryParseDetailList(state.taskId);
  if (Array.isArray(data) && data.length) {
    state.list = data;
  }
}, loading);

const handleGetQuotationInquiryDetail = async () => {
  const { data } = await getQuotationInquiryDetail(state.taskId);
  state.quotationInquiryProgress = data;
};

const handleQuotationInquiryExport = (data: IQuotationInquiryExport) => {
  quotationInquiryExportParams = data;
};

const onQuotationInquiryExport = async () => {
  if (!(await useConfirm("是否导出报价单", "导出报价单"))) {
    return;
  }

  await quotationInquiryExport(quotationInquiryExportParams);
  ElMessage.success("报价单导出中");
};
</script>

<style lang="scss" scoped>
.back {
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0;
  color: #909399;
  padding: 0 10px 0 12px;
  position: relative;
  cursor: pointer;
  margin-right: 25px;

  &::after {
    content: "";
    position: absolute;
    height: 15px;
    top: 3px;
    width: 2px;
    right: 0;
    background-color: #909399;
  }
}

.quotation-no {
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
  letter-spacing: 0;
  color: #303133;
  margin-right: 10px;
}

.detail {
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
  letter-spacing: 0;
  color: #303133;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.risk-info {
  flex: 1;

  :deep(.el-descriptions__body) {
    .el-descriptions__table {
      tbody {
        tr {
          td {
            width: 20% !important;

            .label {
              font-size: 14px;
              font-weight: normal;
              line-height: 22px;
              letter-spacing: 0;
              color: #303133;
              margin-right: 16px;
            }

            .value {
              font-size: 14px;
              line-height: 22px;
              letter-spacing: 0;
              color: #606266;

              &.total {
                color: #f56c6c;
              }
            }

            .inquiry-attachmen {
              width: 100%;
            }
          }
        }

        tr:nth-of-type(2) {
          display: block;
          padding-top: 15px;

          td:nth-of-type(2) {
            width: 80% !important;
          }
        }
      }
    }
  }
}

:deep(.cx-quotation_header) {
  .el-table-v2__header-cell {
    background-color: #fafbfc;
    color: #303339;
    font-size: 14px;
    font-weight: 500;
  }
}

:deep(.cx-quotation_header-original-row) {
  .el-table-v2__header-cell[data-key="excelSheet"],
  .el-table-v2__header-cell[data-key="excelRowIndex"],
  .el-table-v2__header-cell[data-key="columnRawContent"] {
    background-color: var(--el-color-warning-light-9);
  }
}

.quotation-grid {
  :deep(.el-table-v2__empty) {
    margin-top: 20px;
    height: calc(100% - 70px) !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f5;
  }
}

.match-no-price {
  color: var(--el-color-warning);
}
</style>

<style>
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}
</style>
