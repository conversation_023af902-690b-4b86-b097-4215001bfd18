<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="right">
    <TitleBar title="请选择需要初始化的数据项" class="mb-3" />
    <el-row :gutter="40" class="mb-3">
      <el-col :span="24">
        <el-checkbox-group v-model="form.initDataItemList">
          <el-row>
            <el-checkbox label="ROLE_PERMISSION">试用角色权限</el-checkbox>
          </el-row>
          <el-row>
            <el-checkbox label="INQUIRY_ORGANIZATION">询价单位</el-checkbox>
          </el-row>
          <el-row>
            <el-checkbox label="QUOTATION_TEMPLATE">报价单模板</el-checkbox>
          </el-row>
          <el-row>
            <el-checkbox label="PRODUCT_PRICE">试用产品单价信息</el-checkbox>
          </el-row>
        </el-checkbox-group>
      </el-col>
    </el-row>
    <TitleBar title="实施支持人员" class="mb-3" />
    <el-row :gutter="40" class="mb-3">
      <el-col :span="24"><b>把相关人员账号添加到对应企业租户下，账号类型默认设为 技术支持，角色配置为管理员</b></el-col>
    </el-row>
    <el-row :gutter="40" class="mb-3">
      <el-col :span="24">
        <el-form-item label="" prop="contactName">
          <el-select
            v-model="form.userIdList"
            multiple
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择实施支持人员"
            style="width: 240px"
          >
            <el-option
              v-for="item in accountStore.userListSample"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import TitleBar from "@/components/TitleBar";
import { useAccountStore } from "@/store/modules";

interface FormType {
  targetTenantId: string;
  sourceTenantId: string;
  initDataItemList: Array<string>;
  userIdList: Array<string>;
}

const accountStore = useAccountStore();

const form = reactive<FormType>({
  targetTenantId: "",
  sourceTenantId: "",
  initDataItemList: [],
  userIdList: []
});
const formRef = ref<FormInstance>();

const rules: FormRules = {};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

function getUserListSimple() {
  accountStore.listAllSimple();
}

onMounted(async () => {
  await getUserListSimple();
});

defineExpose({
  validateForm,
  getFormValue
});
</script>

<style scoped></style>
