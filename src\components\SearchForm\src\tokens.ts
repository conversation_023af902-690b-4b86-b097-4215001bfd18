import { Injection<PERSON><PERSON>, Ref } from "vue";
import { I<PERSON>eywordField } from "@/components/SearchForm";
import { ButtonInstance } from "element-plus";

interface ISearchFormContext {
  placeholder: string;
  keywordFields: Array<IKeywordField>;
  searchCb: () => void;
  searchBtnRef: Ref<ButtonInstance | undefined>;
}

export const searchFormToken: InjectionKey<ISearchFormContext> = Symbol("SearchForm");
