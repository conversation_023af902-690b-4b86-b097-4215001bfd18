import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

export enum RiskStatusEnum {
  ALLL = -1,

  /** 未处理 */
  UNDISPOSED = 0,

  /** 已处理 */
  DISPOSED = 1
}

export const RiskStatusEnumMapDesc: Record<RiskStatusEnum, string> = {
  [RiskStatusEnum.ALLL]: "全部",
  [RiskStatusEnum.UNDISPOSED]: "未处理",
  [RiskStatusEnum.DISPOSED]: "已处理"
};

export const RiskStatusEnumMapColor: Record<RiskStatusEnum, string> = {
  [RiskStatusEnum.ALLL]: "",
  [RiskStatusEnum.UNDISPOSED]: "danger",
  [RiskStatusEnum.DISPOSED]: "success"
};

export const RiskStatusEnumMapOptions: Array<IOption> = mapDescToOptions(RiskStatusEnumMapDesc);
