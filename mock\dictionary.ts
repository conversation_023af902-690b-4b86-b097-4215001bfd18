import { IDictionary, IDictionaryOption } from "@/models";
import { MockMethod } from "vite-plugin-mock";

const dict = [
  {
    id: "1",
    name: "计量单位",
    code: "01",
    remarks: "计量单位"
  },
  {
    id: "2",
    name: "电压等级",
    code: "02",
    remarks: "电压等级"
  }
];

const dictOp: Array<IDictionaryOption> = [];

export default [
  {
    url: "/api/dict",
    method: "get",
    response: () => {
      return {
        code: "",
        msg: "",
        data: dict
      };
    }
  },
  {
    url: "/api/dict",
    method: "post",
    response: ({ body }) => {
      dict.push({ id: `${Math.ceil(Math.random() * 10)}`, ...(body as IDictionary) });
      return {
        code: "",
        msg: "",
        data: true
      };
    }
  },
  {
    url: "/api/dict",
    method: "put",
    response: ({ body }) => {
      const idx = dict.findIndex(x => x.id === body.id);
      dict[idx] = body as IDictionary;
      return {
        code: "",
        msg: "",
        data: true
      };
    }
  },
  {
    url: "/api/dict-op",
    method: "post",
    response: ({ body }) => {
      dictOp.push({ id: `${Math.ceil(Math.random() * 10)}`, ...(body as IDictionaryOption) });
      return {
        code: "",
        msg: "",
        data: true
      };
    }
  },
  {
    url: "/api/dict-op",
    method: "put",
    response: ({ body }) => {
      const idx = dictOp.findIndex(x => x.id === body.id);
      dictOp[idx] = body as IDictionaryOption;
      return {
        code: "",
        msg: "",
        data: true
      };
    }
  },
  {
    url: "/api/dict-op",
    method: "get",
    response: ({ query }) => {
      return {
        code: "",
        msg: "",
        data: dictOp.filter(x => x.parentCode === query.code)
      };
    }
  }
] as MockMethod[];
