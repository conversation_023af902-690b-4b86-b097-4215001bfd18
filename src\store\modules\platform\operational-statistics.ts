import { defineStore } from "pinia";
import * as api from "@/api/platform/operational-statistics";
import { OperationalStatistics, OperationalStatisticsDetail } from "@/models/platform/operational-statistics";

export const useOperationalStatisticsStore = defineStore({
  id: "cx-operational-statistics",
  state: () => ({
    operationalStatisticsList: [] as Array<OperationalStatistics>,
    operationalStatisticsDetailList: [] as Array<OperationalStatisticsDetail>,
    loading: false as boolean
  }),
  actions: {
    async queryOperationalStatistics() {
      this.loading = true;
      const res = await api.queryOperationalStatistics();
      this.operationalStatisticsList = res.data;
      this.loading = false;
    },
    async queryOperationalStatisticsLiat() {
      this.loading = true;
      const res = await api.queryOperationalStatisticsList();
      this.operationalStatisticsDetailList = res.data;
      this.loading = false;
    }
  }
});
