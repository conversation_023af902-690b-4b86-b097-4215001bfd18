<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="right"
    label-width="110px"
    require-asterisk-position="left"
  >
    <el-row>
      <el-col>
        <el-form-item label="编码" prop="code">
          <el-input placeholder="请输入编码" v-model="form.code" clearable :disabled="disabled" />
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item label="字典类型名称" prop="name">
          <el-input placeholder="请输入字典类型名称" v-model="form.name" clearable />
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item label="备注" prop="remarks">
          <el-input
            clearable
            v-model="form.remarks"
            type="textarea"
            placeholder="请输入备注"
            :rows="2"
            :maxlength="200"
            :show-word-limit="true"
            resize="none"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IDictionaryForm } from "@/models";
import { useDictionaryStore } from "@/store/modules";

defineExpose({
  validate,
  getValidValue
});

const dictionaryStore = useDictionaryStore();

const formRef = ref<FormInstance>();
const disabled = ref<boolean>();

const form = reactive<IDictionaryForm>({
  id: undefined,
  name: undefined,
  code: undefined,
  remarks: undefined
});
const rules = reactive<FormRules>({
  name: [{ required: true, message: requiredMessage("字典类型名称"), trigger: "change" }],
  code: [{ required: true, message: requiredMessage("编码"), trigger: "change" }]
});

watchEffect(() => {
  if (dictionaryStore.dictionaryForm && Object.keys(dictionaryStore.dictionaryForm).length) {
    Object.assign(form, dictionaryStore.dictionaryForm);
  }
  disabled.value = !!dictionaryStore.dictionaryForm?.id;
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IDictionaryForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}
</script>

<style scoped></style>
