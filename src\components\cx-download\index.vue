<template>
  <div class="flex items-center">
    <div class="flex items-center gap-1 cursor-pointer">
      <img v-if="props.iconType" :style="{ width: props.width, height: props.height }" :src="ExcelIcon" />
      <span class="text-primary" @click="onDownload()">{{ props.name }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { downLoadFile } from "@/api";
import ExcelIcon from "@/assets/img/excel-icon.png";
import { downloadByData } from "@pureadmin/utils";
const props = withDefaults(
  defineProps<{
    iconType?: string;
    name?: string;
    downloadUrl?: string;
    width?: string;
    height?: string;
    id?: string;
  }>(),
  {
    width: "16px",
    height: "16px"
  }
);

const onDownload = async () => {
  const blob = await downLoadFile(props.id);
  downloadByData(blob, props.name, blob.type);
};
</script>
