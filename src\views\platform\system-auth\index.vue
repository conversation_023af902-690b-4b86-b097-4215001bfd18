<template>
  <div class="p-4 overflow-hidden h-full flex flex-col">
    <div class="text-right mb-2">
      <el-button type="primary" size="large" :icon="Plus" @click="onAddAuth()">添加授权</el-button>
    </div>
    <el-scrollbar>
      <TenantAuth
        :businessLicense="item"
        v-for="item in state.authTenantList"
        :key="item.tenantId"
        class="mb-4"
        @onAuthSuccess="handleAuthSuccess()"
      />
    </el-scrollbar>
  </div>

  <el-dialog
    v-model="addAuthDialogVisible"
    title="添加授权"
    width="35%"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    :close-on-click-modal="false"
  >
    <AuthForm @onCancelAuth="handleCancelAuth()" @onAuthSuccess="handleAuthSuccess()" />
  </el-dialog>
</template>

<script setup lang="ts">
import { usePageStoreHook } from "@/store/modules/page";
import TenantAuth from "./tenant-auth.vue";
import { Plus } from "@element-plus/icons-vue";
import AuthForm from "./auth-form.vue";
import { useSystemAuthStore } from "@/store/modules";
import { onMounted, reactive, ref } from "vue";
import { IBusinessLicense } from "@/models";

usePageStoreHook().setTitle("系统授权");

const state = reactive<{
  authTenantList: Array<IBusinessLicense>;
}>({
  authTenantList: []
});
const addAuthDialogVisible = ref<boolean>();

const systemAuthStore = useSystemAuthStore();

onMounted(async () => {
  queryAuthTenantList();
});

const onAddAuth = () => {
  addAuthDialogVisible.value = true;
};

const handleCancelAuth = () => {
  addAuthDialogVisible.value = false;
};

const handleAuthSuccess = () => {
  queryAuthTenantList();
  addAuthDialogVisible.value = false;
};

const queryAuthTenantList = async () => {
  state.authTenantList = (await systemAuthStore.queryAuthTenantList()).data;
};
</script>

<style scoped lang="scss"></style>
