<template>
  <div class="h-[55vh] flex flex-col">
    <div class="flex header">
      <div class="select-field">选择产品信息</div>
      <div class="pl-3">修改产品信息</div>
    </div>
    <div class="flex flex-1">
      <div class="flex flex-col select-field">
        <el-checkbox
          v-for="item in fields"
          :key="item.key"
          v-model="item.checked"
          :label="item.name"
          @change="onChange()"
        />
      </div>
      <div class="form w-full">
        <el-form ref="formRef" :model="form" label-position="top">
          <el-row :gutter="20">
            <el-col :span="24" v-if="onCheckFieldVisbile('modelName')">
              <el-form-item
                label="型号"
                prop="modelName"
                class="flex"
                :rules="[
                  {
                    required: modelNameRequired,
                    message: '型号名称不能为空',
                    trigger: 'change'
                  }
                ]"
              >
                <div class="flex justify-between w-full">
                  <el-input class="flex-1" v-model="form.modelName" clearable placeholder="请输入型号" />
                  <el-button @click="onSelectProductDialogVisible()">选择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="onCheckFieldVisbile('voltageLevel')">
              <el-form-item label="电压等级" prop="voltageLevel">
                <el-input v-model="form.voltageLevel" clearable placeholder="请输入电压等级" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="onCheckFieldVisbile('specification')">
              <el-form-item
                label="规格"
                prop="specification"
                :rules="[
                  {
                    required: specificationRequired,
                    message: '规格不能为空',
                    trigger: 'change'
                  }
                ]"
              >
                <el-input v-model="form.specification" clearable placeholder="请输入规格" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="onCheckFieldVisbile('unit')">
              <el-form-item label="单位" prop="unit">
                <el-input v-model="form.unit" clearable placeholder="请输入单位" />
              </el-form-item>
            </el-col>

            <el-col :span="12" v-if="onCheckFieldVisbile('quantity')">
              <el-form-item label="数量" prop="quantity">
                <el-input-number class="!w-full" v-model="form.quantity" :controls="false" placeholder="请输入数量" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="onCheckFieldVisbile('discountBeforeUnitPrice')">
              <el-form-item label="折扣前单价" prop="discountBeforeUnitPrice">
                <el-input-number
                  class="!w-full"
                  v-model="form.discountBeforeUnitPrice"
                  :controls="false"
                  :precision="2"
                  placeholder="请输入折扣前单价"
                />
              </el-form-item>
            </el-col>
            <template v-if="onCheckFieldVisbile('batchDiscount')">
              <el-col :span="12">
                <el-form-item label="折扣1" prop="discount1">
                  <el-input-number
                    class="!w-full"
                    v-model="form.discount1"
                    :controls="false"
                    placeholder="请输入折扣1"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="折扣2" prop="discount2">
                  <el-input-number
                    class="!w-full"
                    v-model="form.discount2"
                    :controls="false"
                    placeholder="请输入折扣2"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="折扣3" prop="discount3">
                  <el-input-number
                    class="!w-full"
                    v-model="form.discount3"
                    :controls="false"
                    placeholder="请输入折扣3"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="折扣4" prop="discount4">
                  <el-input-number
                    class="!w-full"
                    v-model="form.discount4"
                    :controls="false"
                    placeholder="请输入折扣4"
                  />
                </el-form-item>
              </el-col>
            </template>
          </el-row>
          <CategoryProductSelectDialog
            v-model="selectProductDialogVisible"
            @onSelectProductInfo="handleSelectProductInfo($event)"
          />
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IProductInfo, IQuotationInquiryDetail } from "@/models";
import { ElMessage, FormInstance } from "element-plus";
import { computed, reactive, ref } from "vue";
import CategoryProductSelectDialog from "@/views/components/category-product-select/dialog.vue";

interface IField {
  key: string;
  checked: boolean;
  name: string;
}

defineExpose({
  validateForm,
  getFormValue
});

const fields = ref<Array<IField>>([
  {
    key: "modelName",
    checked: true,
    name: "型号"
  },
  {
    key: "voltageLevel",
    checked: true,
    name: "电压"
  },
  {
    key: "specification",
    checked: false,
    name: "规格"
  },
  {
    key: "unit",
    checked: false,
    name: "单位"
  },
  {
    key: "quantity",
    checked: false,
    name: "数量"
  },
  {
    key: "discountBeforeUnitPrice",
    checked: false,
    name: "折扣前单价"
  },
  {
    key: "batchDiscount",
    checked: false,
    name: "批量折扣"
  }
]);

const form = reactive<IQuotationInquiryDetail>({});
const formRef = ref<FormInstance>();
const selectProductDialogVisible = ref();
const modelNameRequired = computed(() => fields.value.find(x => x.key === "modelName")?.checked);
const specificationRequired = computed(() => fields.value.find(x => x.key === "specification")?.checked);

const onSelectProductDialogVisible = () => {
  selectProductDialogVisible.value = true;
};

const handleSelectProductInfo = (data: IProductInfo) => {
  const { product } = data;
  form.modelName = product.model?.modelName;
};

function onCheckFieldVisbile(fieldKey: string) {
  return fields.value.find(x => x.key === fieldKey)?.checked;
}

function onChange() {
  if (fields.value.every(x => !x.checked)) {
    ElMessage.warning("请选择需要修改的字段");
  }
}

async function validateForm() {
  if (fields.value.every(x => !x.checked)) {
    ElMessage.warning("请选择需要修改的字段");
    return false;
  }

  if (!formRef.value) {
    return false;
  }

  return await formRef.value.validate();
}

function getFormValue() {
  const selecteFields = fields.value.filter(x => x.checked);
  const formValue = selecteFields.reduce((acc, cur) => {
    if (cur.key === "batchDiscount") {
      ["discount1", "discount2", "discount3", "discount4"].forEach(key => {
        acc[key] = form[key] || null;
      });
    } else {
      acc[cur.key] = form[cur.key] || null;
    }
    return acc;
  }, {});
  return formValue;
}
</script>

<style scoped lang="scss">
.header {
  height: 30px;
}

.select-field {
  width: 120px;
  border-right: 0.0625rem solid #dcdfe6;
}
</style>
