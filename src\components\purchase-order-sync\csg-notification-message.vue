<template>
  <div class="flex-ac gap-[10px] w-full">
    <ProgressFail v-if="status.value === PurchaseOrderSyncStatus.FAIL" />
    <ProgressSuccess v-else-if="status.value === PurchaseOrderSyncStatus.SUCCESS" />
    <ProgressRunning v-else />
    <div class="flex-1 flex flex-col gap-[10px]">
      <p class="text-base text-primaryText">{{ tips.value }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PurchaseOrderSyncStatus } from "@/enums";
import ProgressRunning from "@/assets/svg/progress_running.svg?component";
import ProgressFail from "@/assets/svg/progress_fail.svg?component";
import ProgressSuccess from "@/assets/svg/progress_success.svg?component";
import { Ref } from "vue";

defineProps<{
  tips: Ref<string>;
  status: Ref<PurchaseOrderSyncStatus>;
}>();
</script>

<style scoped lang="scss">
:deep(.el-progress-bar__inner) {
  $color: rgba(255, 255, 255, 0.2);
  background: repeating-linear-gradient(-60deg, $color 0, $color 2px, transparent 2px, transparent 8px) no-repeat bottom
    var(--el-color-primary);
}
</style>
