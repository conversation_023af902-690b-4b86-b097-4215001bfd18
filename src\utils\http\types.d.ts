import { Method, AxiosError, AxiosResponse, AxiosRequestConfig } from "axios";

export type resultType = {
  accessToken?: string;
};

export type RequestMethods = Extract<Method, "get" | "post" | "put" | "delete" | "patch" | "option" | "head">;

export type RequestDuplicateKey = Symbol | string;

export interface PureHttpError extends AxiosError {
  isCancelRequest?: boolean;
}

export interface PureHttpResponse extends AxiosResponse {
  config: PureHttpRequestConfig;
}

export interface PureHttpRequestConfig extends AxiosRequestConfig {
  beforeRequestCallback?: (request: PureHttpRequestConfig) => void;
  beforeResponseCallback?: (response: PureHttpResponse) => void;
  silentError?: boolean;
  duplicateKey?: RequestDuplicateKey;
  /** show message in dialog body or use a element */
  showErrorInDialog?: boolean | string | HTMLElement;
  skipAccessToken?: boolean;
}

export default class PureHttp {
  request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T>;
  post<T, P>(url: string, params?: T, config?: PureHttpRequestConfig): Promise<P>;
  get<T, P>(url: string, params?: T, config?: PureHttpRequestConfig): Promise<P>;
}
