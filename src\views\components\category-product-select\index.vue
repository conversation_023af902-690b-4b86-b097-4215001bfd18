<template>
  <div class="overflow-hidden w-full flex flex-1 h-[70vh]">
    <div class="bg-bg_color w-[220px]">
      <ProductCategoryList defaultSelected ref="categoryRef" @onSelectCategory="handleSelectCategory($event)" />
    </div>
    <div class="overflow-hidden flex-1 flex flex-col ml-5">
      <div class="bg-bg_color flex justify-between pr-5">
        <ElForm :inline="true" :model="state.params" class="flex-1">
          <ElFormItem label="型号：">
            <ElInput class="!w-[200px]" clearable v-model="state.params.modelName" placeholder="请输入型号" />
          </ElFormItem>
          <ElFormItem label="规格：">
            <ElInput class="!w-[200px]" clearable v-model="state.params.specification" placeholder="请输入规格" />
          </ElFormItem>

          <ElFormItem>
            <ElButton type="primary" @click="onQuery()">搜索</ElButton>
            <ElButton @click="onResetQuery()">重置</ElButton>
          </ElFormItem>
        </ElForm>
      </div>

      <div class="bg-bg_color flex flex-col flex-1 overflow-hidden relative">
        <div class="mb-5 h-[24px] flex items-center justify-between">
          <div>
            <span class="text-base mr-2">选中分类：</span>
            <CXTag v-if="state.selectCategory?.id" type="primary">{{ state.selectCategory?.name }}</CXTag>
          </div>
        </div>
        <PureTable
          class="flex-1 overflow-hidden pagination"
          row-key="id"
          :data="state.list"
          :columns="columns"
          size="large"
          :loading="loading"
          showOverflowTooltip
          v-model:pagination="pagination"
          highlight-current-row
          :row-style="{ cursor: 'pointer' }"
          @current-change="onCurrentChange($event)"
          @page-current-change="onPageCurrentChange"
          @page-size-change="onPageSizeChange"
        >
          <template #selection="{ row }">
            <el-radio-group v-model="row.selection" @change="onChange(row.id)">
              <el-radio :label="true">{{ "" }}</el-radio>
            </el-radio-group>
          </template>
          <template #categoryName="{ row }"> {{ row.product?.model?.category?.name }} </template>
          <template #productCode="{ row }"> {{ row.product?.productCode }} </template>
          <template #modelName="{ row }"> {{ row.product?.modelName }} </template>
          <template #voltage="{ row }"> {{ row.product?.model?.voltage }} </template>
          <template #crossSection="{ row }"> {{ row.product?.crossSection }} </template>
          <template #unit="{ row }"> {{ row.product?.unit }} </template>
          <template #specification="{ row }"> {{ row.product?.specification }} </template>

          <template #empty>
            <CxEmptyData />
          </template>
        </PureTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="product-info">
import { ref, reactive, onMounted, watch } from "vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryProductInfo } from "@/api/basic/product-info";
import { IProductCategory, IProductInfo, IProductInfoReq, IProductVersion } from "@/models";
import { ElButton } from "element-plus";
import ProductCategoryList from "@/views/components/product-category/product-category-list.vue";
import CXTag from "@/components/CxTag/index.vue";
import { getLatestVersion } from "@/api/product-version";

interface IProductInfoExt extends IProductInfo {
  selection?: boolean;
}

const emit = defineEmits<{
  (e: "onSelectProductInfo", data: IProductInfo): void;
}>();

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const categoryRef = ref<InstanceType<typeof ProductCategoryList>>();
const state = reactive<{
  list: Array<IProductInfoExt>;
  params: IProductInfoReq;
  selectCategory?: IProductCategory;
  productVersionDrawerVisible: boolean;
  productVersion?: IProductVersion;
  fileImportDialogVisible: boolean;
}>({
  list: [],
  params: {},
  productVersionDrawerVisible: false,
  fileImportDialogVisible: false,
  productVersion: {}
});

onMounted(() => {
  handleGetLatestVersion();
});

watch([() => state.productVersion?.id, () => state.selectCategory?.id], ([latestVersionId, productCategoryId]) => {
  if (latestVersionId && productCategoryId) {
    pagination.currentPage = 1;
    requestList();
  }
});

const handleSelectCategory = (category: IProductCategory) => {
  if (category) {
    state.selectCategory = category;
  }
};

const onCurrentChange = (data: IProductInfoExt) => {
  onChange(data);
};

const onChange = (data: IProductInfoExt) => {
  state.list.map(item => (item.selection = item.id === data.id));
  emit("onSelectProductInfo", data);
};

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const handleGetLatestVersion = async () => {
  const { data } = await getLatestVersion();
  if (data?.id) {
    state.productVersion = data;
  }
};

const requestList = useLoadingFn(async () => {
  let params: IProductInfoReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    versionId: state.productVersion?.id,
    productCategoryId: state.selectCategory.id
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryProductInfo(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
