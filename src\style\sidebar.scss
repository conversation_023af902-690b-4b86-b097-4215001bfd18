/* $sideBarWidth: vertical 模式下主体内容距离网页文档左侧的距离 */
@mixin merge-style($sideBarWidth) {
  @media screen and (min-width: 150px) and (max-width: 420px) {
    .app-main-nofixed-header {
      overflow-y: hidden;
    }
  }
  @media screen and (min-width: 420px) {
    .app-main-nofixed-header {
      overflow: hidden;
    }
  }

  .sub-menu-icon {
    font-size: 18px;
    margin-right: 8px;

    svg {
      width: 18px;
      height: 18px;
    }
  }

  .set-icon {
    height: 48px;
    width: 40px;
    display: flex;
    cursor: pointer;
    align-items: center;
    justify-content: center;
  }

  .main-container {
    height: 100vh;
    min-height: 100%;
    /* main-content 属性动画 */
    transition: margin-left var(--pure-transition-duration);
    margin-left: $sideBarWidth;
    position: relative;
    background: var(--fullscreen-bg);

    .el-scrollbar__wrap {
      overflow: auto;
      height: 100%;
    }
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 998;
    width: calc(100% - 200px);
    /* fixed-header 属性左上角动画 */
    transition: width var(--pure-transition-duration);
  }

  .main-hidden {
    margin-left: 0 !important;

    .fixed-header {
      width: 100% !important;

      + .app-main {
        padding-top: 37px !important;
      }
    }
  }

  .sidebar-container {
    /* 展开动画 */
    transition: width var(--pure-transition-duration);
    width: $sideBarWidth !important;
    background: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.12);

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0;
    }

    .el-scrollbar {
      height: calc(100% - 66px);
    }

    &.has-logo {
      .el-scrollbar.pc {
        /* logo: 48px、leftCollapse: 40px、leftCollapse-shadow: 4px */
        height: calc(100% - 120px);
      }
      .el-scrollbar.mobile {
        height: 100%;
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      display: flex;
      padding-left: 10px;
      flex-wrap: wrap;
      width: 100%;
    }

    .el-menu {
      border: none;
      height: 100%;
      background-color: transparent !important;
    }

    .el-menu-item,
    .el-sub-menu__title {
      height: 56px;
      color: $menuText;
      background-color: transparent !important;

      &:hover {
        color: $menuTitleHover !important;
      }

      div,
      span {
        height: 56px;
        line-height: 56px;
      }
    }

    .submenu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: transparent;
      }
    }

    .is-active > .el-sub-menu__title,
    .is-active.submenu-title-noDropdown {
      color: $subMenuActiveText !important;

      i {
        color: $subMenuActiveText !important;
      }
    }

    .is-active {
      transition: color 0.3s;
      color: $subMenuActiveText !important;
    }

    .el-menu-item.is-active.nest-menu > * {
      z-index: 1;
      color: $subMenuActiveText;
    }

    .el-menu-item.is-active.nest-menu::before {
      content: "";
      clear: both;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-right: 3px solid $menuRightBorder;
      background: $menuActiveBg !important;
    }

    .el-menu .el-menu--inline .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      font-size: 14px;
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg !important;
    }

    /* 有子集的激活菜单左侧小竖条 */
    .el-menu--collapse .is-active.outer-most.el-sub-menu > .el-sub-menu__title::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 2px;
      height: 100%;
      background-color: $menuActiveBefore;
      content: "";
      clear: both;
      transition: all var(--pure-transition-duration) ease-in-out;
      transform: translateY(0);
    }

    .el-menu--collapse .outer-most.el-sub-menu > .el-sub-menu__title::before {
      content: "";
      display: block;
      position: absolute;
      height: 0;
      width: 3px;
      transform: translateY(-50%);
      top: 50%;
    }

    /* 无子集的激活菜单背景 */
    .is-active.submenu-title-noDropdown.outer-most > * {
      z-index: 1;
      color: $subMenuActiveText;
    }
    .is-active.submenu-title-noDropdown.outer-most::before {
      content: "";
      clear: both;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-right: 3px solid $menuRightBorder;
      background: $menuActiveBg !important;
    }
  }

  /* vertical 菜单折叠 */
  .el-menu--vertical {
    .el-menu--popup {
      background-color: $subMenuBg !important;

      .el-menu-item {
        span {
          font-size: 14px;
        }
      }
    }

    & > .el-menu {
      i {
        margin-right: 20px;
      }
    }

    .is-active > .el-sub-menu__title,
    .is-active.submenu-title-noDropdown {
      color: $subMenuActiveText !important;

      i {
        color: $subMenuActiveText !important;
      }
    }

    /* 子菜单中还有子菜单 */
    .el-menu .el-sub-menu__title {
      font-size: 14px;
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg !important;
    }

    .el-menu-item,
    .el-sub-menu__title {
      height: 56px;
      line-height: 56px;
      color: $menuText;
      background-color: $subMenuBg;

      &:hover {
        color: $menuTitleHover !important;
      }
    }

    .is-active {
      transition: color 0.3s;
      color: $subMenuActiveText !important;
    }

    .el-menu-item.is-active.nest-menu > * {
      z-index: 1;
      color: $subMenuActiveText;
    }

    .el-menu-item.is-active.nest-menu::before {
      content: "";
      clear: both;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-right: 3px solid $menuRightBorder;
      background: $menuActiveBg !important;
    }

    .el-menu-item,
    .el-sub-menu {
      .iconfont {
        font-size: 18px;
      }

      .el-menu-tooltip__trigger {
        width: 54px;
        padding: 0;
      }
    }
  }

  /* horizontal 菜单 */
  .el-menu--horizontal {
    & > .el-sub-menu .el-sub-menu__icon-arrow {
      position: static !important;
      margin-top: 0;
      margin-left: 5px;
    }

    .el-menu--popup {
      background-color: $subMenuBg !important;

      .el-menu-item {
        color: $menuText;
        background-color: $subMenuBg;

        span {
          font-size: 14px;
        }
      }

      .el-sub-menu__title {
        color: $menuText;
      }
    }

    /* 无子菜单时激活 border-bottom */
    .router-link-exact-active > .submenu-title-noDropdown {
      height: 60px;
      border-bottom: 2px solid var(--el-menu-active-color);
    }

    /* 子菜单中还有子菜单 */
    .el-menu .el-sub-menu__title {
      font-size: 14px;
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg !important;

      &:hover {
        color: $menuTitleHover !important;
      }
    }

    .is-active > .el-sub-menu__title,
    .is-active.submenu-title-noDropdown {
      color: $subMenuActiveText !important;

      i {
        color: $subMenuActiveText !important;
      }
    }

    .nest-menu .el-sub-menu > .el-sub-menu__title,
    .el-menu-item {
      &:hover {
        color: $menuTitleHover !important;
      }
    }

    .el-menu-item.is-active {
      transition: color 0.3s;
      color: $subMenuActiveText !important;
    }

    .el-menu-item.is-active.nest-menu > * {
      z-index: 1;
      color: $subMenuActiveText;
    }

    .el-menu-item.is-active.nest-menu::before {
      content: "";
      clear: both;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-right: 3px solid $menuRightBorder;
      background: $menuActiveBg !important;
    }
  }

  .horizontal-header {
    display: flex;
    justify-content: space-around;
    background: $menuBg;
    width: 100%;
    height: 48px;
    align-items: center;

    .horizontal-header-left {
      display: flex;
      height: 100%;
      width: auto;
      min-width: 200px;
      align-items: center;
      padding-left: 10px;
      cursor: pointer;
      transition: all var(--pure-transition-duration) ease;

      img {
        height: 28px;
        display: inline-block;
      }
    }

    .horizontal-header-menu {
      height: 100%;
      min-width: 0;
      flex: 1;
      align-items: center;
    }

    .horizontal-header-right {
      display: flex;
      min-width: 340px;
      align-items: center;
      color: $subMenuActiveText;
      justify-content: flex-end;

      /* 搜索 */
      .search-container,
      /* 告警 */
      .dropdown-badge,
      /* 国际化 */
      .globalization,
      /* 用户名 */
      .el-dropdown-link,
      /* 设置 */
      .set-icon {
        &:hover {
          background: $menuHover;
        }
      }

      .dropdown-badge {
        height: 48px;
        color: $subMenuActiveText;
      }

      .globalization {
        width: 40px;
        height: 48px;
        padding: 11px;
        outline: none;
        cursor: pointer;
        color: $subMenuActiveText;
      }

      .el-dropdown-link {
        height: 48px;
        padding: 10px;
        display: flex;
        cursor: pointer;
        align-items: center;
        justify-content: space-around;
        color: $subMenuActiveText;

        p {
          font-size: 14px;
        }

        img {
          width: 22px;
          height: 22px;
          border-radius: 50%;
        }
      }
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      background-color: transparent;
    }

    .el-menu-item,
    .el-sub-menu__title {
      padding-right: var(--el-menu-base-level-padding);
      color: $menuText;

      &:hover {
        color: $menuTitleHover !important;
      }
    }

    .submenu-title-noDropdown,
    .el-sub-menu__title {
      height: 48px;
      line-height: 48px;
      background: $menuBg;

      svg {
        position: static !important;
      }
    }

    .is-active > .el-sub-menu__title,
    .is-active.submenu-title-noDropdown {
      color: $subMenuActiveText !important;

      i {
        color: $subMenuActiveText !important;
      }
    }

    .is-active {
      transition: color 0.3s;
      color: $subMenuActiveText !important;
    }

    /* tenant ui */

    .cx-tenant {
      background: $headerSelectBg;
      .el-select {
        .el-input__wrapper {
          box-shadow: none;
          background: transparent;
          .el-input__prefix .iconfont {
            color: $menuText;
          }
          .el-input__inner {
            color: $menuText;
          }
        }
      }
      &:hover {
        .el-input__wrapper {
          .el-input__prefix .iconfont {
            color: $menuTitleHover;
          }
          .el-input__inner {
            color: $menuTitleHover;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $sideBarWidth !important;
  }

  /* 手机端 */
  .mobile {
    .fixed-header {
      width: 100% !important;
      transition: width var(--pure-transition-duration);
    }

    .main-container {
      margin-left: 0 !important;
    }

    .sidebar-container {
      transition: transform var(--pure-transition-duration);
      width: $sideBarWidth;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }
}

body[layout="vertical"] {
  $sideBarWidth: 200px;
  @include merge-style($sideBarWidth);

  .el-menu--collapse {
    width: 54px;
  }

  .hideSidebar {
    .fixed-header {
      width: calc(100% - 54px);
      transition: width var(--pure-transition-duration);
    }

    .sidebar-container {
      transition: width var(--pure-transition-duration);
      width: 54px !important;

      .is-active.submenu-title-noDropdown.outer-most {
        background: transparent !important;
      }
    }

    .main-container {
      margin-left: 54px;
    }

    /* 菜单折叠 */
    .el-menu--collapse {
      .el-sub-menu {
        & > .el-sub-menu__title {
          & > span {
            height: 100%;
            width: 100%;
            text-align: center;
            visibility: visible;
          }
        }
      }

      .submenu-title-noDropdown {
        background: transparent !important;
      }

      .el-sub-menu__title {
        padding: 0;
      }
    }

    .sub-menu-icon {
      margin-right: 0;
    }
  }

  /* 搜索 */
  .search-container,
  /* 告警 */
  .dropdown-badge,
  /* 国际化 */
  .globalization,
  /* 用户名 */
  .el-dropdown-link,
  /* 设置 */
  .set-icon {
    &:hover {
      background: #f6f6f6;
    }
  }
}

body[layout="horizontal"] {
  $sideBarWidth: 0;
  @include merge-style($sideBarWidth);

  .fixed-header,
  .main-container {
    transition: none !important;
  }

  .fixed-header {
    width: 100%;
  }
}

body[layout="mix"] {
  $sideBarWidth: 200px;
  @include merge-style($sideBarWidth);

  .el-menu--collapse {
    width: 54px;
  }

  .el-menu {
    --el-menu-hover-bg-color: transparent !important;
  }

  .hideSidebar {
    .fixed-header {
      width: calc(100% - 54px);
      transition: width var(--pure-transition-duration);
    }

    .sidebar-container {
      transition: width var(--pure-transition-duration);
      width: 54px !important;

      .is-active.submenu-title-noDropdown.outer-most {
        background: transparent !important;
      }
    }

    .main-container {
      margin-left: 54px;
    }

    /* 菜单折叠 */
    .el-menu--collapse {
      .el-sub-menu {
        & > .el-sub-menu__title {
          padding: 0;
          & > span {
            height: 100%;
            width: 100%;
            text-align: center;
            visibility: visible;
          }
        }
      }
    }
  }
}
