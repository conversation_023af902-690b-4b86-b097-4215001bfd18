<template>
  <div class="inline-block mx-3">
    <slot name="trigger" :open-dialog="openDialog" />
    <el-dialog
      v-model="dialogVisible"
      title="重置密码"
      align-center
      width="500px"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <reset-password-form ref="formRef" v-loading="loading" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import ResetPasswordForm from "./reset-password-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { resetPassword } from "@/api";
import { IResetPassword } from "@/models";

/**
 * 修改密码 弹窗
 */
const props = defineProps<{
  /** 员工id */
  id: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const formRef = ref<InstanceType<typeof ResetPasswordForm>>();
const dialogVisible = ref(false);
const loading = ref(false);

const requestSave = useLoadingFn(async (form: IResetPassword) => {
  return await resetPassword(form);
}, loading);
/**
 * @description: 保存按钮点击事件
 */
async function handleClickSaveBtn() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }

  const formVal = formRef.value.getFormValue();
  // 请求保存
  const res = await requestSave({ ...formVal, id: props.id });

  if (res.data) {
    // 处理保存后续事件
    closeDialog();
    emits("postSaveSuccess");
    ElMessage({
      message: "操作成功",
      type: "success"
    });
  }
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
