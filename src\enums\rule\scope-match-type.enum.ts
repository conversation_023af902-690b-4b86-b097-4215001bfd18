import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

/**
 * 范围匹配类型枚举
 */
export enum ScopeMatchTypeEnum {
  /**
   * 不匹配 所有规格
   */
  ALL = 0,

  /**
   * 范围匹配
   */
  SPECIFIED_SCOPE = 1,

  /**
   * 正则匹配
   */
  REGEX = 2,

  /**
   * 表达式
   */
  EXPRESSION = 3,

  /** 截面 */
  LESS_THAN = 4
}

/**
 * 范围匹配类型的描述映射
 */
export const ScopeMatchTypeEnumMapDesc: Record<ScopeMatchTypeEnum, string> = {
  [ScopeMatchTypeEnum.ALL]: "所有规格",
  [ScopeMatchTypeEnum.SPECIFIED_SCOPE]: "指定范围",
  [ScopeMatchTypeEnum.REGEX]: "正则",
  [ScopeMatchTypeEnum.EXPRESSION]: "表达式",
  [ScopeMatchTypeEnum.LESS_THAN]: "截面"
};

export const ScopeMatchTypeOptions: Array<IOption> = mapDescToOptions<string>(ScopeMatchTypeEnumMapDesc);
