<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color px-6 py-5 flex justify-between items-center">
      <div>电解铜锭</div>
      <div class="flex gap-4">
        <el-button :icon="Back" @click="onBack()">返回</el-button>
        <AddEditRawMaterialCostDialog mode="edit" :id="id" @post-save-success="handleGetRawMaterialCost()">
          <template #trigger="{ openDialog }">
            <el-button :icon="Edit" type="primary" @click="openDialog">编辑</el-button>
          </template>
        </AddEditRawMaterialCostDialog>
      </div>
    </div>
    <div class="flex flex-col p-5 overflow-hidden flex-1">
      <div class="p-5 bg-bg_color">
        <el-descriptions title="基础信息">
          <el-descriptions-item label="类型">导体原材料</el-descriptions-item>
          <el-descriptions-item label="型号">阴极铜</el-descriptions-item>
          <el-descriptions-item label="规格">CU>=9.91</el-descriptions-item>
          <el-descriptions-item label="计算单位">t</el-descriptions-item>
          <el-descriptions-item label="单价成本">￥80,000.00</el-descriptions-item>
          <el-descriptions-item label="更新人">管理员</el-descriptions-item>
          <el-descriptions-item label="更新时间">2025-06-21 22:22</el-descriptions-item>
          <el-descriptions-item label="版本">V6</el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="bg-bg_color p-5 my-5 flex flex-col flex-1 overflow-hidden relative">
        <PureTable
          class="flex-1 overflow-hidden pagination"
          row-key="id"
          :data="state.list"
          :columns="columns"
          size="large"
          :loading="loading"
          showOverflowTooltip
        >
          <template #empty>
            <CxEmptyData />
          </template>
        </PureTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CxEmptyData from "@/components/CxEmpty";
import { Edit, Back } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { onMounted, reactive, ref } from "vue";
import { IRawMaterialCost } from "@/models/quote-basic-data";
import { ProductUnitEnum } from "@/enums";
import { useRoute, useRouter } from "vue-router";
import { getRawMaterialCostById } from "@/api/quote-basic-data";
import AddEditRawMaterialCostDialog from "../add-edit-raw-material-cost/dialog.vue";

const { columns } = useColumns();
const loading = ref(false);
const router = useRouter();
const route = useRoute();

let id: string;
const state = reactive<{
  list: Array<IRawMaterialCost>;
  rawMaterialCost: IRawMaterialCost;
}>({
  list: [
    {
      versionNum: "1.0",
      price: 0.01,
      productUnit: ProductUnitEnum.KG
    }
  ],
  rawMaterialCost: {}
});

onMounted(() => {
  id = route.params.id as string;
});

const handleGetRawMaterialCost = async () => {
  const { data } = await getRawMaterialCostById(id);
  state.rawMaterialCost = data;
};

const onBack = () => {
  router.back();
};
</script>

<style scoped lang="scss"></style>
