import { IEmployeeReq } from "@/models";
import { useDepartmentStore, useEmployeeStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { reactive } from "vue";

export const useEmployeeHook = () => {
  const departmentStore = useDepartmentStore();
  const employeeStore = useEmployeeStore();
  const employeeState = reactive<{
    modalVisible: boolean;
  }>({
    modalVisible: false
  });

  const queryDepartmentEmployee = (params: IEmployeeReq) => {
    employeeStore.queryEmployee(params);
  };

  const setEmployeeModalVisible = (visible: boolean) => {
    employeeState.modalVisible = visible;
  };

  const deleteDepartmentEmployee = async (deptId: string, userId: string) => {
    await departmentStore.deleteDepartmentEmployee(deptId, userId);
    ElMessage.success("员工移除成功");
  };

  const bindEmployeeToDepartment = async (deptId: string, userIds: Array<string>) => {
    await departmentStore.bindEmployeeToDepartment(deptId, userIds);
    ElMessage.success("员工绑定成功");
    employeeState.modalVisible = false;
  };

  const onCloseSelectEmployeeModal = () => {
    employeeState.modalVisible = false;
  };

  const onBindEmployeeToDepartmentModalVis = () => {
    employeeState.modalVisible = true;
  };

  return {
    employeeState,
    deleteDepartmentEmployee,
    bindEmployeeToDepartment,
    setEmployeeModalVisible,
    queryDepartmentEmployee,
    onCloseSelectEmployeeModal,
    onBindEmployeeToDepartmentModalVis
  };
};
