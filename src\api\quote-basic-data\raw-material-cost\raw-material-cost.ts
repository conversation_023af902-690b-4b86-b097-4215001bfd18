import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse, IRawMaterialCost, IRawMaterialCostForm, IRawMaterialCostReq } from "@/models";

/** 查询全部原材料成本维护 */
export const queryAllRawMaterialCost = (params?: IRawMaterialCostReq) => {
  const url: string = withApiGateway(`admin-api/business/raw-material-cost/all-raw-material-cost`);
  return http.get<IRawMaterialCostReq, IResponse<Array<IRawMaterialCost>>>(url, {
    params
  });
};

/** 查询原材料成本维护分页  */
export const queryRawMaterialCost = (params: IRawMaterialCostReq) => {
  const url: string = withApiGateway(`admin-api/business/raw-material-cost/raw-material-cost`);
  return http.get<IRawMaterialCostReq, IListResponse<IRawMaterialCost>>(url, {
    params
  });
};

/** 根据原材料成本维护id 查询详情 */
export const getRawMaterialCostById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/raw-material-cost/raw-material-cost-detail-by-id/${id}`);
  return http.get<string, IResponse<IRawMaterialCost>>(url);
};

/** 新增原材料成本维护 */
export const createRawMaterialCost = (data: IRawMaterialCostForm) => {
  return http.post<IRawMaterialCostForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/raw-material-cost/raw-material-cost"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑原材料成本维护 */
export const updateRawMaterialCost = (data: IRawMaterialCostForm) => {
  return http.put<IRawMaterialCostForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/raw-material-cost/raw-material-cost"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除原材料成本维护根据Id */
export const deleteRawMaterialCostById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(
    withApiGateway(`admin-api/business/raw-material-cost/raw-material-cost-by-id/${id}`)
  );
};
