import { FieldOperatorEnum } from "@/enums";
import { IBaseFieldMatchCondition } from "./i-base-field-match-condition";

/**
 * 匹配条件 - 电压
 */
export interface IVoltageMatchCondition extends IBaseFieldMatchCondition {
  /**
   * 所有电压
   * @default false
   */
  matchAll?: boolean;

  /**
   * 无电压
   * @default false
   */
  matchEmpty?: boolean;

  /**
   * 范围
   * @default false
   */
  matchRange?: boolean;

  /**
   * 下边界值 X
   */
  lowerBoundX?: number;

  /**
   * 下边界值 Y
   */
  lowerBoundY?: number;

  /**
   * 下边界 操作符枚举
   */
  lowerOperator?: FieldOperatorEnum;

  /**
   * 上边界值 X
   */
  upperBoundX?: number;

  /**
   * 上边界值 Y
   */
  upperBoundY?: number;

  /**
   * 上边界 操作符枚举
   */
  upperOperator?: FieldOperatorEnum;
}
