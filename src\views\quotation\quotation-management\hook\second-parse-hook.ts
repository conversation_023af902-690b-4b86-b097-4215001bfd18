import { batchConfirmApplySecondParse, confirmApplySecondParse } from "@/api/quotation/quotation-inquiry";
import { IQuotationInquiryDetail } from "@/models";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";
import { ref } from "vue";

export const useSecondParseHook = (queryQuotationInquiryParseDetailList: Function) => {
  const applyAiRecommendModelLoading = ref<boolean>(false);

  // 应用AI推荐型号
  const onApplyAiRecommendModel = async (data: IQuotationInquiryDetail) => {
    if (!(await useConfirm("确认应用AI推荐型号成功后，数据将不能回退", "确认应用AI推荐型号"))) {
      return;
    }
    useLoadingFn(async () => {
      await confirmApplySecondParse({ id: data.id, useSecondParse: true });
      ElMessage.success("AI推荐型号应用成功");
      queryQuotationInquiryParseDetailList && queryQuotationInquiryParseDetailList();
    }, applyAiRecommendModelLoading)();
  };

  // 应用AI推荐型号
  const onBatchConfirmApplySecondParse = async (list: Array<IQuotationInquiryDetail>) => {
    if (!Array.isArray(list) || list.length === 0) {
      ElMessage.warning("请选择应用的的数据");
      return;
    }

    if (!(await useConfirm("确认应用AI推荐型号成功后，数据将不能回退", "确认应用AI推荐型号"))) {
      return;
    }

    useLoadingFn(async () => {
      await batchConfirmApplySecondParse({ taskItemIds: list.map(x => x.id), useSecondParse: true });
      ElMessage.success("AI推荐型号应用成功");
      queryQuotationInquiryParseDetailList && queryQuotationInquiryParseDetailList();
    }, applyAiRecommendModelLoading)();
  };

  return {
    onApplyAiRecommendModel,
    onBatchConfirmApplySecondParse,
    applyAiRecommendModelLoading
  };
};
