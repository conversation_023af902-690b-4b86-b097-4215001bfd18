<template>
  <div class="p-5 bg-white">
    <div class="headline">AI模型监控</div>
    <div class="ai-container">
      <AIMonitoring
        v-for="(item, index) in AIMonitoringList"
        :key="index"
        :title="item.title"
        :value="item.value"
        :unit="item.unit"
        :show-tooltip="item.showTooltip"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import AIMonitoring from "./components/ai-monitoring.vue";
import { IDataStatistics } from "@/models";

const props = withDefaults(
  defineProps<{
    data: IDataStatistics;
  }>(),
  {}
);

const AIMonitoringList = ref([
  {
    title: "AI处理报价单数量",
    value: "0",
    unit: "单",
    showTooltip: false
  },
  {
    title: "询价单平均解析时间",
    value: "0",
    unit: "秒",
    showTooltip: false
  },
  {
    title: "本月token用量",
    value: "0",
    unit: "",
    showTooltip: false
  },
  {
    title: "token余额",
    value: "0",
    unit: "",
    showTooltip: false
  }
]);

watch(
  () => props.data,
  data => {
    if (data) {
      AIMonitoringList.value = [
        {
          title: "AI处理报价单数量",
          value: `${props.data.disposeQuotationCount || 0}`,
          unit: "单",
          showTooltip: false
        },
        {
          title: "询价单平均解析时间",
          value: `${props.data.averageAnalysisTime || 0}`,
          unit: "秒",
          showTooltip: false
        },
        {
          title: "本月token用量",
          value: `${props.data.monthTokenUsage || 0}`,
          unit: "",
          showTooltip: true
        },
        {
          title: "token余额",
          value: `${props.data.tokenBalance || 0}`,
          unit: "",
          showTooltip: true
        }
      ];
    }
  },
  {
    immediate: true
  }
);
</script>

<style lang="scss" scoped>
.headline {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 13px;
}

.ai-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}
</style>
