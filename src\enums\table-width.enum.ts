/** 表格列宽 - 字符类别 */
export enum TableWidth {
  /** 单选 */
  radio = "40",

  /** 多选 */
  check = "60",

  /** 序号 */
  index = "80",

  /** 序号 */
  indexName = "60",

  /** 编号 */
  order = "150",

  /** 子编号 */
  suborder = "160",

  /** 编号-large */
  largeOrder = "220",

  /** 名称, 物料名称 */
  name = "220",

  /** 名称-large */
  largeName = "280",

  /** 原因,长备注 */
  result = "400",

  /** 状态 - tag最长四字 */
  status = "110",

  /** 种类,环节,分类等  */
  type = "140",

  /** 长状态 */
  largeType = "190",

  /** 日期 - 年月日 */
  date = "120",

  /** 日期 - 年月日时间 */
  dateTime = "190",

  /** 日期范围 */
  dateRanger = "210",

  /** 日期范围 */
  dateTimeRanger = "340",

  /** 计量单位 */
  unit = "120",

  /** 数量 */
  number = "100",

  /** 长字段数量 */
  largeNumber = "130",

  /** 文件 */
  file = "180",

  /** 操作项 - 一项 */
  simpleOperation = "100",

  /** 操作项 - 两项 */
  operation = "130",

  /** 操作项 - 三项 */
  operations = "180",

  /** 操作项 - 超长字段 */
  largeOperation = "220",

  /** 操作项 - 超超长字段 */
  largeMgOperation = "240",

  /** 操作项 - 超超长字段 */
  largeXgOperation = "300",

  /** 原因 */
  reason = "200"
}

/** 表格列宽 - 字符长度 Char1表示1个中文字符长度 */
export enum ColumnWidth {
  Char1 = 48,
  Char2 = 62,
  Char3 = 76,
  Char4 = 90,
  Char5 = 104,
  Char6 = 118,
  Char7 = 132,
  Char8 = 146,
  Char9 = 160,
  Char10 = 174,
  Char11 = 188,
  Char12 = 202,
  Char13 = 216,
  Char14 = 230,
  Char15 = 244,
  Char16 = 258,
  Char17 = 272,
  Char18 = 286,
  Char19 = 300,
  Char20 = 314,
  Char21 = 328,
  Char22 = 342,
  Char23 = 356,
  Char24 = 370,
  Char25 = 384,
  Char26 = 398,
  Char27 = 412,
  Char28 = 426,
  Char29 = 440,
  Char30 = 458
}
