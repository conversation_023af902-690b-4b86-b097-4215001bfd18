import { Directive, DirectiveBinding } from "vue";

export const disabledOp: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    if (binding.value) {
      el.style.visibility = "hidden";
    } else {
      el.style.visibility = "visible";
    }
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    if (binding.value) {
      el.style.visibility = "hidden";
    } else {
      el.style.visibility = "visible";
    }
  }
};
