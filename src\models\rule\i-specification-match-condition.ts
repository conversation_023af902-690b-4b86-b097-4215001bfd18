import { FieldOperatorEnum, ScopeMatchTypeEnum, ScopeOperatorEnum } from "@/enums";
import { IBaseFieldMatchCondition } from "./i-base-field-match-condition";

/**
 * 匹配条件 - 规格
 */
export interface ISpecificationMatchCondition extends IBaseFieldMatchCondition {
  /**
   * 匹配类型
   */
  matchType?: ScopeMatchTypeEnum;

  /**
   * 匹配操作符
   */
  scopeOperator?: ScopeOperatorEnum;

  /**
   * 匹配值
   */
  matchValue?: string;

  /** 截面操作符 */
  areaOperator?: FieldOperatorEnum;

  /**
   * 截面 - Min
   */
  areaMin?: number;

  /**
   * 截面 -  Max
   */
  areaMax?: number;

  /**
   * 表达式
   */
  expression?: string;
}
