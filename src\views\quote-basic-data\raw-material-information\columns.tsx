import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      type: "selection",
      width: ColumnWidth.Char2
    },
    {
      label: "类型",
      prop: "type",
      width: ColumnWidth.Char6
    },
    {
      label: "物料编码",
      prop: "materialCode",
      width: ColumnWidth.Char8
    },
    {
      label: "原材料名称",
      prop: "rawName",
      width: ColumnWidth.Char16
    },
    {
      label: "原材料型号",
      prop: "rawModel",
      width: ColumnWidth.Char12
    },
    {
      label: "原材料规格",
      prop: "rawSpec",
      width: ColumnWidth.Char7
    },
    {
      label: "单价成本",
      prop: "latestCost.price",
      slot: "latestCostPrice",
      width: ColumnWidth.Char6
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: ColumnWidth.Char10
    },
    {
      label: "创建人",
      prop: "creatorName",
      slot: "creatorName",
      width: ColumnWidth.Char5
    },
    {
      label: "更新时间",
      prop: "updateTime",
      width: ColumnWidth.Char10
    },
    {
      label: "更新人",
      prop: "updaterName",
      slot: "updaterName",
      width: ColumnWidth.Char5
    },
    {
      label: "状态",
      prop: "status",
      slot: "status",
      fixed: "right"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char8
    }
  ];
  return { columns };
}
