import { MaterialTypeEnum } from "@/enums/quote-basic-data";
import { IRawMaterialInformation } from "../raw-material-information";

export interface IBomMaterialQuotaForm {
  /**
   * 选用原材料ID
   * @example "RAW-1001"
   */
  bomRawId?: string;

  /**
   * 主/辅材类型
   * @see MaterialTypeEnum
   */
  materialType?: MaterialTypeEnum;

  /**
   * 材料定额数量
   * @minimum 0
   * @example 2.5
   */
  quantity?: number;

  /**
   * 计量单位
   * @example "米", "千克", "个"
   */
  productUnit?: string;
  /** 原材料 */
  bomRaw?: IRawMaterialInformation;

  /** 删除 标志 */
  deleted?: boolean;
}

export interface IBomMaterialQuotaFormExt extends IBomMaterialQuotaForm {
  materialTypeBoolean?: boolean;
}
