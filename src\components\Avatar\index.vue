<template>
  <el-avatar class="img-avatar" v-if="props.avatar" :size="props.size" :src="props.avatar" />
  <el-avatar v-else :size="props.size">{{ props.word }}</el-avatar>
</template>
<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    avatar?: string;
    size?: number;
    word?: string;
  }>(),
  {
    size: 24
  }
);
</script>

<style scoped>
.img-avatar {
  background-color: #ffffff;
}
</style>
