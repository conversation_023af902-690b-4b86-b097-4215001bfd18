<template>
  <div class="radio">
    <el-radio-group :model-value="inputValue" :disabled="!!config?.disable" @change="optionChange($event)">
      <el-radio v-for="item in radioOptions.optionList" :key="item.key" :label="item.value">{{ item.label }}</el-radio>
    </el-radio-group>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash-unified";
import { useTableForm } from "../../form-value-hooks";
import { EControlType } from "@/enums";

const props = defineProps({
  inputValue: {
    type: String,
    default: ""
  },
  config: {
    type: Object,
    default: () => ({})
  }
});

const emits = defineEmits(["valueChange"]);
// 获取 GroupRadio 的值
const { getRadioOptions, radioOptions } = useTableForm();
const rowTypeData = cloneDeep(props.config);
getRadioOptions(rowTypeData);

/**
 * 控件值更新
 */
const optionChange = value => {
  emits("valueChange", {
    value,
    key: EControlType.RadioControl,
    config: props.config
  });
};
</script>

<style scoped lang="scss"></style>
