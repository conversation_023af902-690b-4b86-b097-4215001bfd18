/**
 * @description ⚠️：此文件仅供主题插件使用，请不要在此文件中导出别的工具函数（仅在页面加载前运行）
 */

import { type multipleScopeVarsOptions } from "@pureadmin/theme";

/** 预设主题色 */
const themeColors = {
  default: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#00b678",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#4CCCA0",
    menuActiveBg: "#00b678"
  },
  light: {
    subMenuActiveText: "#00b678",
    menuBg: "#fff",
    menuHover: "#e0ebf6",
    subMenuBg: "#fff",
    headerSelectBg: "#f6f6f6",
    menuText: "#303133",
    menuTitleHover: "#303133",
    menuActiveBefore: "#00b678",
    menuRightBorder: "#4CCCA0",
    menuActiveBg: "#e5f8f1"
  },
  lightE: {
    subMenuActiveText: "#4FA4D1",
    menuBg: "#fff",
    menuHover: "#e0ebf6",
    subMenuBg: "#fff",
    headerSelectBg: "#f6f6f6",
    menuText: "#303133",
    menuTitleHover: "#303133",
    menuActiveBefore: "#4FA4D1",
    menuRightBorder: "#84BFDF",
    menuActiveBg: "#EDF6FA"
  },
  blue: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#3662EC",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#7392F2",
    menuActiveBg: "#3662EC"
  },
  prussian: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#0747A6",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#527FC1",
    menuActiveBg: "#0747A6"
  },
  greyblue: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#4FA4D1",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#84BFDF",
    menuActiveBg: "#4FA4D1"
  },
  bamboo: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#00B259",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#4DC98B",
    menuActiveBg: "#00B259"
  },
  leaf: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#67C23A",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#95D475",
    menuActiveBg: "#67C23A"
  },
  azure: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#409EFF",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#7ABBFF",
    menuActiveBg: "#409EFF"
  },
  cyan: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#07B9B9",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#52CECE",
    menuActiveBg: "#07B9B9"
  },
  orange: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#FF8F1F",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#FFB163",
    menuActiveBg: "#FF8F1F"
  },
  gold: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#FFC300",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#FFD54D",
    menuActiveBg: "#FFC300"
  },
  purple: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#8A38F5",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#AD74F8",
    menuActiveBg: "#8A38F5"
  },
  red: {
    subMenuActiveText: "#fff",
    menuBg: "#19293D",
    menuHover: "#FA5151",
    subMenuBg: "#162334",
    headerSelectBg: "#142131",
    menuText: "#C0C4CC",
    menuTitleHover: "#fff",
    menuActiveBefore: "#fff",
    menuRightBorder: "#FC8686",
    menuActiveBg: "#FA5151"
  }
};

/**
 * @description 将预设主题色处理成主题插件所需格式
 */
export const genScssMultipleScopeVars = (): multipleScopeVarsOptions[] => {
  const result = [] as multipleScopeVarsOptions[];
  Object.keys(themeColors).forEach(key => {
    result.push({
      scopeName: `layout-theme-${key}`,
      varsContent: `
        $subMenuActiveText: ${themeColors[key].subMenuActiveText} !default;
        $menuBg: ${themeColors[key].menuBg} !default;
        $menuHover: ${themeColors[key].menuHover} !default;
        $subMenuBg: ${themeColors[key].subMenuBg} !default;
        $headerSelectBg: ${themeColors[key].headerSelectBg} !default;
        $menuText: ${themeColors[key].menuText} !default;
        $menuTitleHover: ${themeColors[key].menuTitleHover} !default;
        $menuActiveBefore: ${themeColors[key].menuActiveBefore} !default;
        $menuRightBorder: ${themeColors[key].menuRightBorder} !default;
        $menuActiveBg: ${themeColors[key].menuActiveBg} !default;
      `
    } as multipleScopeVarsOptions);
  });
  return result;
};
