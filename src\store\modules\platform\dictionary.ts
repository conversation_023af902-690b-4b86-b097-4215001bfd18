import { IDictionary, IDictionaryForm, IDictionaryOption, IDictionaryOptionForm, IDictionaryOptionReq } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/platform/dictionary";

export const useDictionaryStore = defineStore({
  id: "cx-dictionary",
  state: () => ({
    dictionaries: [] as Array<IDictionary>,
    dictionaryForm: {} as IDictionaryForm,
    dictionaryOptions: [] as Array<IDictionaryOption>,
    dictionaryOptionForm: {} as IDictionaryOptionForm
  }),
  actions: {
    async queryAllDictionary() {
      this.dictionaries = (await api.queryAllDictionary()).data;
    },

    async addDictionary(data: IDictionaryForm) {
      return api.addDictionary(data);
    },

    async editDictionary(data: IDictionaryForm) {
      return api.editDictionary(data);
    },
    async getDictionaryOptions(data: IDictionaryOptionReq) {
      this.dictionaryOptions = (await api.getDictionaryOptions(data)).data;
    },

    async addDictionaryOption(data: IDictionaryOptionForm) {
      return api.addDictionaryOption(data);
    },

    async editDictionaryOption(data: IDictionaryOptionForm) {
      return api.editDictionaryOption(data);
    },

    setDictionaryForm(data?: IDictionaryForm) {
      this.dictionaryForm = data;
    },

    setDictionaryOptionForm(data?: Partial<IDictionaryOptionForm>) {
      this.dictionaryOptionForm = data;
    }
  }
});
