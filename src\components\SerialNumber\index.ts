import { defineComponent, h, ref, watch } from "vue";
import { ElInput } from "element-plus";
import { useSerialNumber } from "./useSerialNumber";

export default defineComponent({
  name: "SerialNumber",
  props: {
    code: {
      required: true,
      type: String
    },
    dependenceParentNo: {
      type: Boolean
    },
    parentNo: {
      type: String
    },
    create: {
      type: Boolean
    },
    modelValue: {
      type: String
    },
    maxlength: {
      type: [String, Number]
    },
    forcedEditable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      disabled: false
    };
  },
  computed: {
    no: {
      get() {
        return this.$props.modelValue;
      },
      set(value) {
        this.$emit("update:modelValue", value);
      }
    }
  },
  mounted(): void {
    const { generateNo } = useSerialNumber();
    watch(
      [() => this.$props.create, () => this.$props.dependenceParentNo, () => this.$props.parentNo],
      async ([shouldCreate, dependenceParentNo, parentNo]) => {
        if (!shouldCreate) {
          return;
        }
        if (dependenceParentNo && !parentNo) {
          return;
        }
        this.no = await generateNo(this.code, this.parentNo);
      },
      { immediate: true }
    );
    watch(
      [() => this.$props.create, () => this.$props.code],
      ([create]) => {
        if (this.$props.forcedEditable) {
          this.disabled = ref(false);
        } else {
          this.disabled = create ? ref(false) : ref(true);
        }
      },
      { immediate: true }
    );
  },
  methods: {
    async refreshNo() {
      const { generateNo } = useSerialNumber();
      this.no = await generateNo(this.code, this.parentNo);
    }
  },
  render() {
    return h(ElInput, {
      disabled: this.disabled,
      maxlength: this.maxlength,
      modelValue: this.no,
      clearable: true,
      "onUpdate:modelValue": value => (this.no = value)
    });
  }
});
