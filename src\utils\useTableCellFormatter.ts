import { dateFormat, fullDateFormat } from "@/consts";
import { ElRadio, ElRadioGroup, RadioGroupProps, TableColumnCtx } from "element-plus";
import { formatDate, formatDecimal } from "@/utils/format";
import { h, Ref } from "vue";
import CxTag from "@/components/CxTag/index.vue";

export const useTableCellFormatter = () => {
  const dateFormatter =
    (format: string = fullDateFormat) =>
    (row: any, column: TableColumnCtx<any>, cellValue: string) =>
      formatDate(cellValue, format);

  const dateRangeFormatter =
    (startField: string, endField: string, format: string = dateFormat) =>
    row => {
      const start = row[startField];
      const end = row[endField];
      if (!start && !end) {
        return null;
      }
      return `${formatDate(start, format) || ""} ~ ${formatDate(end, format) || ""}`;
    };

  const statusFormatter =
    (successText: string, failText: string, defaultText?: string) =>
    (row: any, column: TableColumnCtx<any>, cellValue: boolean) => {
      if (cellValue === true) {
        return h(CxTag, { type: "success", icon: "icon-success-fill" }, () => successText);
      } else if (cellValue === false) {
        return h(CxTag, { type: "danger", icon: "icon-fail-fill" }, () => failText);
      }
      if (defaultText != null) {
        return h(CxTag, { type: "info" }, () => defaultText);
      }
      return null;
    };

  const singleSelectFormatter =
    <T extends string | number>(selected: Ref<T>, attr?: Partial<RadioGroupProps>) =>
    (row: any, column: TableColumnCtx<any>, value: T) => {
      const radio = h(ElRadio, { label: value, style: { margin: "0 !important", width: "14px" } }, () => "");
      return h(ElRadioGroup, { modelValue: selected.value, ...(attr || {}) }, () => radio);
    };

  const mapFormatter =
    (map: Record<string | number, any>) => (row: any, column: TableColumnCtx<any>, cellValue: string | number) =>
      map[cellValue] || cellValue;

  const decimalFormatter =
    (digitsInfo?: string, locales?: Intl.LocalesArgument) =>
    (row: any, column: TableColumnCtx<any>, cellValue: number) =>
      formatDecimal(cellValue, digitsInfo, locales);

  const statusCodeFormatter = () => (row: any, column: TableColumnCtx<any>, cellValue: number) => {
    const success: boolean = cellValue >= 200 && cellValue < 300;
    return success ? h(CxTag, { type: "success" }, () => "成功") : h(CxTag, { type: "danger" }, () => "失败");
  };

  /**
   * @description: 百分比
   */
  const percentFormatter = (row: any, column: TableColumnCtx<any>, cellValue: number) => {
    return formatDecimal(cellValue, "1.0-2") + "%";
  };

  return {
    dateFormatter,
    dateRangeFormatter,
    statusFormatter,
    singleSelectFormatter,
    mapFormatter,
    decimalFormatter,
    statusCodeFormatter,
    percentFormatter
  };
};
