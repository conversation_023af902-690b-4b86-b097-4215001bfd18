import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      type: "selection",
      width: ColumnWidth.Char2
    },
    {
      label: "产品分类",
      prop: "productCategory",
      width: ColumnWidth.Char8
    },
    {
      label: "物料编号",
      prop: "productCode",
      width: ColumnWidth.Char8
    },
    {
      label: "产品名称",
      prop: "productName",
      slot: "productName",
      width: ColumnWidth.Char12
    },
    {
      label: "型号",
      prop: "model",
      width: ColumnWidth.Char14
    },
    {
      label: "规格",
      prop: "specification",
      width: ColumnWidth.Char8
    },
    {
      label: "电压等级",
      prop: "voltage",
      width: ColumnWidth.Char6
    },
    {
      label: "计量单位",
      prop: "productUnit",
      width: ColumnWidth.Char6
    },
    {
      label: "材料成本",
      prop: "materialCostTotal",
      align: "right",
      width: ColumnWidth.Char8
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: ColumnWidth.Char10
    },
    {
      label: "创建人",
      prop: "creatorName",
      slot: "creatorName",
      width: ColumnWidth.Char5
    },
    {
      label: "更新时间",
      prop: "updateTime",
      width: ColumnWidth.Char10
    },
    {
      label: "更新人",
      prop: "updaterName",
      slot: "updaterName",
      width: ColumnWidth.Char5
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char6
    }
  ];
  return { columns };
}
