<template>
  <TodayQuotation
    v-for="(item, index) in quotationData"
    :key="index"
    :title="item.title"
    :unit="item.unit"
    :type="item.type"
    :todayValue="item.todayValue"
    :yesterdayValue="item.yesterdayValue"
    :icon="item.icon"
  />
</template>

<script setup lang="ts">
import quotationIcon1 from "@/assets/img/dashboard/quotation-icon-1.png";
import quotationIcon2 from "@/assets/img/dashboard/quotation-icon-2.png";
import { ref, watch } from "vue";
import TodayQuotation from "./components/today-quotation.vue";
import { IDataStatistics } from "@/models";

const props = withDefaults(
  defineProps<{
    data: IDataStatistics;
  }>(),
  {}
);

const quotationData = ref([
  {
    title: "今日报价单数量",
    unit: "单",
    todayValue: 0,
    yesterdayValue: 0,
    icon: quotationIcon1,
    type: "count"
  },
  {
    title: "今日报价单金额",
    unit: "元",
    todayValue: 0,
    yesterdayValue: 0,
    icon: quotationIcon2,
    type: "money"
  }
]);

watch(
  () => props.data,
  data => {
    if (data) {
      quotationData.value = [
        {
          title: "今日报价单数量",
          unit: "单",
          todayValue: props?.data?.todayQuotationCount || 0,
          yesterdayValue: props?.data?.yesterdayQuotationCount || 0,
          icon: quotationIcon1,
          type: "count"
        },
        {
          title: "今日报价单金额",
          unit: "元",
          todayValue: props?.data?.todayQuotationAmount || 0,
          yesterdayValue: props?.data?.yesterdayQuotationAmount || 0,
          icon: quotationIcon2,
          type: "money"
        }
      ];
    }
  },
  {
    immediate: true
  }
);
</script>

<style></style>
