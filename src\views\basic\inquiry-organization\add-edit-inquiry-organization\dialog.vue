<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" />
    <el-dialog
      v-model="drawerVisible"
      :title="isEditMode ? '编辑' : '新增'"
      width="35%"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog()"
    >
      <InquiryUnitForm ref="formRef" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button v-if="!props.id" type="warning" @click="handleSaveContinueAddBtn" :loading="saveContinueAddLoading"
            >保存、并继续新增</el-button
          >
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import InquiryUnitForm from "./form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  getInquiryOrganizationById,
  createInquiryOrganization,
  updateInquiryOrganization
} from "@/api/basic/inquiry-organization";

const loading = ref(false);
const props = defineProps<{
  mode: "edit" | "add";
  id?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);
let saveContinueAddTag = false;
const formRef = ref<InstanceType<typeof InquiryUnitForm>>();
const saveContinueAddLoading = ref(false);
const drawerVisible = ref(false);
/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");
/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");
const handleSaveBtn = useLoadingFn(onSave, loading);
const handleSaveContinueAddBtn = useLoadingFn(handleSaveContinueAddLoading, saveContinueAddLoading);

// 订阅弹窗开启状态，请求数据
watch(drawerVisible, async visible => {
  if (!visible || !isEditMode.value) {
    return;
  }
  const { data } = await getInquiryOrganizationById(props.id);
  formRef.value.initFormValue(data);
});

async function handleSaveContinueAddLoading() {
  const result = await handleSave();
  if (!result) {
    return;
  }
  saveContinueAddTag = true;
  formRef.value.emptyFormValue();
  ElMessage({ message: isAddMode.value ? "新增成功" : "编辑成功", type: "success" });
}

/**
 *  保存按钮点击事件
 */
async function onSave() {
  const result = await handleSave();
  if (!result) {
    return;
  }

  closeDialog();
  emits("postSaveSuccess");
  ElMessage({ message: isAddMode.value ? "新增成功" : "编辑成功", type: "success" });
}

async function handleSave() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return false;
  }

  const formVal = formRef.value.getFormValue();
  if (isAddMode.value) {
    await createInquiryOrganization(formVal);
  } else {
    await updateInquiryOrganization(formVal);
  }
  return true;
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  drawerVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  if (saveContinueAddTag) {
    emits("postSaveSuccess");
  }
  drawerVisible.value = false;
}
</script>

<style scoped></style>
