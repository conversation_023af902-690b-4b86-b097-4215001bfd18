<template>
  <el-dialog
    v-model="modelValue"
    :title="props.name || '预览'"
    :append-to-body="true"
    align-center
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div v-if="props.src" class="flex items-center justify-center">
      <el-image
        :src="src"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="[src]"
        show-progress
        :initial-index="4"
        fit="cover"
      />
    </div>
    <template #footer>
      <span>
        <el-button @click="closeDialog">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";

const emits = defineEmits<{
  (e: "update:modelValue", value: boolean);
}>();
const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    src?: string;
    name?: string;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

const closeDialog = () => {
  modelValue.value = false;
};
</script>
