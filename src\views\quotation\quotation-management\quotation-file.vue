<template>
  <div ref="dropZoneRef" class="upload-container" :class="{ 'is-dragover': isDragover }">
    <el-icon class="upload-icon" :size="24"><upload-filled class="text-5xl" /></el-icon>
    <span class="tip">拖拽或手动上传询价单</span>

    <el-upload
      class="quotation-upload"
      ref="uploadRef"
      drag
      :auto-upload="false"
      :limit="1"
      accept="image/*,.xlsx,.xls,.csv,application/pdf"
      :on-exceed="onExceed"
      :on-change="onChangeUploadFile"
      @drag-over="isDragover = true"
      @drag-leave="isDragover = false"
      @drop="isDragover = false"
    >
      <div class="flex items-center justify-center">
        <el-icon class="upload-icon" :size="24"><upload-filled class="text-5xl" /></el-icon>
        <div><span class="tip">拖曳或手动上传询价单</span></div>
      </div>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import { UploadFilled } from "@element-plus/icons-vue";
import { genFileId, UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { onMounted, ref } from "vue";

const isDragover = ref(false);
const dropZoneRef = ref(null);
// 如果 el-upload 的事件不足够，可以添加这些事件监听器
const addCustomDragEvents = () => {
  const dropZone = dropZoneRef.value;
  if (!dropZone) return;

  dropZone.addEventListener("dragover", e => {
    e.preventDefault();
    isDragover.value = true;
  });

  dropZone.addEventListener("dragleave", e => {
    e.preventDefault();
    isDragover.value = false;
  });

  dropZone.addEventListener("drop", e => {
    e.preventDefault();
    isDragover.value = false;
  });
};

// 在组件挂载后添加事件
onMounted(() => {
  addCustomDragEvents();
});

const emits = defineEmits<{
  (e: "onUploadFile", file: UploadRawFile): void;
}>();

const uploadRef = ref<UploadInstance>();
const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

const onChangeUploadFile: UploadProps["onChange"] = uploadFile => {
  emits("onUploadFile", uploadFile.raw);
};
</script>

<style scoped lang="scss">
.upload-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  justify-content: center;
  cursor: pointer;
  position: relative;

  .upload-icon {
    color: var(--el-color-primary);
  }

  .el-upload {
    height: 100%;
  }

  .tip {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    letter-spacing: 0;
    color: #606266;
    margin-left: 10px;
  }

  .quotation-upload {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 100%;
    }
  }

  &.is-dragover {
    background-color: var(--el-color-primary-light-9);
    outline: 0.125rem dashed var(--el-color-primary);
    border-radius: 4px;
  }
}
</style>
