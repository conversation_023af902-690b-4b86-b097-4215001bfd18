<template>
  <div class="bg-bg_color h-full flex flex-col flex-1 overflow-hidden">
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="state.productVersions"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="loading"
      highlight-current-row
      :row-style="{ cursor: 'pointer' }"
      @current-change="onCurrentChange($event)"
    >
      <template #status="{ row }">
        <CXTag :type="ImportProductVersionStatusEnumMapColor[row.status]">{{
          ImportProductVersionStatusEnumMapDesc[row.status]
        }}</CXTag>
      </template>
      <template #selection="{ row }">
        <el-radio-group v-model="row.selection" @change="onChange(row.id)">
          <el-radio :label="true">{{ "" }}</el-radio>
        </el-radio-group>
      </template>
      <template #empty>
        <CxEmptyData />
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { useColumns } from "./columns";
import { onMounted, reactive, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {} from "@/api/basic/product-info";
import { queryAllProductVersion } from "@/api/product-version";
import { IProductVersion } from "@/models";
import CxEmptyData from "@/components/CxEmpty";
import CXTag from "@/components/CxTag/index.vue";
import { ImportProductVersionStatusEnumMapDesc, ImportProductVersionStatusEnumMapColor } from "@/enums";

interface IProductVersionExt extends IProductVersion {
  selection?: boolean;
}

const emit = defineEmits<{
  (e: "onSelectProductVersion", data: IProductVersion): void;
}>();

const { columns } = useColumns();
const { pagination } = useTableConfig();
const loading = ref();

const props = withDefaults(
  defineProps<{
    versionId?: string;
  }>(),
  {}
);

const state = reactive<{
  productVersions: Array<IProductVersionExt>;
}>({
  productVersions: []
});

onMounted(() => {
  requestList();
});

const onCurrentChange = (data: IProductVersionExt) => {
  onChange(data);
};

const onChange = (data: IProductVersionExt) => {
  state.productVersions.map(item => (item.selection = item.id === data.id));
  emit("onSelectProductVersion", data);
};
const requestList = useLoadingFn(async () => {
  const { data } = await queryAllProductVersion();
  state.productVersions = data.map<IProductVersionExt>(item => ({ ...item, selection: item.id === props.versionId }));
  pagination.total = data.length;
  emit(
    "onSelectProductVersion",
    state.productVersions?.find(x => x.id === props.versionId)
  );
}, loading);
</script>

<style scoped lang="scss"></style>
