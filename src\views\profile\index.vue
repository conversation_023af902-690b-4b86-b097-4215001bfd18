<template>
  <div class="profile-page">
    <div class="page-left">
      <div class="mb-7">
        <div class="avatar-box mb-5">
          <el-avatar v-if="imageUrl" :size="144" fit="cover" :src="imageUrl" />
          <el-avatar v-else :size="144" fit="cover" :src="imageIcon" />
          <el-upload
            ref="uploadRef"
            class="avatar-btn"
            :auto-upload="false"
            :on-change="avatarChange"
            :limit="1"
            accept="image/*"
          >
            <el-button link :icon="EditPen" />
          </el-upload>
        </div>
        <div class="flex-c flex-col mb-4">
          <div class="text-lg font-semibold mb-2">
            <span>{{ userStore.profile.nickname }}</span>
          </div>
          <div class="text-base mb-5">
            <span>{{ userStore.profile.remark || "持续成长，不断进步" }}</span>
          </div>
        </div>
        <div class="flex flex-col gap-1.5 px-1">
          <div class="flex-bc text-base">
            <span>用户名</span>
            <span class="text-secondary">{{ userStore.profile.username }}</span>
          </div>
          <div class="flex-bc text-base">
            <span>联系电话</span>
            <span class="text-secondary">{{ userStore.profile.mobile }}</span>
          </div>
          <div class="flex-bc text-base" v-if="userStore.profile.email">
            <span>电子邮件</span>
            <span class="text-secondary">{{ userStore.profile.email }}</span>
          </div>
        </div>
      </div>

      <div
        class="profile-tab"
        :class="{ 'activate-tab': getActivateProfileTab('base-info') }"
        @click="onSetActivateProfileTab('base-info')"
      >
        <IconifyIconOffline :icon="User" />
        <span>个人信息</span>
      </div>
      <div
        class="profile-tab"
        :class="{ 'activate-tab': getActivateProfileTab('edit-password') }"
        @click="onSetActivateProfileTab('edit-password')"
      >
        <IconifyIconOffline :icon="Key" />
        <span>密码安全</span>
      </div>
    </div>
    <div class="page-right">
      <baseInfo v-show="getActivateProfileTab('base-info')" />
      <Password v-show="getActivateProfileTab('edit-password')" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watchEffect } from "vue";
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
import { useUserStore } from "@/store/modules/user";
import Password from "./password.vue";
import baseInfo from "./base-info.vue";
import User from "@iconify-icons/ri/user-fill";
import Key from "@iconify-icons/ri/shield-keyhole-fill";
import { ElMessage, UploadFile } from "element-plus";
import { EditPen } from "@element-plus/icons-vue";
import imageIcon from "@/assets/img/avatar-icon.png";
import { postMessage } from "@/utils/browsingContextsCommunication";
import { TopicEnum } from "@/utils/browsingContextsCommunication/topic.enum";
import { IProfile } from "@/models/user";
import { cloneDeep } from "lodash-unified";

const uploadRef = ref();

const userStore = useUserStore();
const imageUrl = ref("");
const route = useRoute();

usePageStoreHook().setTitle((useRoute().meta?.title as string) || "个人中心");

type activateProfileTab = "base-info" | "edit-password";

const state = reactive<{
  activateProfileTab: activateProfileTab;
}>({
  activateProfileTab: "base-info"
});

watchEffect(() => {
  if (userStore.profile && Object.keys(userStore.profile).length) {
    imageUrl.value = userStore.profile?.avatarInfo?.url
      ? userStore.profile.avatarInfo.url
      : userStore.profile.nickname.charAt(0);
  }
});

const onSetActivateProfileTab = (activateProfileTab: activateProfileTab) => {
  state.activateProfileTab = activateProfileTab;
};

const getActivateProfileTab = (activateProfileTab: activateProfileTab) => {
  return state.activateProfileTab === activateProfileTab;
};

// 上传头像
const avatarChange = async (avatar: UploadFile) => {
  const formData = new FormData();
  formData.append("avatarFile", avatar.raw);
  uploadRef.value?.handleRemove(avatar);
  const uploadRes = await userStore.editAvatar(formData);
  imageUrl.value = uploadRes.data;

  if (!userStore.profile.avatar) {
    userStore.profile.avatarInfo = { url: uploadRes.data };
  } else {
    userStore.profile.avatarInfo.url = uploadRes.data;
  }
  const profile: IProfile = { ...userStore.profile };
  userStore.setUserProfile(profile);
  postMessage(TopicEnum.UPDATE_PROFILE, cloneDeep(profile));
  ElMessage.success("头像更新成功");
};

onMounted(() => {
  if (route.query && route.query.item === "password") {
    state.activateProfileTab = "edit-password";
  }
});
</script>

<style scoped lang="scss">
.profile-page {
  @apply flex w-[1080px] overflow-hidden h-full m-auto my-5;

  .page-left {
    @apply bg-bg_color w-96 p-10 mr-6 rounded;
  }

  .page-right {
    @apply bg-bg_color flex-1 p-6 rounded;
  }
}

.profile-tab {
  @apply flex items-center gap-2 px-4 py-3 mb-2 text-base rounded;

  &:hover {
    cursor: pointer;
    color: var(--el-color-primary);
  }

  &.activate-tab {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

.avatar-box {
  position: relative;
  text-align: center;

  // .avatar-icon {
  //   .el-icon {
  //     font-size: 110px !important;
  //   }
  // }

  .avatar-btn {
    position: absolute;
    bottom: 0;
    right: 25%;

    .el-button {
      @apply text-secondary;
    }

    &:hover {
      .el-button {
        @apply text-regular;
      }
    }
  }
}

:deep(.el-upload-list) {
  margin: 0;
  display: none;
}
</style>
