import { http } from "@/utils/http";
import { IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { IAuth, IWeChatQRCode } from "@/models/user";

/** 登录 */
export const getLoginByWeChatQRCode = () => {
  const url: string = withApiGateway("admin-api/system/auth/wechat/qrcode");
  return http.request<IResponse<IWeChatQRCode>>("get", url);
};

/** 根据账号id获取微信二维码 */
export const getWeChatBindQRCodeByAccountId = (id: string) => {
  const url: string = withApiGateway(`admin-api/system/account/wechat/qrcode/${id}`);
  return http.request<IResponse<IWeChatQRCode>>("get", url);
};

/** 轮询扫描登录结果 */
export const checkWeChatLogin = (sceneId: string) => {
  const url: string = withApiGateway(`admin-api/system/auth/wechat/checkScan/${sceneId}`);
  return http.request<IResponse<IAuth>>("get", url);
};

/** 轮询扫码绑定结果 */
export const checkWeChatBind = (sceneId: string) => {
  const url: string = withApiGateway(`admin-api/system/user/profile/checkScan-for-bind/${sceneId}`);
  return http.request<IResponse<IAuth>>("get", url);
};

/**  解绑微信绑定 */
export const WeChatLoginUnbind = () => {
  const url: string = withApiGateway(`admin-api/system/user/profile/unbind`);
  return http.request<IResponse<IWeChatQRCode>>("get", url);
};

/**  解绑微信绑定 员工Id */
export const weChatLoginUnbindById = (id: string) => {
  const url: string = withApiGateway(`admin-api/system/user/profile/unbind/${id}`);
  return http.request<IResponse<IWeChatQRCode>>("get", url);
};
