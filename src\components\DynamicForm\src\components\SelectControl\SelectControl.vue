<template>
  <div class="select">
    <el-select
      :model-value="inputValue"
      placeholder="请选择"
      filterable
      :disabled="!!config?.disable"
      @change="optionChange($event)"
    >
      <el-option v-for="item in selectOptions.optionList" :key="item.key" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash-unified";
import { EControlType } from "@/enums";
import { useTableForm } from "../../form-value-hooks";

const props = defineProps({
  inputValue: {
    type: String,
    default: ""
  },
  config: {
    type: Object,
    default: () => ({})
  }
});

const emits = defineEmits(["valueChange"]);

// 获取下拉框的值
const { getSelectOptions, selectOptions } = useTableForm();
const rowTypeData = cloneDeep(props.config);
getSelectOptions(rowTypeData);
/**
 * 控件值更新
 */
const optionChange = value => {
  emits("valueChange", {
    value,
    key: EControlType.SelectControl,
    config: props.config
  });
};
</script>

<style scoped lang="scss"></style>
