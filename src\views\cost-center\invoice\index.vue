<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="订单编号：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.orderNo" placeholder="请输入订单编号" />
        </ElFormItem>

        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <UploadPaymentVoucherDialog mode="add" @post-save-success="onQuery()">
        <template #trigger="{ openDialog }">
          <el-button class="mb-5" :icon="Plus" type="primary" @click="openDialog">上传充值记录</el-button>
        </template>
      </UploadPaymentVoucherDialog>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #operation="data">
          <div>
            <UploadPaymentVoucherDialog mode="edit" :id="data.row.id" @post-save-success="onQuery()">
              <template #trigger="{ openDialog }">
                <el-button link type="primary" @click="openDialog">编辑</el-button>
              </template>
            </UploadPaymentVoucherDialog>
            <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="invoice">
import { onMounted, ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryInvoice, deleteInvoiceById } from "@/api/cost-center";
import { IInvoice } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import UploadPaymentVoucherDialog from "@/views/cost-center/components/upload-payment-voucher/dialog.vue";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IInvoice>;
  params: { [key: string]: string };
}>({
  list: [],
  params: {}
});

onMounted(() => {
  requestList();
});

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteInvoiceById(id);
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryInvoice(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
