<template>
  <div class="inline-block w-full overflow-hidden">
    <slot :open="openDialog" :close="closeDialog" />
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="300px"
    >
      <!-- 内容 -->
      <el-scrollbar height="280px">
        <div v-for="item in list" :key="item.id">
          <router-link class="text-primary" :to="`${prePath}/${item.id}`">
            {{ item.no }}
          </router-link>
          <el-divider class="!my-2" />
        </div>
      </el-scrollbar>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { RouterLink } from "vue-router";

/**
 * 多订单情况下使用的选择跳转弹窗面板
 */

interface Item {
  /** 订单号 */
  no: string;
  /** 订单id */
  id: string;
}

withDefaults(
  defineProps<{
    list: Array<Item>;
    prePath: string;
    title?: string;
  }>(),
  {
    title: "选择订单",
    list: () => []
  }
);

const dialogVisible = ref(false);

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
