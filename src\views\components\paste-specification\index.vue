<template>
  <div class="flex flex-col">
    <el-alert title="粘贴规格型号相关的文字信息" type="info" show-icon :closable="false" />
    <div class="mt-3">
      <el-input v-model="content" :rows="20" type="textarea" placeholder="请输入" @change="onChangeContent()" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const emits = defineEmits<{
  (e: "onChangeContent", value: string): void;
}>();

const content = ref();
const onChangeContent = () => {
  emits("onChangeContent", content.value);
};
</script>

<style scoped lang="scss"></style>
