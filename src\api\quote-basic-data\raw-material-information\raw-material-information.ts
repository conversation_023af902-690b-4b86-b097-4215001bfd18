import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IListResponse,
  IResponse,
  IRawMaterialInformation,
  IRawMaterialInformationForm,
  IRawMaterialInformationReq,
  IRawMaterialInformationCost
} from "@/models";

/** 查询原材料信息分页  */
export const queryRawMaterialInformation = (data: IRawMaterialInformationReq) => {
  const url: string = withApiGateway("admin-api/business/bom/raw/page");
  return http.post<IRawMaterialInformationReq, IListResponse<IRawMaterialInformation>>(url, {
    data
  });
};

/** 根据原材料信息id 查询详情 */
export const getRawMaterialInformationById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/bom/raw/get?id=${id}`);
  return http.get<string, IResponse<IRawMaterialInformation>>(url);
};

/** 新增原材料信息 */
export const createRawMaterialInformation = (data: IRawMaterialInformationForm) => {
  return http.post<IRawMaterialInformationForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/bom/raw/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑原材料信息 */
export const updateRawMaterialInformation = (data: IRawMaterialInformationForm) => {
  return http.post<IRawMaterialInformationForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/bom/raw/update"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除原材料信息根据Id */
export const deleteRawMaterialInformationById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/business/bom/raw/delete?id=${id}`));
};

/** 批了删除原材料信息 */
export const batchDeleteRawMaterialInformationById = (ids: Array<string>) => {
  return http.delete<{ idList: Array<string> }, IResponse<boolean>>(
    withApiGateway(`admin-api/business/bom/raw/delete-batch`),
    { data: { idList: ids } }
  );
};

/** 获取指定原材料 历史单价版本信息 */

export const getRawMaterialInformationCostListById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/bom/raw/getBomRawCostListByRawId?bomRawId=${id}`);
  return http.get<string, IResponse<Array<IRawMaterialInformationCost>>>(url);
};
