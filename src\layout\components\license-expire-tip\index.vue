<template>
  <span />
</template>

<script setup lang="ts">
import { useSystemAuthStore } from "@/store/modules";
import { formatDate } from "@vueuse/core";
import { watch, onMounted } from "vue";
import { ElNotification } from "element-plus";

const systemAuthStore = useSystemAuthStore();
onMounted(() => {
  watch(
    () => systemAuthStore.businessLicenseAuth,
    businessLicenseAuth => {
      if (
        businessLicenseAuth &&
        Object.keys(businessLicenseAuth)?.length &&
        !businessLicenseAuth?.isAuthorization &&
        !businessLicenseAuth?.authorization?.companyName
      ) {
        notification("系统未授权！", "请联系管理员进行系统授权");
        return;
      }

      if (businessLicenseAuth?.authorization?.expireDays <= 30) {
        notification(licenseExpired() ? "授权到期" : "授权即将到期", getTip());
      }
    },
    { immediate: true }
  );
});

function getTip() {
  const expireDate = systemAuthStore.businessLicenseAuth?.authorization?.expireDate;
  return expireDate
    ? `系统${licenseExpired() ? "授权" : "授权即将"}到期，请联系管理员，到期时间为 ${formatDate(
        new Date(expireDate),
        "YYYY年MM月DD日"
      )}`
    : null;
}

function licenseExpired() {
  return (systemAuthStore.businessLicenseAuth?.authorization?.expireDays || 0) <= 0;
}

function notification(title: string, message: string) {
  ElNotification({
    title,
    message,
    type: "warning"
  });
}
</script>

<style scoped lang="scss"></style>
