import { h, Ref } from "vue";
import { TableColumnRenderer } from "@pureadmin/table";
import { ElRadioGroup, ElRadio } from "element-plus";
import { getCellValue } from "./tool";

export const SingleSelectCell = (selected: Ref<string | number>, data: TableColumnRenderer) => {
  const value = getCellValue<string | number>(data);
  const radio = h(ElRadio, { label: value, style: { margin: "0 !important", width: "14px" } }, () => "");
  return h(ElRadioGroup, { modelValue: selected.value, style: { verticalAlign: "-2px" } }, () => radio);
};
