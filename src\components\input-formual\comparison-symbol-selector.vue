<template>
  <el-select class="w-full text-center" :disabled="disabled" v-model="modelValue" filterable>
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useVModels } from "@vueuse/core";
import { ComparisonSymbolEnum, ComparisonSymbolMap } from "./types";

/**
 * 公式比较符号选择器
 */

interface IOption {
  label: string;
  value: number;
}

const props = defineProps<{
  /** 已选中类别的id */
  modelValue: number;
  disabled?: boolean;
}>();

const emits = defineEmits<{
  (event: "update:modelValue", id: string): void;
  (event: "update:processCode", code: string): void;
}>();

const { modelValue } = useVModels(props, emits);

const options = ref<Array<IOption>>([
  {
    label: ComparisonSymbolMap[ComparisonSymbolEnum.LessThanValue],
    value: ComparisonSymbolEnum.LessThanValue as number
  },
  {
    label: ComparisonSymbolMap[ComparisonSymbolEnum.LessThanOrEqualValue],
    value: ComparisonSymbolEnum.LessThanOrEqualValue as number
  }
]);
</script>
