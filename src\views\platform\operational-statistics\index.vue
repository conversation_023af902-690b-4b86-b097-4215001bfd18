<template>
  <div class="bg-bg_color pt-[8px] px-6 flex justify-between">
    <el-tabs v-model="activeName">
      <el-tab-pane label="汇总" name="汇总" />
      <el-tab-pane label="明细" name="明细" />
    </el-tabs>
    <div class="text-right mb-3">
      <el-button size="large" type="primary" :icon="Refresh" @click="query"> 刷新 </el-button>
      <el-button size="large" type="primary" :icon="Download" @click="queryOperationalStatisticsExport">
        导出
      </el-button>
    </div>
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <PureTable
      v-show="activeName === '汇总'"
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      size="large"
      :data="operationalStatisticsStore.operationalStatisticsList"
      :columns="columns"
      showOverflowTooltip
      :loading="operationalStatisticsStore.loading"
    >
      <template #useTokenCount="{ row }">
        {{ formatThousands(row.useTokenCount || 0) }}
      </template>
    </PureTable>

    <PureTable
      v-show="activeName === '明细'"
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      size="large"
      :data="operationalStatisticsStore.operationalStatisticsDetailList"
      :columns="detailColumns"
      showOverflowTooltip
      :loading="operationalStatisticsStore.loading"
    >
      <template #useTokenCount="{ row }">
        {{ formatThousands(row.useTokenCount || 0) }}
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { useColumns } from "./columns";
import { usePageStoreHook } from "@/store/modules/page";
import { useOperationalStatisticsStore } from "@/store/modules";
import { useRoute } from "vue-router";
import { ElButton } from "element-plus";
import { operationalStatisticsExport } from "@/api/platform/operational-statistics";
import { downloadByData } from "@pureadmin/utils";
import { Download, Refresh } from "@element-plus/icons-vue";
import { ref } from "vue";
import { formatThousands } from "@/utils/format";

usePageStoreHook().setTitle((useRoute().meta?.title as string) || "运营统计");

const { columns, detailColumns } = useColumns();
const operationalStatisticsStore = useOperationalStatisticsStore();

query();

function query() {
  queryOperationalStatistics();
  queryOperationalStatisticsLiat();
}

const queryOperationalStatisticsExport = async () => {
  operationalStatisticsExport().then(response => {
    downloadByData(response, "运营统计.xlsx");
  });
};

function queryOperationalStatistics() {
  operationalStatisticsStore.queryOperationalStatistics();
}

function queryOperationalStatisticsLiat() {
  operationalStatisticsStore.queryOperationalStatisticsLiat();
}

const activeName = ref("汇总");
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}

.content {
  height: 500px; /* 设置最大高度 */
}
</style>
