import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse, IInvoice, IInvoiceForm, IInvoiceReq } from "@/models";

/** 查询全部账单管理 */
export const queryAllInvoice = (params?: IInvoiceReq) => {
  const url: string = withApiGateway(`admin-api/business/invoice/all-invoice`);
  return http.get<IInvoiceReq, IResponse<Array<IInvoice>>>(url, {
    params
  });
};

/** 查询账单管理分页  */
export const queryInvoice = (params: IInvoiceReq) => {
  const url: string = withApiGateway(`admin-api/business/invoice/invoice`);
  return http.get<IInvoiceReq, IListResponse<IInvoice>>(url, {
    params
  });
};

/** 根据账单管理id 查询详情 */
export const getInvoiceById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/invoice/invoice-detail-by-id/${id}`);
  return http.get<string, IResponse<IInvoice>>(url);
};

/** 新增账单管理 */
export const createInvoice = (data: IInvoiceForm) => {
  return http.post<IInvoiceForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/invoice/invoice"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑账单管理 */
export const updateInvoice = (data: IInvoiceForm) => {
  return http.put<IInvoiceForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/invoice/invoice"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除账单管理根据Id */
export const deleteInvoiceById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/business/invoice/invoice-by-id/${id}`));
};
