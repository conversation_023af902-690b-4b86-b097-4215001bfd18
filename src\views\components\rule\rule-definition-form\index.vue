<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="left">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" clearable placeholder="请输入规则名称" />
        </el-form-item>
      </el-col>
      <el-col :span="4">
        <el-form-item label="优先级" prop="priority">
          <el-input-number
            class="!w-full"
            v-model="form.priority"
            clearable
            controls-position="right"
            placeholder="请输优先级"
          />
        </el-form-item>
      </el-col>
      <el-col :span="3">
        <el-form-item label="规则状态" prop="enabled">
          <el-switch
            clearable
            v-model="form.enabled"
            :active-value="true"
            :inactive-value="false"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
          />
        </el-form-item>
      </el-col>
      <el-col :span="3">
        <el-form-item label="单条规则匹配后停止" prop="stopIfMatched">
          <el-switch
            clearable
            v-model="form.stopIfMatched"
            :active-value="true"
            :inactive-value="false"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
          />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item class="!mb-[12px]" label="匹配字段" prop="matchFieldStrategy">
          <el-radio-group v-model="form.matchFieldStrategy">
            <el-radio :label="item.value" v-for="(item, index) in MatchFieldStrategyOptions" :key="index">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="14">
        <el-form-item class="!mb-[12px]" label="备注" prop="remark">
          <el-input :rows="2" type="textarea" v-model="form.remark" clearable placeholder="请输入备注" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item class="!mb-[12px]" label="最终展示型号" prop="displayStrategy">
          <el-radio-group v-model="form.displayStrategy">
            <el-radio :label="item.value" v-for="(item, index) in FinialDisplayStrategyOptions" :key="index">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item class="!mb-[12px]" label="最终展示电压" prop="displayStrategyVoltage">
          <el-radio-group v-model="form.displayStrategyVoltage">
            <el-radio :label="item.value" v-for="(item, index) in FinialDisplayStrategyVoltageOptions" :key="index">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <div class="font-bold text-text_color_primary text-base">适用范围（需同时满足下列型号、电压、规格的条件）</div>
      <div class="w-full">
        <TitleBar class="mt-2" title="型号" />
        <el-col :span="24">
          <el-row :gutter="20">
            <el-col :span="3">
              <el-form-item prop="modelMatchRule">
                <el-radio
                  :label="ModelMatchRule.BuildInMatch"
                  v-model="form.modelMatchRule"
                  class="!mr-1"
                  @change="onModelMatchRuleChange($event)"
                  >匹配逻辑</el-radio
                >
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item prop="matchType">
                <el-select
                  placeholder="请选择匹配逻辑符"
                  class="w-full"
                  clearable
                  filterable
                  v-model="form.modelCondition.matchType"
                  @change="onChangeModelConditionMatchType()"
                >
                  <el-option
                    v-for="item in state.modelMatchRuleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item prop="modelCondition.matchValue">
                <el-input v-model="form.modelCondition.matchValue" clearable placeholder="请输入值" />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="6">
              <el-form-item>
                <div>(型号=ZN-BRV)</div>
              </el-form-item>
            </el-col> -->
            <el-col :span="3">
              <el-form-item class="!mb-2" prop="modelMatchRule">
                <el-radio
                  :label="ModelMatchRule.Expression"
                  class="!mr-1"
                  v-model="form.modelMatchRule"
                  @change="onModelMatchRuleChange($event)"
                  >表达式</el-radio
                >
              </el-form-item>
            </el-col>
            <el-col :span="21">
              <el-form-item class="!mb-2" prop="modelMatchExpressionValue">
                <el-input
                  v-model="form.modelMatchExpressionValue"
                  clearable
                  placeholder="请输入正则，建议联系程析技术支持处理"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </div>
      <div class="w-full">
        <TitleBar class="mt-2" title="电压-适用范围" />
        <el-col :span="24">
          <el-row :gutter="20">
            <el-col :span="24" class="mb-1">
              <el-checkbox v-model="form.voltageCondition.matchAll" @change="onChangeVoltageMatchAll()"
                >所有电压</el-checkbox
              >
              <el-checkbox v-model="form.voltageCondition.matchEmpty" @change="onChangeVoltageMatchEmpty()"
                >无电压</el-checkbox
              >
              <el-checkbox v-model="form.voltageCondition.matchRange" :disabled="form.voltageCondition.matchAll"
                >指定范围</el-checkbox
              >
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-input-number
                  class="!w-full"
                  v-model="form.voltageCondition.lowerBoundX"
                  clearable
                  controls-position="right"
                  placeholder="下边界值X"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-select
                  placeholder="请选择逻辑符"
                  class="w-full"
                  clearable
                  filterable
                  v-model="form.voltageCondition.lowerOperator"
                >
                  <el-option
                    v-for="item in FieldOperatorOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-input-number
                  class="!w-full"
                  v-model="form.voltageCondition.lowerBoundY"
                  clearable
                  controls-position="right"
                  placeholder="下边界值Y"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-input-number
                  class="!w-full"
                  v-model="form.voltageCondition.upperBoundX"
                  clearable
                  controls-position="right"
                  placeholder="上边界值X"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-select
                  placeholder="请选择逻辑符"
                  class="w-full"
                  clearable
                  filterable
                  v-model="form.voltageCondition.upperOperator"
                >
                  <el-option
                    v-for="item in FieldOperatorOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-input-number
                  class="!w-full"
                  v-model="form.voltageCondition.upperBoundY"
                  clearable
                  controls-position="right"
                  placeholder="上边界值Y"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </div>
      <div class="w-full">
        <div class="mt-2"><TitleBar title="规格" /></div>
        <el-col :span="24">
          <el-form-item class="!mb-2" prop="specificationCondition.actionType">
            <el-radio-group
              v-model="form.specificationCondition.matchType"
              @change="onChangeSpecificationScopeMatchType()"
            >
              <el-radio :label="item.value" v-for="(item, index) in ScopeMatchTypeOptions" :key="index">{{
                item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-row :gutter="20" v-if="form.specificationCondition.matchType === ScopeMatchTypeEnum.SPECIFIED_SCOPE">
          <el-col :span="6">
            <el-form-item prop="specificationCondition.scopeOperator">
              <el-select
                placeholder="请选择"
                class="w-full"
                clearable
                filterable
                v-model="form.specificationCondition.scopeOperator"
              >
                <el-option
                  v-for="item in state.specificationScopeOperatorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item prop="specificationCondition.matchValue">
              <el-input clearable placeholder="请输入值" v-model="form.specificationCondition.matchValue" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-col :span="24" v-if="form.specificationCondition.matchType === ScopeMatchTypeEnum.REGEX">
          <el-form-item prop="specificationConditionRegeMatchValue">
            <el-input clearable placeholder="请输入正则" v-model="form.specificationConditionRegeMatchValue" />
          </el-form-item>
        </el-col>

        <el-col :span="24" v-if="form.specificationCondition.matchType === ScopeMatchTypeEnum.EXPRESSION">
          <el-form-item prop="specificationCondition.expression">
            <el-input
              clearable
              placeholder="请输入正则，建议联系程析技术支持处理"
              v-model="form.specificationCondition.expression"
            />
          </el-form-item>
        </el-col>
        <el-row :gutter="20" v-if="form.specificationCondition.matchType === ScopeMatchTypeEnum.LESS_THAN">
          <el-col :span="8">
            <el-form-item prop="specificationCondition.areaMin">
              <el-input-number
                class="!w-full"
                v-model="form.specificationCondition.areaMin"
                clearable
                controls-position="right"
                placeholder="截面最小值"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="specificationCondition.areaOperator">
              <el-select
                placeholder="请选择逻辑符"
                class="w-full"
                clearable
                filterable
                v-model="form.specificationCondition.areaOperator"
              >
                <el-option
                  v-for="item in FieldOperatorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="specificationCondition.areaMax">
              <el-input-number
                class="!w-full"
                v-model="form.specificationCondition.areaMax"
                clearable
                controls-position="right"
                placeholder="截面最大值"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="font-bold text-text_color_primary text-base">处理逻辑</div>
      <div class="w-full">
        <TitleBar class="mt-2" title="型号" />
        <el-col :span="24">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item class="!mb-2" prop="modelAction.actionType">
                <el-radio-group v-model="form.modelAction.actionType">
                  <el-radio :label="item.value" v-for="(item, index) in ActionTypeOptions" :key="index">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <template v-if="form.modelAction.actionType === ActionTypeEnum.REPLACE_MATCH">
              <el-col :span="11">
                <el-form-item prop="modelAction.matchValue">
                  <el-input v-model="form.modelAction.matchValue" clearable placeholder="请输入匹配值" />
                </el-form-item>
              </el-col>
              <el-col :span="2">
                <el-form-item class="!mr-0">替换为</el-form-item>
              </el-col>
            </template>
            <el-col :span="form.modelAction.actionType === ActionTypeEnum.REPLACE_MATCH ? 11 : 24">
              <el-form-item prop="modelAction.value">
                <el-input v-model="form.modelAction.value" clearable placeholder="请输入替换后的值" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </div>
      <div class="w-full">
        <TitleBar class="mt-2" title="规格" />
        <el-col :span="24">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item class="!mb-2" prop="specificationAction.actionType">
                <el-radio-group v-model="form.specificationAction.actionType">
                  <el-radio :label="item.value" v-for="(item, index) in ActionTypeOptions" :key="index">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <template v-if="form.specificationAction.actionType === ActionTypeEnum.REPLACE_MATCH">
              <el-col :span="11">
                <el-form-item prop="specificationAction.matchValue">
                  <el-input v-model="form.specificationAction.matchValue" clearable placeholder="请输入匹配值" />
                </el-form-item>
              </el-col>
              <el-col :span="2">
                <el-form-item class="text-center">替换为 </el-form-item>
              </el-col>
            </template>
            <el-col :span="form.specificationAction.actionType === ActionTypeEnum.REPLACE_MATCH ? 11 : 24">
              <el-form-item prop="specificationAction.value">
                <el-input v-model="form.specificationAction.value" clearable placeholder="请输入替换后的值" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </div>
      <div class="w-full">
        <TitleBar class="mt-2" title="电压" />
        <el-col :span="24">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item class="!mb-2" prop="voltageAction.actionType">
                <el-radio-group v-model="form.voltageAction.actionType">
                  <el-radio :label="item.value" v-for="(item, index) in ActionTypeOptions" :key="index">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <template v-if="form.voltageAction.actionType === ActionTypeEnum.REPLACE_MATCH">
              <el-col :span="11">
                <el-form-item prop="voltageAction.matchValue">
                  <el-input v-model="form.voltageAction.matchValue" clearable placeholder="请输入匹配值" />
                </el-form-item>
              </el-col>
              <el-col :span="2">
                <el-form-item class="text-center">替换为 </el-form-item>
              </el-col>
            </template>
            <el-col :span="form.voltageAction.actionType === ActionTypeEnum.REPLACE_MATCH ? 11 : 24">
              <el-form-item prop="voltageAction.value">
                <el-input v-model="form.voltageAction.value" clearable placeholder="请输入替换后的值" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </div>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IOption, IRuleDefinition } from "@/models";
import {
  MatchFieldStrategyOptions,
  FinialDisplayStrategyOptions,
  ModeMatchlRule,
  ScopeMatchTypeEnum,
  ScopeOperatorOptions,
  ActionTypeOptions,
  ActionTypeEnum,
  FieldOperatorOptions,
  FieldMatchTypeEnum,
  ScopeMatchTypeOptions,
  FinialDisplayStrategyVoltageOptions
} from "@/enums";
import TitleBar from "@/components/TitleBar/index";

/** 型号匹配规则 */
enum ModelMatchRule {
  /** 内置匹配逻辑 */
  BuildInMatch = "builtInMatching",

  /** 表达式 */
  Expression = "expression"
}

interface IRuleDefinitionForm extends IRuleDefinition {
  modelMatchRule?: ModelMatchRule;
  modelMatchExpressionValue?: string;
  specificationConditionRegeMatchValue?: string;
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  emptyFormValue
});

const form = reactive<IRuleDefinitionForm>({
  modelMatchRule: ModelMatchRule.BuildInMatch,
  enabled: true,
  stopIfMatched: true,
  modelCondition: {},
  specificationCondition: {
    matchType: ScopeMatchTypeEnum.ALL
  },
  modelAction: {
    actionType: ActionTypeEnum.NO_ACTION
  },
  specificationAction: {
    actionType: ActionTypeEnum.NO_ACTION
  },
  voltageCondition: {},
  voltageAction: {
    actionType: ActionTypeEnum.NO_ACTION
  }
});

const formRef = ref<FormInstance>();
const state = reactive<{
  modelMatchRuleOptions: Array<IOption>;
  specificationScopeOperatorOptions: Array<IOption>;
}>({
  modelMatchRuleOptions: ModeMatchlRule[ModelMatchRule.BuildInMatch],
  specificationScopeOperatorOptions: ScopeOperatorOptions
});

const rules: FormRules = {
  ruleName: [{ required: true, trigger: "change", message: "规则名称不能为空" }],
  priority: [{ required: true, trigger: "change", message: "优先级不能为空" }],
  enabled: [{ required: true, trigger: "change", message: "规则状态不能为空" }],
  matchFieldStrategy: [{ required: true, trigger: "change", message: "信息来源不能为空" }],
  displayStrategy: [{ required: true, trigger: "change", message: "最终展示型号不能为空" }]
};
/** 型号 匹配规则切换 */
const onModelMatchRuleChange = (value: string | number | boolean) => {
  const rule: ModelMatchRule = value as ModelMatchRule;
  if (rule === ModelMatchRule.BuildInMatch) {
    form.modelMatchExpressionValue = "";
  } else {
    form.modelCondition = {};
  }
};

/** 内置匹配 逻辑- 修改逻辑符 */
const onChangeModelConditionMatchType = () => {
  form.modelCondition.matchValue = null;
};

/** 规格 匹配类型 */
const onChangeSpecificationScopeMatchType = () => {
  form.specificationCondition.expression = null;
  form.specificationCondition.scopeOperator = null;
  form.specificationCondition.matchValue = null;
  form.specificationConditionRegeMatchValue = null;
};
const onChangeVoltageMatchAll = () => {
  form.voltageCondition.matchEmpty = false;
  form.voltageCondition.matchRange = false;
  form.voltageCondition.upperBoundX = null;
  form.voltageCondition.upperBoundY = null;
  form.voltageCondition.upperOperator = null;
  form.voltageCondition.lowerBoundX = null;
  form.voltageCondition.lowerBoundY = null;
  form.voltageCondition.lowerOperator = null;
};

const onChangeVoltageMatchEmpty = () => {
  form.voltageCondition.matchAll = false;
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(data: IRuleDefinitionForm) {
  Object.assign(form, data);

  if (data.modelCondition.matchType === FieldMatchTypeEnum.EXPRESSION) {
    form.modelMatchRule = ModelMatchRule.Expression;
    form.modelMatchExpressionValue = data.modelCondition.matchValue;
  }

  if (data.specificationCondition.matchType === ScopeMatchTypeEnum.REGEX) {
    form.specificationConditionRegeMatchValue = data.specificationCondition.matchValue;
    form.specificationCondition.matchValue = null;
  }
}

function emptyFormValue() {
  formRef.value.resetFields();
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  const formValue: IRuleDefinitionForm = Object.assign({}, form);
  if (formValue.modelMatchRule === ModelMatchRule.Expression) {
    formValue.modelCondition = {
      matchType: FieldMatchTypeEnum.EXPRESSION,
      matchValue: formValue.modelMatchExpressionValue
    };
  }

  if (formValue.specificationCondition.matchType === ScopeMatchTypeEnum.REGEX) {
    formValue.specificationCondition.matchValue = formValue.specificationConditionRegeMatchValue;
  }

  delete formValue.specificationConditionRegeMatchValue;
  delete formValue.modelMatchExpressionValue;
  delete formValue.modelMatchRule;
  return formValue;
}
</script>
