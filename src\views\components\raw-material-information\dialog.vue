<template>
  <div class="inline-block">
    <el-dialog
      v-model="modelValue"
      title="选择原材料"
      align-center
      width="85%"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <RawMaterialInformation @onSelect="handleOnSelect($event)" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="onConfirm()">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import RawMaterialInformation from "./index.vue";
import { IRawMaterialInformation } from "@/models";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onSelect", data: IRawMaterialInformation): void;
}>();

let rawMaterialInformation: IRawMaterialInformation;

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    versionId?: string;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

const handleOnSelect = (data: IRawMaterialInformation) => {
  rawMaterialInformation = data;
};

/**
 *  保存按钮点击事件
 */
async function onConfirm() {
  emits("onSelect", rawMaterialInformation);
  modelValue.value = false;
}

function closeDialog() {
  emits("onSelect", null);
  modelValue.value = false;
}
</script>

<style scoped></style>
