import { defineStore } from "pinia";
import * as api from "@/api/business-license";
import { IBusinessLicense, ILicenseVerify } from "@/models";
import { LicenseEnum } from "@/enums";

export const useSystemAuthStore = defineStore({
  id: "cx-system-auth-store",
  state: () => ({
    /** license 授权信息，品类 功能列表格式化具体信息 用于展示授权具体信息 */
    businessInfoLicense: {} as IBusinessLicense,

    /** license 授权信息，品类 功能列表没有格式化 用于系统判断权限 */
    businessLicenseAuth: {} as IBusinessLicense
  }),

  getters: {
    /** 授权 是否为企业版本 */
    async getIsEnterprise(state) {
      if (!state.businessLicenseAuth || Object.keys(state.businessLicenseAuth).length === 0) {
        state.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
      }
      return state.businessLicenseAuth?.authorization?.edition === LicenseEnum.Enterprise;
    }
  },

  actions: {
    /** 查询以及授权的租户列表  */
    async queryAuthTenantList() {
      return await api.queryAuthTenantList();
    },

    async queryUnAuthTenant() {
      return await api.queryUnAuthTenant();
    },

    /** 获取单个租户授权信息 */
    async getTenantAuthInfo(id: string) {
      return await api.getTenantAuthInfo(id);
    },

    /** 获取单个租户授权信息[详细信息，包含物质，品类等] */
    async getTenantAuthDisplay(id: string) {
      return await api.getTenantAuthDisplay(id);
    },

    /** license 授权信息，品类 功能列表格式化具体信息 用于展示授权具体信息 */
    async getBusinessLicenseInfo() {
      this.businessInfoLicense = (await api.getBusinessLicenseInfo()).data;
    },

    /** license 授权信息，品类 功能列表没有格式化 用于系统判断权限 */
    async getBusinessLicenseAuth() {
      this.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
    },

    /** 授权 */
    businessLicenseVerify(license: ILicenseVerify) {
      return api.businessLicenseVerify(license);
    },

    setBusinessLicenseAuth(businessLicenseAuth?: IBusinessLicense) {
      this.businessLicenseAuth = businessLicenseAuth;
    }
  }
});
