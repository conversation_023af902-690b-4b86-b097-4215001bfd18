import { PureHttpRequestConfig } from "@/utils/http/types";
import { UNAUTHORIZED } from "@/consts";
import { getToken, removeToken } from "@/utils/auth";
import { useUserStoreHook } from "@/store/modules/user";
import { AxiosInstance } from "axios";
import { ElMessageBox } from "element-plus";
import { router } from "@/router";
import { RouteLocationRaw } from "vue-router";

/** 刷新token url */
const refreshTokenUrl = "/auth/refresh";
/** 登录页path */
const loginPath = "/login";
/** 防止重复刷新token */
let isRefreshing = false;
/** token过期后，暂存待执行的请求 */
let requests: Array<() => void> = [];
let hasAuthTimeoutBox = false;
const returnUrlBlackList = ["/", "/login"];

export function unauthorizedInterceptor(instance: AxiosInstance): number {
  return instance.interceptors.response.use(
    response => response,
    error => {
      if (error.code !== UNAUTHORIZED) {
        return Promise.reject(error);
      }
      const config: PureHttpRequestConfig = error.config;
      if (config.url.includes(refreshTokenUrl)) {
        return goToLogin(error);
      }

      if (isRefreshing) {
        return new Promise(resolve => requests.push(() => resolve(instance(config))));
      }
      const refreshToken: string | undefined = getToken()?.refreshToken;
      if (!refreshToken) {
        return goToLogin(error);
      }

      isRefreshing = true;
      return useUserStoreHook()
        .refreshAccessToken(refreshToken)
        .then(() => {
          requests.forEach(cb => cb());
          requests = [];
          return instance(config);
        })
        .catch(e => goToLogin(e))
        .finally(() => (isRefreshing = false));
    }
  );
}

function goToLogin(error): Promise<void> {
  if (useUserStoreHook().isManualLogOut) {
    return;
  }
  error.config.silentError = true;
  removeToken();
  !location.pathname.includes(loginPath) && showLogoutMessage();
  return Promise.reject(error);
}

function showLogoutMessage(): void {
  if (hasAuthTimeoutBox) {
    return;
  }
  hasAuthTimeoutBox = true;
  ElMessageBox.alert("登录超时，请重新登录", "登录超时", {
    cancelButtonText: "确认",
    showCancelButton: true,
    showConfirmButton: false,
    showClose: false,
    callback: () => {
      useUserStoreHook().logOut();
      const fullPath = router.currentRoute.value.fullPath;
      const returnUrl = returnUrlBlackList.includes(fullPath) ? null : encodeURIComponent(fullPath);
      const routeLocationRaw: RouteLocationRaw = { path: loginPath };
      if (returnUrl) {
        routeLocationRaw.query = { returnUrl };
      }
      router.push(routeLocationRaw);
      hasAuthTimeoutBox = false;
    }
  });
}
