const { data } = require("../data.js");

module.exports.getMode = () => {
  return Object.keys(data.fields).map(key => {
    const field = data.fields[key];
    const type = field.type;
    let newField = { label: field.desc, prop: key, type: field.type, column: field.column };
    if (type === "enum") {
      newField.valueType = field.valueType;
      const enumValue = field.value;
      newField.enums = Object.keys(enumValue).map(enumKey => ({
        label: enumValue[enumKey].desc,
        key: enumKey,
        value: enumValue[enumKey].value
      }));
    } else {
      newField.valueType = field.type;
    }
    return newField;
  });
};

module.exports.getTableFields = () => {
  const tableFieldKeys = Object.keys(data.fields).filter(key => data.fields[key].column);
  if (tableFieldKeys.length === 0) {
    return [];
  }
  const fields = tableFieldKeys.map(key => {
    const field = data.fields[key];
    let _field = { label: field.desc, prop: key };
    if (field.minWidth) {
      _field.minWidth = field.minWidth;
    }
    if (field.width) {
      _field.width = field.width;
    }
    return _field;
  });
  return fields;
};

module.exports.getFormFields = () => {
  if (!data.needCreateOrEdit) {
    return [];
  }
  const formFieldKeys = Object.keys(data.fields).filter(key => data.fields[key].form);
  let fields = formFieldKeys.map(key => {
    const field = data.fields[key];
    let formField = {
      label: field.desc,
      prop: key,
      valueType: field.valueType || field.type,
      placeholder: field.formFieldConfig?.placeholder || `请输入${field.desc}`
    };

    if (field.formFieldConfig) {
      formField = { ...formField, formFieldConfig: { ...field.formFieldConfig } };
    }
    return formField;
  });
  // console.log('getFormFields', fields)
  return fields;
};

module.exports.getFormNumberOfSubarrays = (formFields, numberOfSubarrays = data.formLineNumber) => {
  if (!Array.isArray(formFields) || formFields.length === 0) {
    return [];
  }

  function chunkArrayIntoSubarrays(array, numberOfSubarrays) {
    const chunkSize = Math.ceil(array.length / numberOfSubarrays);
    return Array.from({ length: numberOfSubarrays }, (_, index) => {
      return array.slice(index * chunkSize, (index + 1) * chunkSize);
    });
  }

  fields = chunkArrayIntoSubarrays(formFields, numberOfSubarrays);

  return fields;
};

module.exports.getSearchFields = () => {
  if (!data.needSearch) {
    return [];
  }
  const searchFieldKeys = Object.keys(data.fields).filter(key => data.fields[key].search);
  const fields = searchFieldKeys.map(key => {
    const field = data.fields[key];
    let searchField = {
      label: field.desc,
      prop: key
    };

    if (field.searchFieldConfig && Object.keys(field.searchFieldConfig).length) {
      searchField.placeholder = field.searchFieldConfig.placeholder;
      searchField.type = field.searchFieldConfig.type;
    } else {
      searchField.placeholder = `请输入${searchField.label}`;
      searchField.type = field.type;
    }

    return searchField;
  });
  return fields;
};
module.exports.getSpan = () => {
  return 24 / data.formLineNumber;
};
