<template>
  <div class="bg-bg_color px-5 pt-0 flex gap-3 w-full">
    <el-popover
      class="box-item"
      content="Bottom Right prompts info"
      placement="bottom-end"
      width="370"
      @show="handleGetQuotationInquiryParseEfficiency()"
    >
      <template #reference>
        <div class="statistics-item">
          <img class="w-6 h-6" :src="SandClockIcon" />
          <div class="total">
            {{
              (state.quotationInquiryParseEfficiency?.parsingInquiryCount || 0) +
              (state.quotationInquiryParseEfficiency?.urgentCount || 0)
            }}
          </div>
        </div>
      </template>

      <div class="quotation-statistics">
        <div class="header flex items-center justify-between">
          <div>
            <span class="label">AI正在处理</span>
            <span class="total">{{ state.quotationInquiryParseEfficiency?.parsingInquiryCount }}</span>
            <span class="label">个询价单</span>
          </div>
          <div v-if="state.quotationInquiryParseEfficiency?.urgentCount">
            <span class="label">其中</span>
            <span class="urgent-total">{{ state.quotationInquiryParseEfficiency?.urgentCount }}</span>
            <span class="label">个加急</span>
          </div>
        </div>
        <div class="inquiry-analysis">
          <div class="inquiry-analysis-item">
            <span class="label">询价单-平均解析时间</span>
            <div class="time-consum">
              <span class="time">{{ state.quotationInquiryParseEfficiency?.avgInquiryParseTime }}</span>
              <span class="second">秒</span>
            </div>
          </div>
          <div class="inquiry-analysis-item">
            <span class="label">行-平均解析时间</span>
            <div class="time-consum">
              <span class="time">{{ state.quotationInquiryParseEfficiency?.avgInquiryItemParseTime }}</span>
              <span class="second">秒</span>
            </div>
          </div>
        </div>
      </div>
    </el-popover>

    <div class="upload_progress flex-1" :class="{ 'upload-file-class': uploadFileClass }">
      <template v-if="existquotationInquiryProgress">
        <div class="flex w-full px-5 items-center">
          <UploadFile
            :fileNames="props.quotationInquiryProgress?.inquiryAttachmentList?.map(x => x.inquiryAttachmentName)"
            :files="props.quotationInquiryProgress?.inquiryAttachmentList"
          />
          <div class="mr-[30px] flex-1">
            <div class="progress flex items-center justify-between mr-14">
              <div class="progress_statistics">
                <span>总条数：</span>
                <span>{{ props.quotationInquiryProgress.totalInquiryParseCount || 0 }}</span>
              </div>
              <div class="progress_statistics">
                <span>处理进度：</span>
                <span
                  >{{ props.quotationInquiryProgress.parsedInquiryCount || 0 }}/{{
                    props.quotationInquiryProgress.totalInquiryParseCount || 0
                  }}</span
                >
              </div>
              <div class="progress_statistics">
                <span>开始解析时间：</span>
                <span>{{ props.quotationInquiryProgress.parseStartTime }}</span>
              </div>
              <div class="progress_statistics">
                <span>token用量：</span>
                <span>{{ formatThousands(props.quotationInquiryProgress?.parseTokenUsage) }}</span>
              </div>
              <div class="progress_statistics">
                <span>已耗时：</span>
                <span>{{ props.quotationInquiryProgress.parseTimeCost || 0 }}s</span>
              </div>
            </div>
            <el-progress
              class="w-full"
              :format="percentageFormat"
              :percentage="props.quotationInquiryProgress.parseSchedule || 0"
            />
          </div>

          <template v-if="parseStatusName">
            <template v-if="props?.quotationInquiryProgress?.parseStatus === TaskParseStatusEnum.PARSING_STOP">
              <template v-if="props?.quotationInquiryProgress?.terminationReason">
                <el-tooltip
                  v-if="parseStatusName"
                  effect="dark"
                  :content="props?.quotationInquiryProgress?.terminationReason"
                  placement="top"
                >
                  <div class="status">{{ parseStatusName }}</div>
                </el-tooltip>
              </template>
              <template v-else>
                <div class="status">{{ parseStatusName }}</div>
              </template>
            </template>
            <template v-else>
              <div class="status">{{ parseStatusName }}</div>
            </template>
          </template>

          <div class="bts">
            <template v-if="parseParsed || parseStop">
              <div class="btn" @click="onQuotationInquiryReParse()">
                <span class="name">重新解析</span>
                <img class="btn-icon" :src="AgainIcon" />
              </div>
            </template>
            <template v-else>
              <div class="btn" @click="onQuotationInquiryStopParse()">
                <span class="name">停止解析</span>
                <img class="btn-icon" :src="StopIcon" />
              </div>
            </template>

            <!-- <div class="btn" :class="{ 'not-allowed': !parsePARSED }" @click="onQuotationInquiryContinueParse()">
            <span class="name">继续解析</span>
            <img class="btn-icon" :src="StartIcon" />
          </div> -->
          </div>
        </div>
      </template>
      <template v-else>
        <template v-if="fileNames?.length">
          <template v-if="uploadFileComplete">
            <div class="upload-complete flex items-center">
              <UploadFile :fileNames="fileNames" />
              <el-icon color="#67c23a"><CircleCheck /></el-icon>
              <span class="complete">已上传</span>
              <div class="start" @click="onStartParsing()">
                <span>开始解析</span>
                <div class="start_icon">
                  <el-icon color="#ffffff"><CaretRight /></el-icon>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="uploading flex items-center">
              <UploadFile :fileNames="fileNames" />
              <div class="w-[300px]">
                <span class="uploading-tip">正在上传...</span>
                <el-progress class="flex-1" :percentage="uploadPercentage" />
              </div>
            </div>
          </template>
        </template>
        <template v-else>
          <QuotationFile @onUploadFile="handleUploadFile($event)" />
        </template>
      </template>
    </div>
    <ElButton
      :disabled="!parseParsed"
      class="!h-[74px]"
      :icon="Download"
      type="primary"
      v-auth="PermissionKey.quotation.quotationManagementExport"
      @click="onQuotationInquiryExport()"
      >导出报价单</ElButton
    >
  </div>
</template>

<script setup lang="ts">
import { CaretRight, CircleCheck, Download } from "@element-plus/icons-vue";
import AgainIcon from "@/assets/img/again.png";
import {
  getQuotationInquiryParseEfficiency,
  quotationInquiryExport,
  quotationInquiryReParse,
  quotationInquiryStopParse
} from "@/api/quotation/quotation-inquiry";
import SandClockIcon from "@/assets/img/sand-clock.png";
import StopIcon from "@/assets/img/stop.png";
import { TaskParseStatusEnum, TaskParseStatusEnumMapDesc } from "@/enums";
import {
  IQuotationInquiryExport,
  IQuotationInquiryParseEfficiency,
  IQuotationInquiryProgress
} from "@/models/quotation";
import { useConfirm } from "@/utils/useConfirm";
import { ElMessage, UploadRawFile } from "element-plus";
import { computed, onMounted, reactive } from "vue";
import QuotationFile from "./quotation-file.vue";
import { useIntervalFn } from "@vueuse/core";
import { PermissionKey } from "@/consts/permission-key";
import UploadFile from "./upload-file.vue";
import { formatThousands } from "@/utils/format";

const emits = defineEmits<{
  (event: "onStartParsing"): void;
  (event: "onStopParsing"): void;
  (event: "onReParseing"): void;
  (e: "onUploadFile", file: UploadRawFile): void;
}>();

const { resume: startQuotationInquiryParseEfficiency } = useIntervalFn(handleGetQuotationInquiryParseEfficiency, 5000, {
  immediate: false
});

const state = reactive<{
  quotationInquiryParseEfficiency?: IQuotationInquiryParseEfficiency;
}>({
  quotationInquiryParseEfficiency: {}
});

const props = withDefaults(
  defineProps<{
    quotationInquiryExport?: IQuotationInquiryExport;
    taskId?: string;
    quotationInquiryProgress?: IQuotationInquiryProgress;
    fileName?: string;
    uploadPercentage?: number;
    fileNames?: Array<string>;
  }>(),
  {}
);

const parseStatusName = computed(() => {
  const { parseStatus } = props?.quotationInquiryProgress || {};
  const name = TaskParseStatusEnumMapDesc[parseStatus];
  if ([TaskParseStatusEnum.PARSING].includes(parseStatus)) {
    return `${name}…`;
  }
  return name;
});

const parseParsed = computed(() => {
  return props?.quotationInquiryProgress?.parseStatus === TaskParseStatusEnum.PARSED;
});

const parseStop = computed(() => {
  return props?.quotationInquiryProgress?.parseStatus === TaskParseStatusEnum.PARSING_STOP;
});

const uploadFileClass = computed(() => {
  return !props.quotationInquiryProgress && !props.fileName;
});

const uploadFileComplete = computed(() => {
  return props.uploadPercentage === 100;
});

const existquotationInquiryProgress = computed(() => {
  return props.quotationInquiryProgress && Object.keys(props.quotationInquiryProgress).length > 0;
});

onMounted(() => {
  handleGetQuotationInquiryParseEfficiency();
  startQuotationInquiryParseEfficiency();
});
const percentageFormat = percentage => (percentage >= 100 ? "100%" : `${percentage}%`);

// const onQuotationInquiryUrgent = async () => {
//   if (!props.taskId) {
//     return;
//   }
//   if (!(await useConfirm("是否确认一键加急", "一键加急"))) {
//     return;
//   }
//   await quotationInquiryUrgent(props.taskId);
//   ElMessage.success("一键加急成功");
// };

const onQuotationInquiryReParse = async () => {
  if (!props.taskId) {
    return;
  }

  if (!(await useConfirm("是否确认重新解析", "重新解析"))) {
    return;
  }
  await quotationInquiryReParse(props.taskId);
  ElMessage.success("重新解析中");
  emits("onReParseing");
};

const onQuotationInquiryStopParse = async () => {
  if (!props.taskId) {
    return;
  }

  if (!(await useConfirm("是否确认停止解析?", "停止解析"))) {
    return;
  }
  await quotationInquiryStopParse(props.taskId);
  ElMessage.success("停止解析成功");
  emits("onStopParsing");
};

// const onQuotationInquiryContinueParse = async () => {
//   if (!props.taskId) {
//     return;
//   }
//   if (!(await useConfirm("是否继续解析", "继续解析"))) {
//     return;
//   }
//   await quotationInquiryContinueParse(props.taskId);
//   ElMessage.success("继续解析成功");
// };

const onQuotationInquiryExport = async () => {
  // if (!props.quotationInquiryExport?.inquiryCompanyId) {
  //   ElMessage.warning("请选择询价单位");
  //   return;
  // }

  if (!(await useConfirm("是否导出报价单", "导出报价单"))) {
    return;
  }

  await quotationInquiryExport({ ...props.quotationInquiryExport, taskId: props.taskId });
  ElMessage.success("报价单导出中");
};

async function handleGetQuotationInquiryParseEfficiency() {
  const { data } = await getQuotationInquiryParseEfficiency();
  state.quotationInquiryParseEfficiency = data;
}

const onStartParsing = () => {
  emits("onStartParsing");
};

const handleUploadFile = (file: UploadRawFile) => {
  emits("onUploadFile", file);
};
</script>

<style scoped lang="scss">
.upload-file-class {
  border: 1px solid;
}

.statistics-item {
  padding: 8px 12px;
  border-radius: 4px;
  background: linear-gradient(180deg, #ebf3fc 0%, rgba(235, 243, 252, 0.4) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;

  .label {
    font-size: 13px;
    line-height: 22px;
    letter-spacing: 0px;
    color: #606266;
    margin-left: 4px;
  }

  .total {
    margin-top: 4px;
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 0;
    color: #303133;
  }
}

.upload_progress {
  border: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  border-radius: 4px;
  flex: 1;
  position: relative;

  &.upload-file-class {
    border: 1px dashed #dcdfe6;

    &:hover {
      border: 1px dashed var(--el-color-primary);
    }
  }

  .file_name {
    font-size: 18px;
    font-weight: 500;
    color: #303339;
    margin: 0 22px 0 13px;
    max-width: 35%;
  }
  // .stop {
  //   position: absolute;
  //   right: 20px;
  // }
  .progress_statistics {
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    color: #909399;
  }
}

.status {
  color: var(--el-color-primary);
  margin-right: 30px;
}

.bts {
  display: flex;
  align-items: center;
  gap: 10px;

  .btn {
    border-radius: 4px;
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    display: flex;
    align-items: center;
    cursor: pointer;

    &.not-allowed {
      cursor: not-allowed;
    }

    .name {
      font-size: 12px;
      font-weight: 500;
      letter-spacing: 0;
      color: #606266;
      margin-right: 4px;
    }

    .btn-icon {
      width: 16px;
      height: 16px;
    }
  }
}

.quotation-statistics {
  padding: 0 10px;

  .header {
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0;
    padding: 16px 0;
    border-bottom: 1px solid #e4e7ed;

    .label {
      color: #606266;
    }

    .total {
      color: var(--el-color-primary);
      margin: 0 4px;
      font-weight: 600;
    }

    .urgent-total {
      font-weight: 600;
      color: #f56c6c;
      margin: 0 4px;
    }
  }

  .inquiry-analysis {
    padding-top: 24px;
    padding-bottom: 16px;
    display: flex;
    flex-direction: column;
    gap: 24px;

    .inquiry-analysis-item {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .label {
        font-size: 16px;
        line-height: 100%;
        letter-spacing: 0;
        color: #606266;
      }

      .time-consum {
        display: flex;
        align-items: flex-end;

        .time {
          font-size: 20px;
          font-weight: 600;
          line-height: 100%;
          letter-spacing: 0;
          color: #303133;
          margin-right: 4px;
        }

        .second {
          font-size: 16px;
          font-weight: normal;
          line-height: 100%;
          letter-spacing: 0;
          color: #606266;
        }
      }
    }
  }
}

.upload-complete {
  padding: 0 20px;
  width: 100%;

  .complete {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0;
    color: #67c23a;
    margin: 0 22px 0 5px;
  }

  .start {
    display: flex;
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0;
    color: #606266;
    border: 1px solid #dcdfe6;
    padding: 6px 12px;
    border-radius: 4px;
    align-items: center;
    cursor: pointer;

    .start_icon {
      margin-left: 4px;
      width: 16px;
      height: 16px;
      border-radius: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--el-color-primary);
    }
  }
}

.uploading {
  .uploading-tip {
    font-size: 13px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0;
    color: #a8abb2;
    margin-bottom: 7px;
  }
}
</style>
