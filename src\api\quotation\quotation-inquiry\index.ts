import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IFile,
  IPasteContentInquiry,
  IQuotationInquiry,
  IQuotationInquiryCurrentParsing,
  IQuotationInquiryDetail,
  IQuotationInquiryExport,
  IQuotationInquiryParse,
  IQuotationInquiryParseEfficiency,
  IQuotationInquiryProgress,
  IQuotationInquirySetDiscount,
  IReQuote,
  IResponse
} from "@/models";

/** 单条解析明细编辑-折扣  */
export const quotationInquiryEditItemDicount = (data: IQuotationInquiryDetail) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/editItemDiscount/${data.id}`);
  return http.put<IQuotationInquiryDetail, IResponse<boolean>>(url, { data });
};

/** 单条解析明细编辑  */
export const quotationInquiryEditItem = (data: IQuotationInquiryDetail) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/editItem/${data.id}`);
  return http.put<IQuotationInquiryDetail, IResponse<boolean>>(url, { data });
};

/** 批量更新-折扣  */
export const quotationInquiryBatchEdit = (data: Array<IQuotationInquiryDetail>) => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/editItemDiscount");
  return http.put<Array<IQuotationInquiryDetail>, IResponse<boolean>>(url, { data });
};

/** 一键加急 */
export const quotationInquiryUrgent = (taskId: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/urgent/${taskId}`);
  return http.post<void, IResponse<boolean>>(url);
};
/** 重新解析 */
export const quotationInquiryReParse = (taskId: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/reParse/${taskId}`);
  return http.post<void, IResponse<boolean>>(url);
};

/** 停止解析 */
export const quotationInquiryStopParse = (taskId: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/stopParse/${taskId}`);
  return http.post<void, IResponse<boolean>>(url);
};

/** 解析询价单 */
export const quotationInquiryParseInquiry = (data: IQuotationInquiryParse) => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/parseInquiry");
  return http.post<IQuotationInquiryParse, IResponse<string>>(url, { data });
};

/** 报价单导出 */
export const quotationInquiryExport = (data: IQuotationInquiryExport) => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/export");
  return http.post<IQuotationInquiryExport, IResponse<IFile>>(url, { data });
};

/** 继续解析 */
export const quotationInquiryContinueParse = (taskId: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/continueParse/${taskId}`);
  return http.post<void, IResponse<boolean>>(url);
};

/** 批量新增 */
export const quotationInquiryBatchInsert = (data: Array<IQuotationInquiry>) => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/batchInsert");
  return http.post<Array<IQuotationInquiry>, IResponse<boolean>>(url, { data });
};

/** 询价单解析详情 */
export const getQuotationInquiryProgress = (taskId: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/getParseProgress/${taskId}`);
  return http.get<void, IResponse<IQuotationInquiryProgress>>(url);
};

/** 查询待解析数据及解析效率 */
export const getQuotationInquiryParseEfficiency = () => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/getParseEfficiency");
  return http.get<void, IResponse<IQuotationInquiryParseEfficiency>>(url);
};

/** 解析明细查询 */
export const getQuotationInquiryParseDetailList = (taskId: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/getParseDetail/${taskId}`);
  return http.get<void, IResponse<Array<IQuotationInquiryDetail>>>(url);
};

/** 解析明细查询-解析成功 */
export const getQuotationInquiryCompletedParseList = (taskId: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/getCompletedParseDetail/${taskId}`);
  return http.get<void, IResponse<Array<IQuotationInquiryDetail>>>(url);
};

/** 查询当前用户正在解析的询价单 */
export const getQuotationInquiryCurrentParsingInquiry = () => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/getCurrentParsingInquiry");
  return http.get<void, IResponse<IQuotationInquiryCurrentParsing>>(url);
};

/** 批量设置折扣 */
export const quotationInquiryBatchSetDiscount = (data: IQuotationInquirySetDiscount) => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/batchSetDiscount");
  return http.post<IQuotationInquirySetDiscount, IResponse<boolean>>(url, { data });
};

export const getQuotationInquiryDetail = (taskId: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/getEnrichedParseProgress/${taskId}`);
  return http.get<void, IResponse<IQuotationInquiryProgress>>(url);
};

/** 删除报价单行 */
export const deleteTaskItemById = (taskId: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/deleteTaskItemById/${taskId}`);
  return http.post<void, IResponse<boolean>>(url);
};

/**  批量删除报价单行 */
export const batchDeleteTaskItemByIds = (taskItemIds: Array<string>) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/batchDeleteTaskItems`);
  return http.post<{ taskItemIds: Array<string> }, IResponse<boolean>>(url, { data: { taskItemIds } });
};

/*** 根据id 查询解析 单条详情 */
export const getParseDetailByTaskItemId = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationInquiry/getParseDetailByTaskItemId/${id}`);
  return http.get<void, IResponse<IQuotationInquiryDetail>>(url);
};

/*** 添加单条解析详情 */
export const addQuotationInquiryItem = (data: IQuotationInquiryDetail) => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/addItem");
  return http.post<IQuotationInquiryDetail, IResponse<IQuotationInquiryDetail>>(url, { data });
};

/*** 粘贴内容解析 */
export const pasteContentInquiry = (data: IPasteContentInquiry) => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/pasteContentInquiry");
  return http.post<IPasteContentInquiry, IResponse<string>>(url, { data });
};

/** 重新报价根据版本 */
export const reQuoteByVersion = (params: IReQuote) => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/reQuoteByVersion");
  return http.post<IReQuote, IResponse<string>>(url, { params });
};
