import { dayjs } from "element-plus";
import { reactive } from "vue";
import { dateFormat } from "@/consts/date-format";
import { EControlType } from "@/enums/dynamic-form.enum";
import { IDynamicFormItem } from "@/models/dynamic-form/i-dynamic-form";

export function useTableForm() {
  /**
   * 设置控件的唯一Id值
   */
  const setControlIdByData = (rowData: IDynamicFormItem) => {
    return `${rowData.id}Value`;
  };

  /**
   * 格式化传入的数据
   */
  const formatDynamicFormData = (
    collectionData: IDynamicFormItem[],
    tableControlKeyWords: string,
    formControlKeyWords: string
  ) => {
    const formValue = {};
    (collectionData || []).forEach(item => {
      const controlKey = item?.dataTypeIdentityDetail?.identityCode;
      // 设置唯一控件Id
      if (controlKey) {
        const controlValueId = setControlIdByData(item);
        switch (controlKey) {
          case EControlType.TextControl:
            item[tableControlKeyWords] = controlKey;
            item[formControlKeyWords] = controlValueId;
            formValue[controlValueId] = "";
            break;
          case EControlType.NumberTextControl:
            item[tableControlKeyWords] = controlKey;
            item[formControlKeyWords] = controlValueId;
            formValue[controlValueId] = null;
            break;
          case EControlType.SelectControl:
            item[tableControlKeyWords] = controlKey;
            item[formControlKeyWords] = controlValueId;
            formValue[controlValueId] = "";
            break;
          case EControlType.DateControl:
            item[tableControlKeyWords] = controlKey;
            item[formControlKeyWords] = controlValueId;
            formValue[controlValueId] = dayjs(new Date()).format(dateFormat);
            break;
          case EControlType.RadioControl:
            item[tableControlKeyWords] = controlKey;
            item[formControlKeyWords] = controlValueId;
            formValue[controlValueId] = null;
            break;
          case EControlType.WaveRoseControl:
            item[tableControlKeyWords] = controlKey;
            item[formControlKeyWords] = controlValueId;
            formValue[controlValueId] = null;
            break;
          default:
            break;
        }
      }
    });
    return {
      formValue,
      collectionData
    };
  };

  /**
   * 根据传入的表格数据设置表单默认值
   */
  const patchFormByData = (
    dynamicFormData: IDynamicFormItem[],
    tableFormData: IDynamicFormItem,
    tableControlKeyWords: string,
    formControlKeyWords: string,
    editMode: Boolean
  ) => {
    (dynamicFormData || []).forEach(item => {
      const controlKey = item[tableControlKeyWords];
      // 设置唯一控件Id
      const controlValueId = item[formControlKeyWords];
      switch (controlKey) {
        case EControlType.TextControl:
          tableFormData[controlValueId] = item.targetValue;
          break;
        case EControlType.NumberTextControl:
          tableFormData[controlValueId] =
            item.targetValue === null || item.targetValue === undefined || !item.targetValue?.length
              ? null
              : editMode
              ? item.targetValue
              : item.targetValue;
          break;
        case EControlType.SelectControl:
          tableFormData[controlValueId] = editMode ? item.targetValue : item.targetValueLabel;
          break;
        case EControlType.DateControl:
          tableFormData[controlValueId] = item.targetValue ? item.targetValue : dayjs(new Date()).format(dateFormat);
          break;
        case EControlType.RadioControl:
          tableFormData[controlValueId] = editMode ? item.targetValue : item.targetValueLabel;
          break;
        case EControlType.WaveRoseControl:
          tableFormData[controlValueId] = editMode ? item.targetValue : item.targetValueLabel;
          break;
        default:
          break;
      }
    });
  };

  /**
   * 抽取不同控件的值
   */
  const getControlValueHook = (
    rowData: IDynamicFormItem,
    tableFormData: IDynamicFormItem,
    formControlKeyWords: string
  ) => {
    // 唯一控件Id
    const controlValueId = rowData[formControlKeyWords];
    return tableFormData[controlValueId];
  };

  /**
   * 获取下拉框的 options list值
   */
  const selectOptions = reactive({
    optionList: []
  });
  const getSelectOptions = (rowTypeData: IDynamicFormItem) => {
    const optionList = (rowTypeData?.dataTypeIdentityDetail?.identityDetailList || []).map(item => {
      const { identityId, identityValue, identityLabel } = item;
      return {
        key: identityId,
        label: identityLabel,
        value: identityValue
      };
    });
    selectOptions.optionList = optionList || [];
  };

  /**
   * 获取下拉框的 options list值
   */
  const radioOptions = reactive({
    optionList: []
  });
  const getRadioOptions = (rowTypeData: IDynamicFormItem) => {
    const optionList = (rowTypeData?.dataTypeIdentityDetail?.identityDetailList || []).map(item => {
      const { identityId, identityValue, identityLabel } = item;
      return {
        key: identityId,
        label: identityLabel,
        value: identityValue
      };
    });
    radioOptions.optionList = optionList || [];
  };

  return {
    selectOptions,
    radioOptions,
    formatDynamicFormData,
    getControlValueHook,
    getSelectOptions,
    getRadioOptions,
    patchFormByData
  };
}
