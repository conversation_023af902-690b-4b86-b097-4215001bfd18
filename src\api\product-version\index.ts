import { IListResponse, IProductVersiontReq, IProductVersion, IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

/** 获取版本列表 */
export const queryProductVersion = (params?: IProductVersiontReq) => {
  return http.get<IProductVersiontReq, IListResponse<IProductVersion>>(
    withApiGateway(`admin-api/business/productVersion`),
    { params }
  );
};

/** 获取版本列表 -全部 */
export const queryAllProductVersion = (params?: IProductVersiontReq) => {
  return http.get<IProductVersiontReq, IResponse<Array<IProductVersion>>>(
    withApiGateway(`admin-api/business/productVersion/getVersionList`),
    { params }
  );
};

/** 获取最新版本 */
export const getLatestVersion = () => {
  return http.get<void, IResponse<IProductVersion>>(
    withApiGateway(`admin-api/business/productVersion/getLatestVersion`)
  );
};
