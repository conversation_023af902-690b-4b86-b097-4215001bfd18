import { IResponse, ISync } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

export const putSyncConfig = (data: ISync) => {
  return http.put<ISync, IResponse<boolean>>(withApiGateway("admin-api/system/tenant/update-auto-sync"), { data });
};

export const querySyncConfig = () => {
  return http.get<void, IResponse<ISync>>(withApiGateway("admin-api/system/tenant/get-auto-sync"));
};
