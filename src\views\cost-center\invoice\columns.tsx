import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "订单编号",
      prop: "orderNo"
    },
    {
      label: "状态",
      prop: "status"
    },
    {
      label: "类型",
      prop: "type"
    },
    {
      label: "充值金额",
      prop: "rechargeAmount"
    },
    {
      label: "充值token",
      prop: "rechargeToken"
    },
    {
      label: "实际到账token",
      prop: "actualToken"
    },
    {
      label: "充值时间",
      prop: "rechargeTime"
    },
    {
      label: "提交人",
      prop: "submitter"
    },
    {
      label: "备注",
      prop: "remark"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char11
    }
  ];
  return { columns };
}
