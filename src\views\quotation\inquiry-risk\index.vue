<template>
  <div class="overflow-auto m-auto mt-6 mb-6 flex flex-col">
    <div class="bg-bg_color pt-6 pb-6">
      <el-form label-position="left" :model="form">
        <div>
          <TitleBar title="询价风险" />
          <div class="flex flex-col gap-5 form">
            <div class="item">
              <div class="flex justify-between items-center pb-4">
                <div class="label">匹配周期</div>
                <el-form-item class="!mb-0 flex justify-between" prop="matchPeriod">
                  <el-input v-model="form.matchPeriod" class="!w-40" placeholder="请输入" :disabled="!isEdit">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </div>
              <div class="text-gray-500 text-sm mb-4 tip">进行匹配周期内参与解析的询价相似度</div>
            </div>

            <div class="item">
              <div class="flex justify-between items-center pb-4">
                <div class="label">相似度阈值</div>
                <el-form-item class="!mb-0 flex justify-between" prop="similarityThreshold">
                  <el-input v-model="form.similarityThreshold" class="!w-40" placeholder="请输入" :disabled="!isEdit">
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </div>
              <div class="text-gray-500 text-sm mb-4 tip">
                匹配周期内询价单规格型号的相似度，1%~99%，超过阈值的进行提醒
              </div>
            </div>
          </div>
        </div>
        <div class="text-right">
          <el-button
            v-auth="PermissionKey.quotation.quotationRiskConfigEdit"
            v-if="!isEdit"
            type="primary"
            size="large"
            @click="onToggleEditMode(true)"
            >编辑</el-button
          >
          <template v-else>
            <el-button size="large" @click="onToggleEditMode(false)">取消</el-button>
            <el-button size="large" type="primary" @click="onSaveAsyncConfig()">保存</el-button>
          </template>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { usePageStoreHook } from "@/store/modules/page";
import TitleBar from "@/components/TitleBar";
import { onMounted, reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { PermissionKey } from "@/consts";
import { IInquiryRiskConfig } from "@/models";
import { getInquiryRisk, updateInquiryRiskConfig } from "@/api/quotation/inquiry-risk";

usePageStoreHook().setTitle(useRoute().meta?.title as string);
const isEdit = ref(false);

const form = reactive<IInquiryRiskConfig>({});

onMounted(async () => {
  const { data } = await getInquiryRisk();
  form.matchPeriod = data.matchPeriod;
  form.similarityThreshold = data.similarityThreshold;
});

const onToggleEditMode = (editMode: boolean) => {
  isEdit.value = editMode;
};
const onSaveAsyncConfig = async () => {
  await updateInquiryRiskConfig(form);
  ElMessage.success("修改成功");
  isEdit.value = false;
};
</script>

<style scoped lang="scss">
.form {
  padding: 60px 140px;
}

.item {
  padding: 20px 40px;
  background: #fafafa;
  box-sizing: border-box;
  border: 1px solid #ebeef5;
  padding-bottom: 16px;

  border-radius: 4px;

  .label {
    font-size: 18px;
    font-weight: bold;
    line-height: 26px;
    letter-spacing: 0;
    color: #303133;
  }
}

.tip {
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.note {
  color: var(--el-text-color-secondary);
}
</style>
