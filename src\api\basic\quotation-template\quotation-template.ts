import { http } from "@/utils/http";
import { withApiGateway } from "../../util";
import { IListResponse, IResponse, IQuotationTemplate, IQuotationTemplateForm, IQuotationTemplateReq } from "@/models";

/** 查询报价单模板分页  */
export const queryQuotationTemplate = (data?: IQuotationTemplateReq) => {
  const url: string = withApiGateway("admin-api/business/quotationTemplate/getPageList");
  return http.post<IQuotationTemplateReq, IListResponse<IQuotationTemplate>>(url, {
    data
  });
};

/** 根据报价单模板id 查询详情 */
export const getQuotationTemplateById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/quotationTemplate/${id}`);
  return http.get<string, IResponse<IQuotationTemplate>>(url);
};

/** 新增报价单模板 */
export const createQuotationTemplate = (data: IQuotationTemplateForm) => {
  return http.post<IQuotationTemplateForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/quotationTemplate"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑报价单模板 */
export const updateQuotationTemplate = (data: IQuotationTemplateForm) => {
  return http.put<IQuotationTemplateForm, IResponse<boolean>>(
    withApiGateway(`admin-api/business/quotationTemplate/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除报价单模板根据Id */
export const deleteQuotationTemplateById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/business/quotationTemplate/${id}`));
};
