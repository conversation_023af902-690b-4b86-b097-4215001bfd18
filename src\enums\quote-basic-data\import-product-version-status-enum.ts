export enum ImportProductVersionStatusEnum {
  /** 处理中 */
  processing = 1,

  /** 已完成 */
  completion = 2
}

/**
 * 材料类型枚举描述映射
 */
export const ImportProductVersionStatusEnumMapDesc: Record<ImportProductVersionStatusEnum, string> = {
  [ImportProductVersionStatusEnum.processing]: "导入处理中",
  [ImportProductVersionStatusEnum.completion]: "已完成"
};

/**
 * 材料类型枚举颜色映射
 */
export const ImportProductVersionStatusEnumMapColor: Record<ImportProductVersionStatusEnum, string> = {
  [ImportProductVersionStatusEnum.completion]: "primary", // 主色
  [ImportProductVersionStatusEnum.processing]: "warning" // 次要色
};
