import { TaskParseStatusEnum, TaskStatusEnum } from "@/enums";

/** 解析进度详情 */
export interface IQuotationInquiryProgress {
  /**
   * 联系人
   */
  contactPerson?: string;
  /**
   * 铜价
   */
  copperPrice?: number;
  /**
   * ID
   */
  id?: number;
  /**
   * 询价单附件id
   */
  inquiryAttachmentId?: Array<string>;
  /**
   * 询价单附件名
   */
  inquiryAttachmentName?: string;
  /**
   * 询价单附件url
   */
  inquiryAttachmentUrl?: string;

  inquiryAttachmentList?: Array<InquiryAttachment>;

  /**
   * 询价单位id
   */
  inquiryOrgId?: number;

  /** 询价单位 */
  inquiryOrgName?: string;
  /**
   * 询价时间
   */
  inquiryTime?: Date;
  /**
   * 操作人id
   */
  operatorId?: string;

  /** 销售人员 */
  operatorName?: string;
  /**
   * 解析完成时间
   */
  parseFinishTime?: Date;
  /**
   * 任务 解析状态
   */
  parseStatus?: TaskParseStatusEnum;
  /**
   * 产品单价版本id
   */
  versionId?: string;
  /**
   * 报价金额
   */
  quotationAmount?: number;
  /**
   * 报价单附件id
   */
  quotationAttachmentId?: string;

  /*** 报价单附件名 */
  quotationAttachmentName?: string;

  /** 报价单地址 */
  quotationAttachmentUrl?: string;

  /**
   * 报价批次号
   */
  quotationBatchNo?: string;
  /**
   * 参考
   */
  reference?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 主题
   */
  subject?: string;
  /**
   * 任务状态
   */
  taskStatus?: TaskStatusEnum;

  /** 解析总数 */
  totalInquiryParseCount?: number;

  /** 已解析数量 */
  parsedInquiryCount?: number;

  /** 耗时 */
  parseTimeCost?: number;

  /** 解析开始时间 */
  parseStartTime?: string;

  /** 解析进度 */
  parseSchedule?: number;

  productPriceVersionNumber?: string;

  /** 产品版本id */
  productPriceVersionId?: string;

  /** token用量 */
  parseTokenUsage?: string;

  /** 终止原因 */
  terminationReason?: string;
}

export interface InquiryAttachment {
  inquiryAttachmentId: string;
  inquiryAttachmentName: string;
  inquiryAttachmentUrl: string;
}
