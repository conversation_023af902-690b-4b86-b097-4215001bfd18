<template>
  <div class="flex flex-col h-full">
    <el-input class="mb-4" placeholder="产品分类" :suffix-icon="Search" v-model="state.keyword" />
    <el-scrollbar class="w-full flex-1">
      <div class="flex flex-col gap-2">
        <div
          class="text-base cursor-pointer category-item"
          :class="{ 'activate-dict': state.selectCategory?.id === item.id }"
          v-for="item in state.categorys"
          :key="item.id"
          @click="onSelectCategory(item)"
        >
          {{ item.name }}
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { queryAllProductCategory } from "@/api/product-category";
import { Search } from "@element-plus/icons-vue";
import { IProductCategory } from "@/models";
import { onMounted, reactive, watch } from "vue";
import { cloneDeep } from "@pureadmin/utils";

defineExpose({ emptySelectCategory, handleQueryAllCategory });

const emits = defineEmits<{
  (e: "onSelectCategory", value: IProductCategory): void;
}>();

const props = withDefaults(
  defineProps<{
    defaultSelected: boolean;
  }>(),
  {
    defaultSelected: false
  }
);

let allProductCategory: Array<IProductCategory> = [];
const state = reactive<{
  categorys: Array<IProductCategory>;
  selectCategory?: IProductCategory;
  keyword?: string;
}>({
  categorys: []
});

onMounted(() => {
  handleQueryAllCategory();
});

watch(
  () => state.keyword,
  keyword => {
    state.categorys = keyword?.trim() ? allProductCategory.filter(x => x.name.includes(keyword)) : allProductCategory;
  }
);

async function handleQueryAllCategory() {
  const { data } = await queryAllProductCategory();
  state.categorys = data;
  allProductCategory = cloneDeep(data);

  if (state.selectCategory) {
    return;
  }

  if (props.defaultSelected && Array.isArray(data) && data.length) {
    onSelectCategory(data[0]);
  }
}

const onSelectCategory = (category: IProductCategory) => {
  state.selectCategory = category;
  emits("onSelectCategory", category);
};

function emptySelectCategory() {
  state.selectCategory = undefined;
  emits("onSelectCategory", undefined);
}
</script>

<style scoped lang="scss">
.category-item {
  @apply text-base text-regular p-0.5 pl-2 flex-bc;

  &:hover {
    color: var(--el-color-primary);
    background: var(--el-fill-color-light);

    .edit-icon {
      visibility: visible;
    }
  }

  &.activate-dict {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }
}
</style>
