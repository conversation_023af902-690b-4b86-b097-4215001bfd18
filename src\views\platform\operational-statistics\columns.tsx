import { fullDateFormat } from "@/consts";
import { ColumnWidth, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "序号",
      prop: "index",
      align: "center",
      width: TableWidth.index
    },
    {
      label: "客户名称",
      prop: "tenantComName",
      align: "center",
      width: ColumnWidth.Char6
    },
    {
      label: "客户简称",
      prop: "tenantName",
      align: "center",
      width: ColumnWidth.Char6
    },
    {
      label: "租户创建时间",
      prop: "tenantCreateTime",
      align: "center",
      width: TableWidth.dateTime
    },
    {
      label: "状态",
      prop: "status",
      cellRenderer(data: TableColumnRenderer) {
        const status: boolean = data.row.status;
        return status ? <CxTag type="danger">禁用</CxTag> : <CxTag type="success">启用</CxTag>;
      },
      align: "center",
      width: TableWidth.status
    },
    {
      label: "用户数",
      prop: "userCount",
      align: "center",
      width: TableWidth.index
    },
    {
      label: "询价历史总数",
      prop: "inquiryCount",
      align: "center",
      width: ColumnWidth.Char6
    },
    {
      label: "试用开始时间",
      prop: "inquiryTimeBegin",
      align: "center",
      width: TableWidth.dateTime
    },
    {
      label: "最新报价记录时间",
      prop: "inquiryTimeLast",
      align: "center",
      width: TableWidth.dateTime
    },
    {
      label: "已用Token",
      prop: "useTokenCount",
      align: "center",
      slot: "useTokenCount",
      width: ColumnWidth.Char8
    },
    {
      label: "3日内询价记录数",
      prop: "inquiryCountThreeDay",
      align: "center",
      width: ColumnWidth.Char8
    },
    {
      label: "7日内询价记录数",
      prop: "inquiryCountSevenDay",
      align: "center",
      width: ColumnWidth.Char8
    },
    {
      label: "统计时间",
      prop: "queryTime",
      align: "center",
      formatter: dateFormatter(fullDateFormat),
      width: TableWidth.dateTime
    }
  ];
  const detailColumns: TableColumnList = [
    {
      label: "序号",
      prop: "index",
      align: "center",
      width: TableWidth.index
    },
    {
      label: "客户名称",
      prop: "tenantComName",
      align: "center"
    },
    {
      label: "客户简称",
      prop: "tenantName",
      align: "center"
    },
    {
      label: "租户创建时间",
      prop: "tenantCreateTime",
      align: "center"
    },
    {
      label: "新增数据-统计日期",
      prop: "inquiryDate",
      align: "center"
    },
    {
      label: "新增用户数",
      prop: "userCount",
      align: "center"
    },
    {
      label: "新增询价单",
      prop: "inquiryCount",
      align: "center"
    },
    {
      label: "新增token用量",
      prop: "useTokenCount",
      align: "center",
      slot: "useTokenCount"
    }
  ];
  return { columns, detailColumns };
}
