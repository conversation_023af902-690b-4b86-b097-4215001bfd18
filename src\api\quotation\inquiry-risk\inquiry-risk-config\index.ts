import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse, IInquiryRiskConfig } from "@/models";

/** 获取当前租户询价风险配置  */
export const getInquiryRisk = () => {
  const url: string = withApiGateway("admin-api/system/tenant/getInquiryRisk");
  return http.get<void, IResponse<IInquiryRiskConfig>>(url);
};

/** 更新当前租户询价风险配置  */
export const updateInquiryRiskConfig = (data: IInquiryRiskConfig) => {
  const url: string = withApiGateway("/admin-api/system/tenant/updateInquiryRisk");
  return http.put<IInquiryRiskConfig, IResponse<boolean>>(url, { data });
};
