import { StatusEnum, SexEnum, DataPermissionTypeEnum } from "@/enums";
import { IDepartment } from "../department";
import { IRole } from "../role";

export interface IEmployee {
  id: string;
  username: string;
  nickname: string;
  remark: string;
  email: string;
  mobile: string;
  sex: SexEnum;
  avatar: string;
  password: string;
  status: StatusEnum;
  createTime: Date;
  /**  在职状态 true: 在职，false 离职 */
  onJobStatus: boolean;
  deptIds: Array<string>;
  roleIds: Array<string>;
  dept: Array<IDepartment>;
  roles: Array<IRole>;
  wechatUnionId?: string;
  dataPermissionType?: DataPermissionTypeEnum;
}
