export enum VerificationStatusEnum {
  /** 核验中 */
  VERIFYING = 0,

  /** "已核验 */
  VERIFIED = 1
}

/**
 * 描述映射
 */
export const VerificationStatusEnumMapDesc: Record<VerificationStatusEnum, string> = {
  [VerificationStatusEnum.VERIFYING]: "核验中",
  [VerificationStatusEnum.VERIFIED]: "已核验"
};

/**
 * 描述映射
 */
export const VerificationStatusEnumMapColor: Record<VerificationStatusEnum, string> = {
  [VerificationStatusEnum.VERIFYING]: "warning",
  [VerificationStatusEnum.VERIFIED]: "success"
};
