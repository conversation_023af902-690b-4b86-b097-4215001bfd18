import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "类型",
      prop: "bomRaw.type",
      width: ColumnWidth.Char5
    },
    {
      label: "原材料名称",
      prop: "rawMaterialInformationName",
      slot: "rawMaterialInformationName"
    },
    {
      label: "主材/辅材",
      prop: "materialType",
      slot: "materialType",
      width: ColumnWidth.Char6
    },
    {
      label: "型号",
      prop: "bomRaw.rawModel"
    },
    {
      label: "规格",
      prop: "bomRaw.rawSpec",
      width: ColumnWidth.Char5
    },
    {
      label: "材料定额数量",
      prop: "quantity",
      slot: "quantity",
      width: ColumnWidth.Char12
    },
    {
      label: "计量单位",
      prop: "productUnit",
      slot: "productUnit",
      width: ColumnWidth.Char8
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char4
    }
  ];
  return { columns };
}
