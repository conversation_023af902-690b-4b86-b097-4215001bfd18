import { TableWidth, StatusEnum } from "@/enums";
import { IDictionaryOption, IEmployee } from "@/models";
import { formatEnum } from "@/utils/format";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "排序",
      prop: "sort"
    },
    {
      label: "物资种类",
      prop: "subClassCodeName",
      formatter: (row: IDictionaryOption) => row?.subClassNameList?.join(",")
    },
    {
      label: "编码",
      prop: "code"
    },
    {
      label: "字典值名称",
      prop: "name"
    },
    {
      label: "状态",
      prop: "status",
      formatter: (row: IEmployee) => formatEnum(row.status, StatusEnum, "StatusEnum")
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.simpleOperation
    }
  ];
  return { columns };
}
