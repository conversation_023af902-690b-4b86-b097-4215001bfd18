<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="left">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="报价批次号" prop="quotationBatchNo">
          <el-input v-model="form.quotationBatchNo" clearable placeholder="请输入报价批次号" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="销售人员" prop="salesPersonnel">
          <el-input v-model="form.salesPersonnel" clearable placeholder="请选择销售人员" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="询价时间" prop="inquiryTime">
          <el-date-picker
            class="!w-full"
            v-model="form.inquiryTime"
            type="date"
            clearable
            placeholder="请输入询价时间"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="询价单位" prop="inquiryOrganization">
          <el-input v-model="form.inquiryOrganization" clearable placeholder="请输入询价单位" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="报价单" prop="inquiryFile">
          <el-upload class="w-full" ref="uploadRef" drag :auto-upload="false" :limit="1" :on-exceed="onExceed">
            <el-icon><upload-filled /></el-icon>
            <div>拖动上传 or <em>点击上传</em></div>
            <!-- <template #tip>
                   <div class="el-upload__tip">jpg/png files with a size less than 500kb</div>
                  </template> -->
          </el-upload>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules, genFileId, UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { IQuotationHistory, IQuotationHistoryForm } from "@/models";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});

const form = reactive<IQuotationHistoryForm>({});
const formRef = ref<FormInstance>();
const uploadRef = ref<UploadInstance>();

const rules: FormRules = {
  quotationBatchNo: [{ required: true, trigger: "change", message: "报价批次号不能为空" }],
  salesPersonnel: [{ required: true, trigger: "change", message: "销售人员不能为空" }],
  inquiryTime: [{ required: true, trigger: "change", message: "询价时间不能为空" }],
  inquiryOrganization: [{ required: true, trigger: "change", message: "询价单位不能为空" }],
  inquiryFile: [{ required: true, trigger: "change", message: "报价单不能为空" }]
};

const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IQuotationHistory) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}
</script>

<style scoped></style>
