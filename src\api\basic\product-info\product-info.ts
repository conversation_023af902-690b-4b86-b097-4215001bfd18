import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IListResponse,
  IResponse,
  IProductInfo,
  IProductInfoForm,
  IProductInfoReq,
  IProductInfoImport
} from "@/models";

/** 查询产品信息分页  */
export const queryProductInfo = (data: IProductInfoReq) => {
  const url: string = withApiGateway("admin-api/business/productPrice/queryPage");
  return http.post<IProductInfoReq, IListResponse<IProductInfo>>(url, { data });
};

/** 根据产品信息id 查询详情 */
export const getProductInfoById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/productPrice/${id}`);
  return http.get<string, IResponse<IProductInfo>>(url);
};

/** 新增产品信息 */
export const createProductInfo = (data: IProductInfoForm) => {
  return http.post<IProductInfoForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/productPrice/createPrice"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑产品信息 */
export const updateProductInfo = (data: IProductInfoForm) => {
  return http.put<IProductInfoForm, IResponse<boolean>>(
    withApiGateway(`admin-api/business/productPrice/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除产品信息根据Id */
export const deleteProductInfoById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/business/productPrice/${id}`));
};

/*** 导入产品信息 */
export const productInfoImport = (data: IProductInfoImport) => {
  return http.post<IProductInfoImport, IResponse<boolean>>(withApiGateway("admin-api/business/productPrice/import"), {
    data
  });
};

/*** 导入产品信息 根据导入类型 */
export const productInfoImportByType = (data: IProductInfoImport) => {
  return http.post<IProductInfoImport, IResponse<boolean>>(
    withApiGateway("admin-api/business/productPrice/importByType"),
    {
      data
    }
  );
};
