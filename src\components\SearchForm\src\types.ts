import { Component } from "vue";
import { FormItemProps } from "element-plus";

export enum LabelPosition {
  LEFT = "left",
  RIGHT = "right",
  TOP = "top"
}

export enum SizeType {
  DEFAULT = "default",
  SMALL = "small",
  LARGE = "large"
}

export interface ISearchItem extends Partial<FormItemProps> {
  key: string;
  component: string | Component;
  componentProps?: object;
  full?: boolean;
  style?: Record<string, string>;
}

export interface IKeywordField {
  key: string;
  title: string;
}

export interface IKeywordPrompt {
  id: string;
  label: string;
  field: string;
  keyword: string;
  fieldTitle: string;
}
