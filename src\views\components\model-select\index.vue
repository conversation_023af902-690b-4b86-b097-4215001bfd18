<template>
  <el-select
    class="!w-full"
    v-model="modelValue"
    clearable
    :placeholder="placeholder"
    :filterable="filterable"
    @change="onSelectModel($event)"
  >
    <el-option v-for="item in allProductModel || []" :key="item.id" :label="item.modelName" :value="item.modelName" />
  </el-select>
</template>

<script setup lang="ts">
import { queryAllProductModelByCategoryId } from "@/api/product-model";
import { IProductModel } from "@/models";
import { computed, ref, watch } from "vue";

const emits = defineEmits<{
  (e: "update:modelValue", val?: string): void;
  (e: "onSelectModel", val: IProductModel): void;
}>();
const allProductModel = ref<Array<IProductModel>>([]);

const props = withDefaults(
  defineProps<{
    modelValue?: string;
    placeholder?: string;
    productCategoryId?: string;
    filterable?: boolean;
  }>(),
  {
    placeholder: "请选择型号",
    filterable: true
  }
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: string) {
    emits("update:modelValue", val);
  }
});

watch(
  () => props.productCategoryId,
  async (productCategoryId: string) => {
    modelValue.value = null;
    if (productCategoryId) {
      const { data } = await queryAllProductModelByCategoryId(props.productCategoryId);
      allProductModel.value = data;
    } else {
      allProductModel.value = [];
    }
  }
);

const onSelectModel = (modelName: string) => {
  const productModel = allProductModel.value.find(item => item.modelName === modelName);
  emits("onSelectModel", productModel);
};
</script>
