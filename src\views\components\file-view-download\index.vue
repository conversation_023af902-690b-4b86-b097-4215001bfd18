<template>
  <slot />
  <!-- <div>
    <el-popover placement="left" :popper-style="{ width: '80px' }" :show-after="100">
      <div class="text-center">
        <el-button type="primary" link @click="onViewPreview()"> 预览 </el-button>
        <el-divider class="!my-4" />
        <el-button type="primary" link @click="onDownload()"> 下载 </el-button>
      </div>
      <template #reference>
        <slot />
      </template>
    </el-popover>
    <fileExcelPreview v-if="isExcel" :src="fileUrl" :name="fileName" v-model="filePreviewShow" />
    <FileImagePreview v-else :src="fileUrl" :name="fileName" v-model="filePreviewShow" />
  </div> -->
</template>

<script setup lang="ts">
//import { downLoadFile } from "@/api/upload-file";
// import fileExcelPreview from "@/views/components/file-view-download/file-excel-preview.vue";
// import FileImagePreview from "@/views/components/file-view-download/file-image-preview.vue";
// import { downloadByData } from "@pureadmin/utils";
// import { computed, ref } from "vue";

// const props = withDefaults(
//   defineProps<{
//     fileId?: string;
//     fileName?: string;
//     fileUrl?: string;
//     fileType?: string;
//   }>(),
//   {}
// );
// const isExcel = computed(() => props.fileName?.endsWith(".xlsx") || props.fileName?.endsWith(".xls"));
// const filePreviewShow = ref(false);
// const onViewPreview = () => {
//   filePreviewShow.value = true;
// };

// const onDownload = async () => {
//   const blob = await downLoadFile(props.fileId);
//   downloadByData(blob, props.fileName, blob.type);
// };
</script>
