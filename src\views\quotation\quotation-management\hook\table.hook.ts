import { VTable } from "@visactor/vue-vtable";
import Edit from "@/assets/svg/edit.svg?url";
import Delete from "@/assets/svg/delete.svg?url";
import Filter from "@/assets/svg/filter.svg?url";
import IconMatchingRaw from "@/assets/svg/icon_matching.svg?raw";
import IconQuivalentMatchingRaw from "@/assets/svg/icon_quivalent_matching.svg?raw";
import IconUnmatchedRaw from "@/assets/svg/icon_unmatched.svg?raw";
import { ref } from "vue";

export const useTable = () => {
  const editIcon = "edit-icon";
  const deleteIcon = "delete-icon";
  const filterIcon = "filter-icon";
  const filterIconModelName = "filter-icon-model-name";
  const iconMatching = "icon-matching";
  const iconQuivalentMatching = "icon_quivalent_matching";
  const iconUnmatched = "icon_unmatched";

  const tableOption = ref<Record<string, any>>({
    select: {
      disableSelect: true
    },
    customConfig: {
      createReactContainer: true
    },
    //  autoFillWidth: true,
    autoFillHeight: false,
    // autoFillHeight: true,
    tooltip: {
      renderMode: "html",
      overflowTextTooltipDisappearDelay: 100,
      isShowOverflowTextTooltip: true
    },
    fontWeight: 400,
    fontSize: 14,
    editCellTrigger: "doubleclick",
    // containerFit: {
    //   //width: true,
    //   height: false
    // },
    showFrozenIcon: false,
    frozenColCount: 7,
    rightFrozenColCount: 6,
    resize: {
      disableDblclickAutoResizeColWidth: true
    }
  });

  const registerIcon = () => {
    VTable.register.icon(editIcon, {
      type: "svg",
      svg: Edit,
      width: 14,
      height: 14,
      name: editIcon,
      positionType: VTable.TYPES.IconPosition.right,
      marginRight: 10,
      hover: {
        width: 14,
        height: 14,
        bgColor: "rgba(101, 117, 168, 0.1)"
      },
      cursor: "pointer"
    });

    VTable.register.icon(deleteIcon, {
      type: "svg",
      svg: Delete,
      width: 14,
      height: 14,
      name: deleteIcon,
      positionType: VTable.TYPES.IconPosition.right,
      marginRight: 0,
      hover: {
        width: 14,
        height: 14,
        bgColor: "rgba(101, 117, 168, 0.1)"
      },
      cursor: "pointer"
    });

    VTable.register.icon(filterIcon, {
      type: "svg",
      svg: Filter,
      width: 16,
      height: 16,
      name: filterIcon,
      positionType: VTable.TYPES.IconPosition.right,
      marginRight: 0,
      hover: {
        width: 14,
        height: 14,
        bgColor: "rgba(101, 117, 168, 0.1)"
      },
      cursor: "pointer"
    });

    VTable.register.icon(filterIconModelName, {
      type: "svg",
      svg: Filter,
      width: 16,
      height: 16,
      name: filterIconModelName,
      positionType: VTable.TYPES.IconPosition.right,
      marginRight: 0,
      hover: {
        width: 14,
        height: 14,
        bgColor: "rgba(101, 117, 168, 0.1)"
      },
      cursor: "pointer"
    });

    VTable.register.icon(iconMatching, {
      type: "svg",
      svg: IconMatchingRaw,
      width: 16,
      height: 16,
      name: iconMatching,
      positionType: VTable.TYPES.IconPosition.right,
      marginRight: 0,
      hover: {
        width: 14,
        height: 14,
        bgColor: "rgba(101, 117, 168, 0.1)"
      }
    });
    VTable.register.icon(iconQuivalentMatching, {
      type: "svg",
      svg: IconQuivalentMatchingRaw,
      width: 16,
      height: 16,
      name: iconMatching,
      positionType: VTable.TYPES.IconPosition.right,
      marginRight: 0,
      hover: {
        width: 14,
        height: 14,
        bgColor: "rgba(101, 117, 168, 0.1)"
      }
    });
    VTable.register.icon(iconUnmatched, {
      type: "svg",
      svg: IconUnmatchedRaw,
      width: 16,
      height: 16,
      name: iconUnmatched,
      positionType: VTable.TYPES.IconPosition.right,
      marginRight: 0,
      hover: {
        width: 14,
        height: 14,
        bgColor: "rgba(101, 117, 168, 0.1)"
      }
    });
  };
  return {
    registerIcon,
    filterIcon,
    filterIconModelName,
    editIcon,
    deleteIcon,
    iconMatching,
    iconQuivalentMatching,
    iconUnmatched,
    IconMatchingRaw,
    IconQuivalentMatchingRaw,
    IconUnmatchedRaw,
    tableOption
  };
};
