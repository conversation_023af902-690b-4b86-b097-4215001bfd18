import { IRemittanceInfo, IRemittanceInfoForm, IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

export const getRemittanceInfo = () => {
  return http.get<void, IResponse<IRemittanceInfo>>(withApiGateway("admin-api/system/tenant/get"));
};

export const createOrUpdateRemittanceInfo = (data: IRemittanceInfoForm) => {
  return http.post<IRemittanceInfoForm, IResponse<IRemittanceInfo>>(withApiGateway("admin-api/system/tenant/get"), {
    data
  });
};
