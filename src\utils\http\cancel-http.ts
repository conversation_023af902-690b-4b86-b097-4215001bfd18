import { tryOnUnmounted } from "@vueuse/core";
import { ref } from "vue";

export function useCancelHttp() {
  let controller: AbortController;
  const signal = ref<AbortSignal>();

  _initControl();

  function abort(reason?: any) {
    controller.abort(reason);
  }

  function abortAndCreateNewController(reason?: any) {
    abort(reason);
    _initControl();
  }

  tryOnUnmounted(() => {
    abort();
  });

  function _initControl() {
    controller = new AbortController();
    signal.value = controller.signal;
  }

  return {
    signal,
    abort,
    abortAndCreateNewController
  };
}
