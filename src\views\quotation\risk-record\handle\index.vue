<template>
  <div class="px-5 pt-[10px]">
    <div class="card">
      <el-descriptions class="risk-info" :column="2">
        <el-descriptions-item class-name="value" label-class-name="label" label="告警时间">{{
          inquiryRiskRef.riskTime
        }}</el-descriptions-item>
        <el-descriptions-item class-name="value" label-class-name="label" label="销售人员"
          ><CxEmployee :name="inquiryRiskRef.operatorName"
        /></el-descriptions-item>
        <el-descriptions-item class-name="value" label-class-name="label" label="询价单位">{{
          inquiryRiskRef.inquiryOrgName
        }}</el-descriptions-item>
        <el-descriptions-item class-name="value" label-class-name="label" label="相似度"
          >{{ inquiryRiskRef.highRiskSimilarity }}%</el-descriptions-item
        >
        <el-descriptions-item class-name="value" label-class-name="label" label="关联销售">
          <div class="flex gap-[10px]" v-if="inquiryRiskRef.similarityInquiryOperatorName?.length">
            <CxEmployee
              v-for="(item, index) in inquiryRiskRef.similarityInquiryOperatorName"
              :name="item"
              :key="index"
            />
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top" class="p-0">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="处理时间" prop="riskDisposeTime">
            <el-date-picker
              class="!w-full"
              v-model="form.riskDisposeTime"
              type="datetime"
              placeholder="请选择处理时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处理人" prop="handlerName">
            <el-input v-model="form.handlerName" clearable placeholder="请输入处理人" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注" prop="riskDisposeRemark">
            <el-input v-model="form.riskDisposeRemark" :rows="2" type="textarea" clearable placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { IInquiryRisk, IInquiryRiskHandle } from "@/models";
import { onMounted, reactive, ref } from "vue";
import CxEmployee from "@/components/cx-employee/index.vue";
import { FormInstance, FormRules } from "element-plus";
import { getInquiryRiskDetailById } from "@/api/quotation/inquiry-risk";

defineExpose({
  validateForm,
  getFormValue
});

const form = reactive<IInquiryRiskHandle>({});
const formRef = ref<FormInstance>();
const rules: FormRules = {
  riskDisposeTime: [{ required: true, trigger: "change", message: "处理时间不能为空" }],
  riskDisposeOperatorId: [{ required: true, trigger: "change", message: "处理任不能为空" }],
  riskDisposeRemark: [{ required: true, trigger: "change", message: "备注不能为空" }]
};

const props = withDefaults(defineProps<{ riskId: string }>(), {});

const inquiryRiskRef = ref<IInquiryRisk>({});

onMounted(() => {
  handleGetRiskRecordDetail(props.riskId);
});

const handleGetRiskRecordDetail = async id => {
  const { data } = await getInquiryRiskDetailById(id);
  inquiryRiskRef.value = data;
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}
</script>

<style scoped lang="scss">
.card {
  border: 1px solid #ebeef5;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;

  .risk-info {
    :deep(.el-descriptions__body) {
      .el-descriptions__table {
        tbody {
          tr {
            td {
              width: 50%;

              .label {
                font-size: 14px;
                font-weight: normal;
                line-height: 22px;
                letter-spacing: 0;
                color: #303133;
                margin-right: 16px;
              }

              .value {
                font-size: 14px;
                line-height: 22px;
                letter-spacing: 0;
                color: #606266;
              }
            }
          }

          tr:nth-of-type(2) {
            padding: 16px 0;
            display: block;
          }
        }
      }
    }
  }
}
</style>
