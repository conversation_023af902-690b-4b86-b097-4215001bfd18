import type { Emitter } from "mitt";
import mitt from "mitt";

type Events = {
  resize: {
    detail: {
      width: number;
      height: number;
    };
  };
  openPanel: string;
  tagViewsChange: string;
  tagViewsShowModel: string;
  logoChange: boolean;
  changLayoutRoute: {
    indexPath: string;
    parentPath: string;
  };
  formChange: {
    value: string;
    key: string;
  };
  /** 来自生产数据完整性检查的事件 */
  dataIntegrity: {
    index: number;
  };

  /** 刷新工单和报工完整性提示 */
  refreshWorkOrderAndReportWorkTip: string;

  /** 刷新质量追溯记录详情分类列表 */
  refreshQualityTracingRecordDetailCategoryList: string;

  /** 导出记录新增提示 */
  exportRecordAddTip: string;

  /** 刷新过程检测列表 */
  refreshProcessInspecList: string;

  /** 刷新出厂试验列表 */
  refreshOutGoingFactoryExperimentList: string;

  /** 刷新采购订单列表 */
  refreshPurchaseOrderList: string;

  /** 刷新销售订单列表 */
  refreshSalesOrderList: string;

  /** 刷新Ecode标准树 */
  refreshEcodeStandardTree: string;
};

export const emitter: Emitter<Events> = mitt<Events>();
