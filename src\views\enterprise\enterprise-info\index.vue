<template>
  <div class="bg-bg_color p-10 overflow-hidden w-[960px] m-auto mt-5 mb-5 h-full">
    <el-form ref="formRef" :model="form" :rules="rules" size="large" label-width="100px" :disabled="disabledEditState">
      <div>
        <TitleBar class="mb-4" title="企业信息" />
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="企业名称" prop="comName">
              <el-input placeholder="请输入企业名称" v-model="form.comName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="企业简称" prop="name">
              <el-input placeholder="请输入企业简称" v-model="form.name" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="企业地址" prop="address">
              <el-input placeholder="请输入企业地址" v-model="form.address" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider />
        <TitleBar class="mb-4" title="联系人信息" />
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="联系人姓名" prop="contactName">
              <el-input placeholder="请输入联系人姓名" v-model="form.contactName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人电话" prop="contactMobile">
              <el-input placeholder="请输入联系人手机" v-model="form.contactMobile" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人邮箱" prop="contactEmail">
              <el-input placeholder="请输入联系人邮箱" v-model="form.contactEmail" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="mt-5 text-right">
      <div v-auth="PermissionKey.enterprise.enterpriseInfoEdit">
        <template v-if="disabledEditState">
          <ElButton type="primary" size="large" @click="onToggleEditState(false)">修改</ElButton>
        </template>
      </div>
      <template v-if="!disabledEditState">
        <ElButton size="large" @click="onToggleEditState(true)">取消</ElButton>
        <ElButton type="primary" size="large" @click="onSaveEditEnterpriseInfo()">保存</ElButton>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { ElForm, ElMessage, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { ITenant, ITenantForm } from "@/models";
import { useTenantStore } from "@/store/modules";
import { EMAIL_REGEXP, MOBILE_REGEXP, PermissionKey } from "@/consts";
import TitleBar from "@/components/TitleBar";
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
import { useUserStore } from "@/store/modules/user";

usePageStoreHook().setTitle((useRoute().meta?.title as string) || "企业信息");

const userStore = useUserStore();
const tenantStore = useTenantStore();
const formRef = ref<FormInstance>();
const disabledEditState = ref<boolean>(true);
const form = reactive<ITenantForm>({
  id: undefined,
  comName: undefined,
  name: undefined,
  address: undefined,
  contactName: undefined,
  contactMobile: undefined,
  contactEmail: undefined,
  status: undefined
});

const rules: FormRules = {
  comName: [{ required: true, message: requiredMessage("企业名称"), trigger: "change" }],
  name: [{ required: true, message: requiredMessage("企业简称"), trigger: "change" }],
  status: [{ required: true, message: requiredMessage("状态"), trigger: "change" }],
  contactMobile: [{ trigger: "change", message: "手机号码格式不正确", pattern: MOBILE_REGEXP }],
  contactEmail: [{ trigger: "change", message: "邮箱格式不正确", pattern: EMAIL_REGEXP }]
};

watchEffect(async () => {
  const tenantInfo = (await userStore.getProfile)?.tenantInfo;
  if (!tenantInfo) {
    Object.assign(form, {});
    return;
  }
  const tenantInfoRes = await tenantStore.getTenantDetailById(tenantInfo.id);
  Object.assign(form, tenantInfoRes);
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

const onToggleEditState = (state: boolean) => {
  if (state) {
    Object.assign(form, userStore.profile.tenantInfo);
  }
  disabledEditState.value = state;
};

const onSaveEditEnterpriseInfo = async () => {
  if (!(await validate())) {
    return;
  }
  await tenantStore.editEnterpriseInfo(form);
  ElMessage.success("编辑成功");
  userStore.setUserTenantInfo({ ...form, createTime: userStore.profile.tenantInfo.createTime });
  disabledEditState.value = true;
  const accountTenants: Array<ITenant> = await userStore.getAccountTenants;
  const editIndex: number = accountTenants.findIndex(x => x.id === form.id);
  accountTenants[editIndex] = { ...form, createTime: accountTenants[editIndex].createTime };
  userStore.setAccountTenants(accountTenants);
};
</script>

<style scoped></style>
