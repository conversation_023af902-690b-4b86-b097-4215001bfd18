import { ISynchronizationFlag, ISynchronizationFlagReq, ISynchronizationFlagForm } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/platform/synchronization-flag";

export const useSynchronizationFlagStore = defineStore({
  id: "cx-synchronization-flag",
  state: () => ({
    flagList: [] as Array<ISynchronizationFlag>,
    total: 0,
    loading: false,
    synchronizationFlagDetail: {} as ISynchronizationFlagForm
  }),
  actions: {
    /** 查询同步标识列表 */
    async querySynchronizationFlag(params?: ISynchronizationFlagReq) {
      this.loading = true;

      if (params) {
        Object.keys(params).forEach(key => {
          if (!params[key] && typeof params[key] !== "boolean") {
            delete params[key];
          }
        });
      }

      const res = await api.querySynchronizationFlag(params);
      this.flagList = res.data.list;
      this.total = res.data.total;
      this.loading = false;
    },

    /** 新增同步标识 */
    async createSynchronizationFlag(data: ISynchronizationFlagForm) {
      return api.createSynchronizationFlag(data);
    },

    /** 编辑同步标识 */
    async editSynchronizationFlag(data: ISynchronizationFlagForm) {
      return api.editSynchronizationFlag(data);
    },

    /** 编辑同步标识赋值 */
    setSynchronizationFlagStorage(flagDetail: ISynchronizationFlagForm) {
      this.synchronizationFlagDetail = flagDetail;
    },

    /** 编辑同步标识清空 */
    clearSynchronizationFlagStorage() {
      this.synchronizationFlagDetail = {};
    }
  }
});
