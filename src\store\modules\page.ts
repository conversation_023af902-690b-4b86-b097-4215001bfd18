import { store } from "@/store";
import { pageType } from "./types";
import { defineStore } from "pinia";
import { Component } from "vue";

export const usePageStore = defineStore({
  id: "cx-page",
  state: (): pageType => ({
    title: undefined,
    component: undefined
  }),
  getters: {
    getTitle: state => state.title,
    getComponent: state => state.component
  },
  actions: {
    setTitle(title: string) {
      this.title = title;
    },
    clearTitle() {
      this.title = undefined;
    },
    setComponent(component?: string | Component) {
      this.component = component;
    }
  }
});

export function usePageStoreHook() {
  return usePageStore(store);
}
