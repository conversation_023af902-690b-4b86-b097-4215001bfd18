<template>
  <div class="table-form">
    <div class="table-content">
      <el-form ref="tableFormRef" :model="forms" class="rules-form" :scroll-to-error="true">
        <el-table :data="cloneDynamicFormData" border>
          <el-table-column width="220px" :label="$t('tableFormThead.columns.checkItem')">
            <template #default="{ row }">
              <div class="label-content">
                <span class="item-name">{{ row.targetName }}</span>
                <div class="require">
                  <span v-if="row.required && editMode">*</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="220px"
            :label="$t('tableFormThead.columns.checkTotal')"
            :class-name="editMode ? 'target-value' : null"
          >
            <template #default="{ row }">
              <el-form-item :rules="currentFormRules(row)" :prop="row[formControlKeyWords]">
                <Control
                  :editMode="editMode"
                  :isAdd="isAdd"
                  :rowData="row"
                  :table-control-key-words="tableControlKeyWords"
                  :form-control-key-words="formControlKeyWords"
                  :formValue="forms"
                  @formValueChange="formValueChange($event, row)"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            width="100px"
            prop="unit"
            :label="$t('tableFormThead.columns.unit')"
            :formatter="formatterEmpty"
          />
          <el-table-column label="数据格式要求">
            <template #default="{ row }">
              <div class="datumOrganization-content">
                <span class="datumOrganization">{{ formatterEmpty(null, null, row.datumOrganization) }}</span>
              </div>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty :image-size="120">
              <template #image> <EmptyData /> </template>
            </el-empty>
          </template>
        </el-table>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import Control from "./control.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { FormInstance, FormRules } from "element-plus";
import { reactive, ref, toRaw } from "vue";
import { useTableForm } from "./form-value-hooks";
import { EControlType } from "@/enums";
import { cloneDeep, isNullOrUnDef } from "@pureadmin/utils";
import { requiredMessage } from "@/utils/form";
import { IDynamicFormItem, IControlValueChange } from "@/models/dynamic-form/i-dynamic-form";
import { formatterEmpty, handleNumber } from "../src/tools/index";

const props = withDefaults(
  defineProps<{
    /** 是 展示 还是 编辑 */
    editMode: boolean;
    /** 是否是新增 */
    isAdd: boolean;
    dynamicFormData: Array<IDynamicFormItem>; // 表格表单数据
  }>(),
  {
    dynamicFormData: () => [],
    isAdd: false
  }
);

const emits = defineEmits<{
  (event: "formControlValueChange", configData): void;
}>();

const tableControlKeyWords = `tableControlKey`;
const formControlKeyWords = `formControlKey`;
const tableFormRef = ref<FormInstance>();
const rules = reactive<FormRules>({});
const { formatDynamicFormData, patchFormByData } = useTableForm();
const forms = ref();
const cloneDynamicFormData = ref();

// 飞哥在此为所有表单数据添加了一个标记
const { collectionData, formValue } = formatDynamicFormData(
  props.dynamicFormData,
  tableControlKeyWords,
  formControlKeyWords
);
// console.log(collectionData, "ooooo", formValue);
forms.value = formValue;
cloneDynamicFormData.value = collectionData;

// 导出的组装表单数据
const dyicFormSubmitData = reactive({
  submitData: []
});

/**
 * 处理组装数据
 * @param config
 * @param value
 */
const updateDyicFormSubmitData = (config: IDynamicFormItem, value, formControlKeyWords: string) => {
  // 表单控件挂载值
  const controlKey = config[formControlKeyWords];
  config[controlKey] = value;
  const newFormData = {
    ...config,
    targetValue: value
  };
  const submitData = dyicFormSubmitData.submitData || [];
  // 去除重复的数据
  const isHas = submitData.find(item => item.id === newFormData.id);
  let hasIndex = undefined;
  if (isHas) {
    hasIndex = submitData.findIndex(item => item.id === newFormData.id);
    submitData.splice(hasIndex, 1);
  }
  dyicFormSubmitData.submitData.push(newFormData);
};

// 设置表单值
patchFormByData(cloneDynamicFormData.value, forms.value, tableControlKeyWords, formControlKeyWords, props.editMode);

// 根据默认值设置导出的表单值
const patchSubmitData = (dynamicFormData, tableControlKeyWords: string, formControlKeyWords: string) => {
  (dynamicFormData || []).forEach(item => {
    const controlKey = item[tableControlKeyWords] || "";
    switch (controlKey) {
      case EControlType.TextControl:
        updateDyicFormSubmitData(item, item.targetValue, formControlKeyWords);
        break;
      case EControlType.NumberTextControl:
        updateDyicFormSubmitData(item, handleNumber(item.targetValue), formControlKeyWords);
        break;
      case EControlType.SelectControl:
        updateDyicFormSubmitData(item, item.targetValue, formControlKeyWords);
        break;
      case EControlType.RadioControl:
        updateDyicFormSubmitData(item, item.targetValue, formControlKeyWords);
        break;
      case EControlType.WaveRoseControl:
        updateDyicFormSubmitData(item, item.targetValue, formControlKeyWords);
        break;
      default:
        break;
    }
  });
};
patchSubmitData(cloneDynamicFormData.value, tableControlKeyWords, formControlKeyWords);

/**
 * 验证表单
 * @param formEl
 */
const submitValidForm = async (formEl: FormInstance | undefined = tableFormRef.value) => {
  if (!formEl) return;
  return await formEl.validate(() => {});
};

/**
 * 表单校验类型
 * @param rowData
 */
const currentFormRules = (rowData: IDynamicFormItem) => {
  if (rowData) {
    const rowConfigData = cloneDeep(rowData);
    const controlKey = rowConfigData[tableControlKeyWords] || "";
    const isRequired = rowData?.required;
    switch (controlKey) {
      case EControlType.TextControl:
        return (rules.textRules = [
          {
            type: "string",
            required: isRequired,
            message: requiredMessage("检测值"),
            trigger: "change"
          }
        ]);
      case EControlType.NumberTextControl:
        return (rules.numberTextRules = [
          {
            type: "string",
            required: isRequired,
            message: requiredMessage("检测值"),
            trigger: "blur"
          }
        ]);
      case EControlType.SelectControl:
        return (rules.selectRules = [
          {
            required: isRequired,
            message: "请选择数据",
            trigger: "change"
          }
        ]);
      case EControlType.DateControl:
        return (rules.dateRules = [
          {
            type: "date",
            required: isRequired,
            message: "请选择日期数据",
            trigger: "change"
          }
        ]);
      case EControlType.RadioControl:
        return (rules.dateRules = [
          {
            required: isRequired,
            message: "请选择",
            trigger: "change"
          }
        ]);
      default:
        break;
    }
  }
};

/**
 * 控件数据变化时，更新表单值
 * @param data
 */
const formValueChange = (data: IControlValueChange, rowData: IDynamicFormItem) => {
  const { key, value, config } = data;
  emits("formControlValueChange", data);
  // 唯一控件Id
  const controlValueId = rowData[formControlKeyWords];
  switch (key) {
    case EControlType.TextControl:
      // 设置校验字段值
      setValidatedValue(config, value, key);
      forms.value[controlValueId] = value;
      updateDyicFormSubmitData(config, value, formControlKeyWords);
      break;
    case EControlType.NumberTextControl:
      // 设置校验字段值
      setValidatedValue(config, value, key);
      forms.value[controlValueId] = value === null ? null : value;
      updateDyicFormSubmitData(config, value, formControlKeyWords);
      break;
    case EControlType.SelectControl:
      forms.value[controlValueId] = value;
      updateDyicFormSubmitData(config, value, formControlKeyWords);
      break;
    case EControlType.RadioControl:
      forms.value[controlValueId] = value;
      updateDyicFormSubmitData(config, value, formControlKeyWords);
      break;
    default:
      break;
  }
};

/**
 * 校验表单值规范
 */
function setValidatedValue(config: IDynamicFormItem, value: any, key: string) {
  if (config) {
    let validated = true;
    const {
      id,
      maxStrLength,
      useRegular,
      regular,
      validMaxValue,
      maxValue,
      includeMaxValue,
      validMinValue,
      minValue,
      includeMinValue,
      decimalDigits
    } = config;
    switch (key) {
      case EControlType.TextControl:
        if (useRegular) {
          const reg = new RegExp(regular);
          validated = regular?.length ? reg.test(value) : true;
        } else {
          validated = maxStrLength ? value?.length <= maxStrLength : true;
        }
        handleValidate(id, validated);
        break;
      case EControlType.NumberTextControl:
        // 校验最大值 及最小值
        if (validMaxValue) {
          if (includeMaxValue) {
            validated = value <= maxValue;
          } else {
            validated = value < maxValue;
          }
        }
        // 最小值
        if (validMinValue) {
          if (includeMinValue) {
            validated = validated && value >= minValue;
          } else {
            validated = validated && value > minValue;
          }
        }

        if (!validMaxValue && !validMinValue) {
          validated = true;
        }

        // 校验小数点位数
        if (!isNullOrUnDef(value)) {
          const valStr = value + "";
          const posIndex = valStr.lastIndexOf(".");
          if (posIndex !== -1) {
            const pointerLength = valStr.substring(posIndex + 1)?.length;
            // 如果有小数位校验
            if (decimalDigits) {
              validated = pointerLength <= decimalDigits;
            } else {
              validated = pointerLength <= 0;
            }
          }
        }
        handleValidate(id, validated);
        break;
      default:
        break;
    }
  }
}

/**
 * 设置 validated
 */
function handleValidate(id: string, validated: boolean) {
  (cloneDynamicFormData.value || []).forEach((item: IDynamicFormItem) => {
    if (item.id === id) {
      item.validated = validated;
    }
  });
}

/**
 * 导出表单数据
 */
const payloadFormData = async () => {
  const valid = await submitValidForm();
  if (valid) {
    return toRaw(dyicFormSubmitData);
  }
};

defineExpose({
  forms, // 仅表单数据
  tableFormRef, // 表单实例
  dyicFormSubmitData, // 最终表单数据
  payloadFormData,
  // 获取飞哥的动态表单验证结果
  validDynamicForm: submitValidForm
});
</script>

<style scoped lang="scss">
.table-form {
  .label-content {
    display: flex;

    .item-name {
      display: flex;
    }

    .require {
      margin-left: 4px;
      width: 8px;
      color: var(--el-color-danger);

      span {
        vertical-align: middle;
      }
    }
  }

  .datumOrganization-content {
    &.validate-error {
      color: var(--el-color-warning);
    }
  }

  :deep(.el-form-item.asterisk-left) {
    margin-bottom: 0px;
  }

  :deep(.el-form-item.is-required) {
    margin-bottom: 0px;
  }

  :deep(.el-form-item.is-required.is-error) {
    // margin-bottom: 18px;
  }

  :deep(.target-value) {
    padding: 2px 0;

    .cell {
      padding: 15px 12px;
    }
  }

  :deep(.el-table--border) {
    &::after {
      background-color: var(--el-table-border-color) !important;
    }
  }
}
</style>
