buttons:
  hsLoginOut: 退出系统
  hsUserSetting: 个人设置
  hsadd: 新增
  hscloseAllTabs: 关闭全部标签页
  hscloseCurrentTab: 关闭当前标签页
  hscloseLeftTabs: 关闭左侧标签页
  hscloseOtherTabs: 关闭其他标签页
  hscloseRightTabs: 关闭右侧标签页
  hscollapseAll: 全部折叠
  hscontentExitFullScreen: 内容区退出全屏
  hscontentFullScreen: 内容区全屏
  hsdelete: 删除
  hsexitfullscreen: 退出全屏
  hsexpendAll: 全部展开
  hsfullscreen: 全屏
  hslogin: 登录
  hsmark: 标记/取消
  hsrefreshRoute: 刷新路由
  hsreload: 重新加载
  hssave: 保存
  hssearch: 搜索
  hssystemSet: 打开项目配置
  hswholeExitFullScreen: 退出全屏
  hswholeFullScreen: 全屏
fillInProductOrder:
  buttons:
    addProductOrder: 新增生产订单
    deleteProductOrder: 删除
    editProductOrder: 编辑
    viewProductOrderDetail: 详情
  columns:
    actualEndTime: 实际结束时间
    actualStartTime: 实际开始时间
    model: 规格型号
    operation: 操作
    planDate: 计划时间
    productOrderNo: 生产订单号
    quantity: 需求数量
    saleOrderLine: 销售订单行项目
    saleOrderNo: 销售订单号
    subClassName: 物料名称
    unit: 计量单位
  status:
    fillInProductData: 填报生产数据
    noProductOrderLine: 需生产销售订单行
    number: 条
    productOrderLine: 有生产销售订单行数
    saleOrderLineTotal: 销售订单行总数
linkStep:
  fillIn: 填报
  productionSchedulingPlan: 排产计划
  salesOrder: 销售订单
  triggerStateGrid: 触发国网
login:
  company: 程析智能
  forget: 忘记密码?
  login: 登 录
  password: 密码
  passwordReg: 请输入密码
  passwordRuleReg: 密码格式应为8-18位数字、字母、符号的任意两种组合
  username: 用户名
  usernameReg: 请输入用户名
  welcome: 登录到
menus:
  hsFive: "500"
  hsabnormal: 异常页面
  hsfourZeroFour: "404"
  hsfourZeroOne: "403"
  hshome: 首页
  hslogin: 登录
  permission: 权限管理
  permissionButton: 按钮权限
  permissionPage: 页面权限
purchaseOrder:
  buttons:
    all: 全部
    productionData: 填报生产数据
    onlyUnRead: 仅看未读
    linkSales: 关联销售订单
    productionPlan: 制定排产计划
    syncOrder: 同步订单数据
    qualityEval: 触发质量评分
    productionOrder: 填报生产订单
  column:
    purchaseOrder:
      buyerName: 采购方公司名称
      conCode: 合同编号
      conName: 合同名称
      readed: 最新拉取
      linkStep: 当前填报环节
      poNo: "采购订单号"
      prjName: 项目名称
      sellerConCode: 合同编号(国网经法)
      sellerSignTime: 合同签订日期
      subClassName: 物资种类
      synState:
        "false": 同步失败
        "true": 同步成功
      synType: 同步结果
  enum:
    linkStep:
      productionData: 填报生产数据
      linkSales: 关联销售订单
      productionPlan: 制定排产计划
      syncOrder: 同步订单数据
      qualityEval: 触发质量评分
      productionOrder: 填报生产订单
    process:
      salesOrder: 关联销售订单
      productionSchedule: 制定排产计划
      productionOrder: 填报生产订单
      productionData: 填报生产数据
      sgOrder: 同步订单数据
      sgData: 触发质量评分
      salesOrderLine: 维护销售订单行
  menu:
    purchaseOrder: 采购订单
  status:
    lastPullNumber: 最新拉取条数
    lastPullTime: 最新拉取时间
    number: 条
    pullFailed: 拉取失败
    pullState: 拉取状态
    pullSuccessful: 拉取成功
searchForm:
  advanced: 高级搜索
  placeholder: 请输入关键字
  reset: 重置
  search: 搜索
status:
  hsLoad: 加载中...
enum:
  IOTSyncStatusEnum:
    NotSynced: 未同步
    Synced: 已同步
    SyncFailed: 同步失败
  SalesOrderStatus:
    CreateOrder: 创建订单
    InProduction: 生产中
    ProductTesting: 成品试验中
    ToBeShipped: 待发货
    PartialShipment: 部分发货
    ShipmentCompleted: 发货完成
    OrderCompletion: 订单完结
  productionStateEnum:
    CREATE: 创建
    RAW_MATERIAL_INSPECTION: 原材料检测
    IN_PRODUCTION: 生产中(包含原材料校验)
    ROUTINE_TEST: 出厂试验
    PACKAGING_WAREHOUSING: 包装入库
  deviceTerminalEnum:
    GIS_TERMINAL: GIS终端
    OUTDOOR_TERMINAL: 户外终端
    INDOOR_TERMINAL: 户内终端
    DEVICE_TERMINAL: 设备终端
    TRANSFORMER_TERMINAL: 变压器终端
  jointCategoriesEnum:
    CONTRACTION_TYPE: 冷缩式
    PREFAB: 预制式
  voltageTypeEnum:
    DIRECT_CURRENT: 直流
    INTERFLOW: 交流
  voltageClassesEnum:
    Voltage_1: "1 kV"
    Voltage_6: "6 kV"
    Voltage_10: "10 kV"
    Voltage_15: "15 kV"
    Voltage_20: "20 kV"
    Voltage_35: "35 kV"
    Voltage_66: "66 kV"
    Voltage_110: "110 kV"
    Voltage_160: "160 kV"
    Voltage_200: "200 kV"
    Voltage_220: "220 kV"
    Voltage_320: "320 kV"
    Voltage_330: "330 kV"
    Voltage_400: "400 kV"
    Voltage_500: "500 kV"
  unitEnum:
    COVER: 套
    ONLY: 只
  sparePartEnum:
    Others: 其他
  StateGridOrderSyncStep:
    SALES_LINE: 销售订单明细
    PRODUCTION_PLAN: 排产计划
    PRODUCTION: 生产订单
    WORK_ORDER: 工单信息
    WORK_REPORT: 报工信息
    MATERIAL_COMP_QUALITY: 原材料、组部件检验
    TECH_PROCESS_QUALITY: 过程检验数据
    OUTGOING_QUALITY: 出厂试验
    PRODUCTION_INFO: 成品信息
    TECHNICAL_STANDARD: 技术标准
    ATTACHED_REPORT: 工序文档
    PROCESS_QUALITY_AUTO_COLLECT: 生产过程自动采集项
  QualifiedEnum:
    QUALIFIED: 合格
    NOT_QUALIFIED: 不合格
  ShipmentStatusEnum:
    SHIPMENT: 已发货
    NOT_SHIPMENT: 未发货
  FunctionTypeEnum:
    PRODUCTION_DEVICE: 生产设备
    TEST_DEVICE: 普通试验设备
    JFNY_TEST_DEVICE: 局放耐压设备
  ContractTypeEnum:
    NORMAL: 标准合同
    AGREEMENT_INVENTORY: 协议库存
    PURCHASE_SUPPLY_ORDER: 采购供货单
  VirtualFlag:
    NORMAL: 普通订单
    VIRTUAL: 虚拟订单
  QMXExperimentStatusEnum:
    READY: 就绪
    PROCESSING: 试验中
    COMPLETED: 完成
    CANCEL: 取消
  QMXExperimentResultsEnum:
    PASS: 通过
    FAIL: 不通过
    IGNORE: 忽略
  StateGridOrderSyncResult:
    NO_SYNC: 未同步
    QUEUING: 排队中
    TRANSFERRING: 传输中
    SUCCESS: 已触发同步
    FAULT: 同步失败
    GATEWAY_FAULT: 网关拉取失败
  MaterialType:
    CABLE_ADAPTER: 电缆中间接头
    CABLE_TERMINAL: 电缆终端
  SexEnum:
    Man: 男
    Woman: 女
  StatusEnum:
    ENABLE: 启用
    DISABLE: 禁用
  StateGridOrderTriggerStatus:
    NO_TRIGGER: 未触发
    TRANSFERRING: 传输中
    SUCCESS: 已触发评分
    FAULT: 评分触发失败
  DataLogUpdateType:
    Add: 增加
    Update: 修改
    Delete: 删除
  CollectionModeEnum:
    MANUAL_FILL_IN: 手动填报
    AUTO_COLLECT: 自动采集
  CameraStatusEnum:
    START: 开启
    DISABLE: 关闭
  MonitorDeviceBrandEnum:
    HK: 海康
    DH: 大华
  MonitorDeviceSubTypeEnum:
    MAIN_STREAM: 主码流
    DISABLE: 子码流
  MonitorDeviceStatusEnum:
    START: 开启
    DISABLE: 关闭

tableFormThead:
  columns:
    checkItem: 检查项目
    checkTotal: 检测值
    unit: 单位

tableEmpty:
  emptyText: "暂无数据"

salesOrder:
  enum:
    linkStep:
      all: 全部
      maintainSalesOrderLine: 维护销售订单行
      productionPlan: 制定排产计划
      productionData: 填报生产数据
      productionOrder: 填报生产订单
      syncOrder: 同步订单数据
      qualityEval: 触发质量评分
