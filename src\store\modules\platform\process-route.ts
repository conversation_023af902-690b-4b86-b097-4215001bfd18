import { IProcessRoute, IProcessRouteForm, IProcessRouteExt } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/platform/process-route";

/**
 * 工艺路线
 */
export const useProcessRouteStore = defineStore({
  id: "cx-process-route",
  state: () => ({
    processRouteList: [] as Array<IProcessRoute>,
    loading: false,
    processRouteForm: {} as IProcessRoute,
    currentProcessDetail: {} as IProcessRouteExt
  }),
  actions: {
    /** 获取工艺路线 */
    async queryProcessRoute(id: string) {
      this.loading = true;
      const res = await api.queryProcessRoute(id);
      this.processRouteList = res.data;
      this.loading = false;
    },

    /** 获取工艺路线id详情 */
    async queryProcessRouteDeteil(id: string) {
      const res = await api.queryProcessRouteDeteil(id);
      this.processRouteForm = res.data;
    },

    emptyForm(form?: IProcessRouteForm) {
      this.processRouteForm = form;
    },

    /** 新增工艺路线 */
    async createProcessRoute(data: IProcessRouteForm) {
      return api.createProcessRoute(data);
    },

    /** 编辑工艺路线 */
    async editProcessRoute(data: IProcessRouteForm) {
      return api.editProcessRoute(data);
    },

    /** 删除工艺路线 */
    async deleteProcessRoute(processRouteId: string) {
      return api.deleteProcessRoute(processRouteId);
    },

    // 当前的工艺
    currentProcessRoute(data?: IProcessRoute) {
      this.currentProcessDetail = data;
    }
  }
});
