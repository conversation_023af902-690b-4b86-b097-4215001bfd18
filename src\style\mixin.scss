@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: transparent;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 20px;
  }
  &:hover::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    &:hover {
      background: rgba(144, 147, 153, 0.5);
    }
  }
}

@mixin productCard {
  @apply bg-bg_color px-5 pt-3 w-full flex flex-col overflow-hidden;
  .header {
    @apply bg-bg_color pb-3 flex-bc;
    .status {
      @apply flex-c;
      .title {
        @apply text-text_color_primary;
        font-size: 18px;
        font-weight: normal;
        line-height: 26px;
        letter-spacing: 0;
      }
    }
  }
}
