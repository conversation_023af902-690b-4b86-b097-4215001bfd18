<template>
  <div class="w-full flex edit-cell" :class="{ 'edit-model': !isEdit }">
    <!-- <div v-show="!isEdit" class="flex flex-1 h-full w-full items-center" @click="onToggelEdit()">
      <span> {{ props.rowData?.modelName }}</span>
      <span
        v-if="
          voltageLevelColumn &&
          voltageLevelColumn?.hidden &&
          voltageLevelColumn.voltageLevelMergeModelName &&
          props?.rowData?.voltageLevel
        "
      >
        -{{ props?.rowData?.voltageLevel }}
      </span>
      <span
        v-if="
          specificationColumn &&
          specificationColumn.hidden &&
          specificationColumn.specificationMergeModelName &&
          props?.rowData.specification
        "
      >
        -{{ props?.rowData?.specification }}
      </span>
    </div> -->
    <div class="flex gap-1 items-center text-[13px]">
      <div class="flex-1 text-center">
        <span class="whitespace-nowrap" v-show="!state.isEditModelName" @click="onEditModelName()">{{
          state.modelName
        }}</span>
        <el-input
          v-show="state.isEditModelName"
          v-model="state.modelName"
          :controls="false"
          @blur="onEditModelNameBlur()"
          :disabled="props.disabled"
        />
      </div>
      <div class="flex-1 text-center" v-if="voltageLevelColumn?.voltageLevelMergeModelName">
        <span class="whitespace-nowrap" v-show="!state.isEditVoltageLevel" @click="onEditVoltageLeve()">{{
          state.voltageLevel
        }}</span>
        <el-input
          v-show="state.isEditVoltageLevel"
          v-model="state.voltageLevel"
          :controls="false"
          @blur="onEditVoltageLevelBlur()"
          :disabled="props.disabled"
        />
      </div>
      <div class="flex-1 text-center" v-if="specificationColumn?.specificationMergeModelName">
        <span class="whitespace-nowrap" v-show="!state.isEditSpecification" @click="onEditSpecification()">{{
          state.specification
        }}</span>
        <el-input
          v-show="state.isEditSpecification"
          v-model="state.specification"
          :controls="false"
          @blur="onEditSpecificationlur()"
          :disabled="props.disabled"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { quotationInquiryEditItem } from "@/api/quotation/quotation-inquiry";
import { IQuotationInquiryDetail } from "@/models";
import { Column, ElMessage } from "element-plus";
import { reactive, ref, watch } from "vue";

const emits = defineEmits<{
  (e: "onEditSuccess"): void;
}>();

const props = withDefaults(
  defineProps<{
    rowData?: IQuotationInquiryDetail;
    voltageLevelColumn?: Column;
    specificationColumn?: Column;
    disabled?: boolean;
    handleOnEditSuccess?: Function;
  }>(),
  {
    disabled: false
  }
);

const isEdit = ref(true);
const state = reactive<{
  modelName?: string;
  voltageLevel?: string;
  specification?: string;
  isEditModelName?: boolean;
  isEditVoltageLevel?: boolean;
  isEditSpecification?: boolean;
}>({
  isEditModelName: true,
  isEditVoltageLevel: true,
  isEditSpecification: true
});

watch(
  () => props.rowData,
  data => {
    if (data && Object.keys(data).length > 0) {
      state.modelName = data.modelName;
      state.voltageLevel = data.voltageLevel;
      state.specification = data.specification;
    }
  },
  {
    immediate: true
  }
);

const onToggelEdit = () => {
  if (props.disabled) {
    return;
  }
  isEdit.value = !isEdit.value;
  state.isEditModelName = isEdit.value;
  state.isEditVoltageLevel = isEdit.value;
  state.isEditSpecification = isEdit.value;
};

const onEditModelName = () => {
  state.isEditModelName = true;
};

const onEditModelNameBlur = async () => {
  await onEditRowBlur();
  state.isEditModelName = false;
  onCheckDetailModel();
};

const onEditVoltageLeve = () => {
  state.isEditVoltageLevel = true;
};

const onEditVoltageLevelBlur = async () => {
  await onEditRowBlur();
  state.isEditVoltageLevel = false;
  onCheckDetailModel();
};

const onEditSpecification = () => {
  state.isEditSpecification = true;
};

const onEditSpecificationlur = async () => {
  await onEditRowBlur();
  state.isEditSpecification = false;
  onCheckDetailModel();
};

const onEditRowBlur = async () => {
  try {
    await quotationInquiryEditItem({ ...props.rowData, ...state });
    emits("onEditSuccess");
    props.handleOnEditSuccess && props.handleOnEditSuccess();
  } catch (_error) {
    ElMessage.success("修改失败");
  }
};

const onCheckDetailModel = () => {
  if (!state.isEditModelName && !state.isEditVoltageLevel && !state.isEditSpecification) {
    onToggelEdit();
  }
};
</script>

<style scoped lang="scss">
.edit-cell {
  height: 100%;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  &.edit-model {
    padding-left: 6px;

    &:hover {
      border: 1px dotted var(--el-color-primary);
    }
  }

  .edit {
    width: 30px;
    text-align: center;
    visibility: hidden;
  }

  &:hover {
    .edit {
      visibility: visible;
      cursor: pointer;
      width: 30px;
      text-align: center;
      color: var(--el-color-primary);
    }
  }
}
</style>
