import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "姓名",
      prop: "username",
      width: ColumnWidth.Char4
    },
    {
      label: "手机号",
      prop: "phone"
    },
    {
      label: "所属企业",
      prop: "enterprise"
    },
    {
      label: "所在部门",
      prop: "department"
    },
    {
      label: "岗位",
      prop: "position"
    },
    {
      label: "邮箱",
      prop: "email"
    },
    {
      label: "注册申请时间",
      prop: "applyTime",
      width: ColumnWidth.Char10
    },
    {
      label: "注册来源",
      prop: "registerSource",
      width: ColumnWidth.Char6,
      slot: "registerSource"
    },
    {
      label: "审核时间",
      prop: "reviewTime",
      width: ColumnWidth.Char10
    },
    {
      label: "审核人",
      prop: "reviewerName",
      width: ColumnWidth.Char4
    },

    {
      label: "审核备注",
      prop: "reviewRemark"
    },
    {
      label: "审核状态",
      prop: "reviewStatus",
      slot: "reviewStatus",
      fixed: "right",
      width: ColumnWidth.Char5
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation"
    }
  ];
  return { columns };
}
