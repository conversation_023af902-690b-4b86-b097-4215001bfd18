import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

/**
 * 枚举：最终显示字段策略
 */
export enum FinialDisplayStrategyEnum {
  /**
   * 规格引擎型号
   */
  RULE_MODEL = 0,

  /**
   * AI解析型号
   */
  AI_MODEL = 1,

  /**
   * 基础库名称
   */
  BASIC_MODEL = 2
}

/**
 * 最终显示字段策略的描述映射
 */
export const FinialDisplayStrategyEnumMapDesc: Record<FinialDisplayStrategyEnum, string> = {
  [FinialDisplayStrategyEnum.BASIC_MODEL]: "基础库型号",
  [FinialDisplayStrategyEnum.RULE_MODEL]: "规则处理后型号",
  [FinialDisplayStrategyEnum.AI_MODEL]: "AI-解析型号"
};
export const FinialDisplayStrategyEnumMapVoltageDesc: Record<FinialDisplayStrategyEnum, string> = {
  [FinialDisplayStrategyEnum.BASIC_MODEL]: "基础库电压",
  [FinialDisplayStrategyEnum.RULE_MODEL]: "规则处理后电压",
  [FinialDisplayStrategyEnum.AI_MODEL]: "AI-解析电压"
};

export const FinialDisplayStrategyOptions: Array<IOption> = mapDescToOptions<number>(FinialDisplayStrategyEnumMapDesc);

export const FinialDisplayStrategyVoltageOptions: Array<IOption> = mapDescToOptions<number>(
  FinialDisplayStrategyEnumMapVoltageDesc
);
