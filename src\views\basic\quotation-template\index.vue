<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="模板编号：">
          <ElInput class="!w-[360px]" clearable v-model="state.params.templateCode" placeholder="请输入模板编号" />
        </ElFormItem>

        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <AddEditQuotationTemplateDialog
        v-auth="PermissionKey.meta.metaQuotationTempCreate"
        mode="add"
        @post-save-success="onQuery()"
      >
        <template #trigger="{ openDialog }">
          <el-button class="mb-5" :icon="Plus" type="primary" @click="openDialog">新增</el-button>
        </template>
      </AddEditQuotationTemplateDialog>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #status="{ row }">
          <CxTag v-if="row.status" type="success">已启用</CxTag>
          <CxTag v-else type="warning">未启用</CxTag>
        </template>
        <template #inquiryAttachmentName="{ row }">
          <CxDownload
            :name="row.inquiryAttachmentName"
            :download-url="row.inquiryAttachmentUrl"
            :id="row.inquiryAttachmentId"
          />
        </template>
        <template #operation="data">
          <div>
            <AddEditQuotationTemplateDialog
              v-auth="PermissionKey.meta.metaQuotationTempEdit"
              mode="edit"
              :id="data.row.id"
              @post-save-success="onQuery()"
            >
              <template #trigger="{ openDialog }">
                <el-button link type="primary" @click="openDialog">编辑</el-button>
              </template>
            </AddEditQuotationTemplateDialog>
            <ElButton
              link
              type="danger"
              v-auth="PermissionKey.meta.metaQuotationTempDelete"
              @click="onDelete(data.row.id)"
            >
              删除
            </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="quotation-template">
import { onMounted, ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryQuotationTemplate, deleteQuotationTemplateById } from "@/api/basic";
import { IQuotationTemplate, IQuotationTemplateReq } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import AddEditQuotationTemplateDialog from "./add-edit-quotation-template/dialog.vue";
import CxTag from "@/components/CxTag/index.vue";
import CxDownload from "@/components/cx-download/index.vue";
import { PermissionKey } from "@/consts/permission-key";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IQuotationTemplate>;
  params: IQuotationTemplateReq;
}>({
  list: [],
  params: {}
});

onMounted(() => {
  requestList();
});

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteQuotationTemplateById(id);
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params: IQuotationTemplateReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryQuotationTemplate(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
