<template>
  <div class="cx-tenant flex-c w-70 h-12 px-2">
    <el-select v-model="state.tenantId" filterable @change="onChangeTenant($event)" class="w-full">
      <template #prefix>
        <FontIcon class="text-regular" icon="icon-company" />
      </template>
      <el-option v-for="item in state.tenants" :key="item.id" :label="item.comName" :value="item.id">
        <FontIcon class="pr-2" icon="icon-company" />
        <span>{{ item.comName }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ITenant } from "@/models";
import { useUserStore } from "@/store/modules/user";
import { getToken } from "@/utils/auth";
import { reactive, watchEffect } from "vue";
import { postMessage } from "@/utils/browsingContextsCommunication";
import { TopicEnum } from "@/utils/browsingContextsCommunication/topic.enum";

const userStore = useUserStore();
const state = reactive<{
  tenantId: string;
  tenants: Array<ITenant>;
}>({
  tenantId: "",
  tenants: []
});

watchEffect(async () => {
  if (getToken()) {
    await userStore.getProfile;
    await userStore.getAccountTenants;
  }
});

watchEffect(() => (state.tenants = userStore.accountTenants || []));
watchEffect(() => (state.tenantId = userStore.profile?.tenantInfo?.id));

const onChangeTenant = async (tenantId: string) => {
  await userStore.switchTenant(tenantId);
  postMessage(TopicEnum.SWITCH_TENANT);
};
</script>

<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  // box-shadow: none;

  &:hover .el-input__suffix {
    opacity: 1;
  }
}

:deep(.el-input__suffix) {
  opacity: 0;
}
</style>
