import { FinialDisplayStrategyEnum, MatchFieldStrategyEnum } from "@/enums";
import { IBase } from "../i-base";
import { IModelMatchCondition } from "./i-model-match-condition";
import { IVoltageMatchCondition } from "./i-voltage-match-condition";
import { ISpecificationMatchCondition } from "./i-specification-match-condition";
import { IModelAction } from "./i-model-action";
import { ISpecificationAction } from "./i-specification.action";

/**
 * 规则定义
 */
export interface IRuleDefinition extends IBase {
  /**
   * 规则状态(启用/禁用)
   */
  enabled?: boolean;

  /**
   * 规格名称
   */
  ruleName?: string;

  /**
   * 优先级
   * - 数字越小优先级越高
   * - 租户大于系统规则优先级
   */
  priority?: number;

  /**
   * 单条匹配后停止(默认停止)
   */
  stopIfMatched?: boolean;

  /**
   * 型号字段匹配策略
   */
  matchFieldStrategy?: MatchFieldStrategyEnum;

  /**
   * 最终型号展示策略
   */
  displayStrategy?: FinialDisplayStrategyEnum;

  /** 最终电压展示策略  */
  displayStrategyVoltage?: FinialDisplayStrategyEnum;

  /**
   * 匹配条件 - 型号
   */
  modelCondition?: IModelMatchCondition;

  /**
   * 匹配条件 - 电压
   */
  voltageCondition?: IVoltageMatchCondition;

  /**
   * 匹配条件 - 规格
   */
  specificationCondition?: ISpecificationMatchCondition;

  /**
   * 处理动作 - 型号
   */
  modelAction?: IModelAction;

  /**
   * 处理动作 - 规格
   */
  specificationAction?: ISpecificationAction;

  /**
   * 处理动作 - 电压
   */
  voltageAction?: ISpecificationAction;

  /**
   * 备注
   */
  remark?: string;
}
