import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "选择",
      prop: "selection",
      slot: "selection",
      width: ColumnWidth.Char3
    },
    {
      label: "产品分类",
      prop: "categoryName",
      slot: "categoryName"
    },
    {
      label: "产品Id",
      prop: "productCode",
      slot: "productCode"
    },
    {
      label: "型号",
      prop: "modelName",
      slot: "modelName",
      width: ColumnWidth.Char10
    },
    {
      label: "电压等级",
      prop: "voltage",
      slot: "voltage",
      width: ColumnWidth.Char10
    },
    {
      label: "规格",
      prop: "specification",
      slot: "specification",
      width: ColumnWidth.Char10
    },
    {
      label: "单价",
      prop: "price"
    },
    {
      label: "平方数",
      prop: "crossSection",
      slot: "crossSection"
    },
    {
      label: "单位",
      prop: "unit",
      slot: "unit"
    },
    {
      label: "成本",
      prop: "costPrice"
    },
    {
      label: "备注",
      prop: "remark"
    }
  ];
  return { columns };
}
