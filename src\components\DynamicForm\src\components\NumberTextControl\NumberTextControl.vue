<template>
  <div class="number-text">
    <!-- title="" 去除默认的校验提示信息 -->
    <el-input-number
      :model-value="inputValue as number"
      :precision="precision"
      class="mx-4"
      :class="{ 'warning-text': showWarning }"
      title=""
      :disabled="!!config?.disable"
      controls-position="right"
      placeholder="请输入"
      @keyup="onKeyup($event)"
      @change="onChange"
    />
  </div>
</template>

<script setup lang="ts">
import { EControlType } from "@/enums";
import { computed, ref } from "vue";

const props = defineProps({
  inputValue: {
    type: [Number, String],
    default: ""
  },
  config: {
    type: Object,
    default: () => ({})
  }
});

const precision = ref(initPrecision());

const emits = defineEmits(["valueChange"]);

const showWarning = computed(() => {
  const { maxValue, minValue, includeMaxValue, includeMinValue, decimalDigits, validMaxValue, validMinValue } =
    props.config;

  if (!props.inputValue) {
    return false;
  }
  // 获取当前小数点后的位数长度
  const curPrecision = props.inputValue.toString().split(".")[1]?.length || 0;
  // 校验小数点后位数是否符合配置要求
  if (decimalDigits && curPrecision !== decimalDigits) {
    return true;
  }

  // 如果maxValue和minValue都不存在精度也不存在，或者输入值为空，则不显示警告色
  if ((typeof maxValue !== "number" && typeof minValue !== "number") || props.inputValue === "") {
    return false;
  }

  // 如果不需要验证最大值和最小值，则不显示警告色
  if (!validMaxValue && !validMinValue) {
    return false;
  }

  const val = +props.inputValue;
  // 如果最大最小值都要验证
  if (validMaxValue && validMinValue) {
    // 如果是同时可以包含最大值和最小值的闭区间
    if (includeMaxValue && includeMinValue) {
      return val > maxValue || val < minValue;
    }

    // 如果是左开右闭区间
    if (includeMaxValue) {
      return val > maxValue || val <= minValue;
    }

    // 如果是左闭右开区间
    if (includeMinValue) {
      return val < minValue || val >= maxValue;
    }

    // 如果是开区间
    return val <= minValue || val >= maxValue;
  }

  // 只验证最大值
  if (validMaxValue) {
    if (includeMaxValue) {
      return val > maxValue;
    }
    return val >= maxValue;
  }

  // 只验证最小值
  if (validMinValue) {
    if (includeMinValue) {
      return val < minValue;
    }
    return val <= minValue;
  }

  return false;
});

function initPrecision() {
  if (!props.inputValue) {
    return 0;
  }
  const str = props.inputValue.toString();
  if (str.includes(".")) {
    return str.split(".")[1].length;
  }

  return 0;
}

/**
 * 保留小数位
 */
// const precisionValue = computed(() => (props.config?.decimalDigits ? props.config?.decimalDigits : undefined));

const onKeyup = e => {
  if (!e) {
    return;
  }

  let str = e.target.value;
  precision.value = str.includes(".") ? str.split(".")[1].length : 0;
  if (str.endsWith(".")) {
    str = str.slice(0, -1);
  }
  emits("valueChange", {
    value: str,
    key: EControlType.NumberTextControl,
    config: props.config
  });
};

const onChange = e => {
  // 当前小数点后的位数
  const curPrecision = e.toString().split(".")[1]?.length || 0;
  // 需要补0的位数
  const needZeroLength = precision.value - curPrecision;

  let value = e.toString().includes(".") ? e + "0".repeat(needZeroLength) : e + "." + "0".repeat(needZeroLength);
  if (value.endsWith(".")) {
    value = value.slice(0, -1);
  }
  emits("valueChange", {
    value,
    key: EControlType.NumberTextControl,
    config: props.config
  });
};
</script>

<style scoped lang="scss">
.number-text {
  :deep(.el-input-number) {
    margin: 0;
    width: 100%;
  }

  :deep(.el-input__inner) {
    text-align: left;
  }
}

.warning-text {
  :deep(.el-input__inner) {
    color: var(--el-color-warning);
    font-weight: 500;
  }
}
</style>
