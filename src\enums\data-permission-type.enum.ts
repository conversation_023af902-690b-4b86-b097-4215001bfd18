import { IOption } from "@/models";

export enum DataPermissionTypeEnum {
  /** 默认 */
  DEFAULT = 0,
  /** 部门管理 */
  DEPARTMENT_MANAGER = 1,

  /** 高层领导 */
  SENIOR_LEADER = 2,

  /** 技术支持 */
  TECHNICAL_SUPPORT = 3
}

export const DataPermissionTypeEnumMapDesc: Record<DataPermissionTypeEnum, string> = {
  [DataPermissionTypeEnum.DEFAULT]: "默认",
  [DataPermissionTypeEnum.DEPARTMENT_MANAGER]: "部门管理",
  [DataPermissionTypeEnum.SENIOR_LEADER]: "高层领导",
  [DataPermissionTypeEnum.TECHNICAL_SUPPORT]: "技术支持"
};

export const DataPermissionTypeEnumMapColor: Record<DataPermissionTypeEnum, string> = {
  [DataPermissionTypeEnum.DEFAULT]: "info",
  [DataPermissionTypeEnum.DEPARTMENT_MANAGER]: "success",
  [DataPermissionTypeEnum.SENIOR_LEADER]: "primary",
  [DataPermissionTypeEnum.TECHNICAL_SUPPORT]: "primary"
};

export const DataPermissionTypeEnumOption: Array<IOption> = [
  {
    label: DataPermissionTypeEnumMapDesc[DataPermissionTypeEnum.DEFAULT],
    value: DataPermissionTypeEnum.DEFAULT
  },
  {
    label: DataPermissionTypeEnumMapDesc[DataPermissionTypeEnum.DEPARTMENT_MANAGER],
    value: DataPermissionTypeEnum.DEPARTMENT_MANAGER
  },
  {
    label: DataPermissionTypeEnumMapDesc[DataPermissionTypeEnum.SENIOR_LEADER],
    value: DataPermissionTypeEnum.SENIOR_LEADER
  },
  {
    label: DataPermissionTypeEnumMapDesc[DataPermissionTypeEnum.TECHNICAL_SUPPORT],
    value: DataPermissionTypeEnum.TECHNICAL_SUPPORT
  }
];
