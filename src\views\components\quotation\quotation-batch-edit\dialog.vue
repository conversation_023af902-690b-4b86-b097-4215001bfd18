<template>
  <div>
    <el-dialog
      v-model="modelValue"
      title="批量编辑"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="default h-[70vh]"
    >
      <QuotationBatchEditForm ref="quotationBatchEditFormRef" />
      <template #footer>
        <div class="flex justify-between items-center">
          <span class="text-primary text-sm">已选数据：{{ props.ids?.length }}条</span>
          <div>
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useLoadingFn } from "@/utils/useLoadingFn";
import { computed, ref } from "vue";
import QuotationBatchEditForm from "./index.vue";
import { batchUpdateTaskItem } from "@/api/quotation/quotation-inquiry";
import { ElMessage } from "element-plus";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onSaveSuccess"): void;
  (e: "onCancel"): void;
}>();

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    ids: Array<string>;
  }>(),
  {
    modelValue: false,
    ids: () => []
  }
);
const quotationBatchEditFormRef = ref<InstanceType<typeof QuotationBatchEditForm>>();
const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

const loading = ref(false);
const handleSaveBtn = useLoadingFn(onSave, loading);

async function onSave() {
  const validResult = await quotationBatchEditFormRef.value.validateForm();
  if (!validResult) {
    return false;
  }

  const fieldsToUpdate = quotationBatchEditFormRef.value.getFormValue();
  await batchUpdateTaskItem({
    taskItemIds: props.ids,
    fieldsToUpdate: fieldsToUpdate ? JSON.stringify(fieldsToUpdate) : ""
  });

  emits("onSaveSuccess");
  modelValue.value = false;
  ElMessage.success("批量修改成功");
}

function closeDialog() {
  modelValue.value = false;
  emits("onCancel");
}
</script>

<style scoped lang="scss"></style>
