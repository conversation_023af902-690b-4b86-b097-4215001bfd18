@font-face {
  font-family: "iconfont"; /* Project id 4000813 */
  src: url("iconfont.woff2?t=1705304614746") format("woff2"), url("iconfont.woff?t=1705304614746") format("woff"),
    url("iconfont.ttf?t=1705304614746") format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-a-search2:before {
  content: "\e79d";
}

.icon-biaoshi:before {
  content: "\e79a";
}

.icon-low-voltage:before {
  content: "\e637";
}

.icon-high-voltage:before {
  content: "\e63c";
}

.icon-medium-voltage:before {
  content: "\e63f";
}

.icon-OPGW-OPPC:before {
  content: "\e642";
}

.icon-reactor:before {
  content: "\e635";
}

.icon-current-transformer:before {
  content: "\e636";
}

.icon-distribution-transformer:before {
  content: "\e638";
}

.icon-high-switch:before {
  content: "\e639";
}

.icon-ADSS:before {
  content: "\e63a";
}

.icon-arrester:before {
  content: "\e63b";
}

.icon-transformer:before {
  content: "\e63d";
}

.icon-mn-conductor:before {
  content: "\e63e";
}

.icon-dn-conductor:before {
  content: "\e640";
}

.icon-voltage-transformer:before {
  content: "\e641";
}

.icon-disconnector:before {
  content: "\e62f";
}

.icon-RMU:before {
  content: "\e630";
}

.icon-cable-terminal:before {
  content: "\e631";
}

.icon-distribution-box:before {
  content: "\e632";
}

.icon-armour-clamp:before {
  content: "\e633";
}

.icon-cable-adapter:before {
  content: "\e634";
}

.icon-list:before {
  content: "\e62e";
}

.icon-ellipsis:before {
  content: "\e62b";
}

.icon-cuo:before {
  content: "\e62c";
}

.icon-gou:before {
  content: "\e62d";
}

.icon-full-screen:before {
  content: "\e61f";
}

.icon-exit-full-screen:before {
  content: "\e629";
}

.icon-seal:before {
  content: "\e62a";
}

.icon-classify-fill:before {
  content: "\e61b";
}

.icon-menu:before {
  content: "\e628";
}

.icon-visible:before {
  content: "\e61d";
}

.icon-invisible1:before {
  content: "\e61e";
}

.icon-home-fill:before {
  content: "\e620";
}

.icon-basedata-fill:before {
  content: "\e621";
}

.icon-datacenter-fill:before {
  content: "\e622";
}

.icon-company-fill:before {
  content: "\e623";
}

.icon-orderdata-fill:before {
  content: "\e624";
}

.icon-setting:before {
  content: "\e625";
}

.icon-application-fill:before {
  content: "\e626";
}

.icon-pm-fill:before {
  content: "\e627";
}

.icon-fold-left:before {
  content: "\e615";
}

.icon-fold-right:before {
  content: "\e616";
}

.icon-user-fill:before {
  content: "\e617";
}

.icon-success-fill:before {
  content: "\e618";
}

.icon-fail-fill:before {
  content: "\e619";
}

.icon-warning-fill:before {
  content: "\e61a";
}

.icon-telephone:before {
  content: "\e61c";
}

.icon-search:before {
  content: "\e603";
}

.icon-refresh:before {
  content: "\e604";
}

.icon-plus:before {
  content: "\e605";
}

.icon-close:before {
  content: "\e606";
}

.icon-info:before {
  content: "\e607";
}

.icon-edit:before {
  content: "\e608";
}

.icon-global:before {
  content: "\e609";
}

.icon-time:before {
  content: "\e60a";
}

.icon-sync:before {
  content: "\e60b";
}

.icon-empty:before {
  content: "\e60c";
}

.icon-link:before {
  content: "\e60d";
}

.icon-unlink:before {
  content: "\e60e";
}

.icon-step-1:before {
  content: "\e60f";
}

.icon-step-2:before {
  content: "\e610";
}

.icon-step-3:before {
  content: "\e611";
}

.icon-company:before {
  content: "\e612";
}

.icon-lock:before {
  content: "\e613";
}

.icon-user:before {
  content: "\e614";
}

.icon-arrow-right:before {
  content: "\e600";
}

.icon-arrow-down:before {
  content: "\e601";
}

.icon-arrow-up:before {
  content: "\e602";
}
