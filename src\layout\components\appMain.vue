<script setup lang="ts">
import { useGlobal } from "@pureadmin/utils";
import { h, computed, Transition, defineComponent } from "vue";
import { usePermissionStoreHook } from "@/store/modules/permission";
import Title from "./title/title.vue";

const props = defineProps({
  fixedHeader: Boolean
});

const { $storage, $config } = useGlobal<GlobalPropertiesApi>();

const keepAlive = computed(() => {
  return $config?.KeepAlive;
});

const hideTabs = computed(() => {
  return $storage?.configure.hideTabs;
});

const layout = computed(() => {
  return $storage?.layout.layout === "vertical";
});

const getSectionStyle = computed(() => {
  return [
    hideTabs.value && layout ? "padding-top: 3rem;" : "",
    !hideTabs.value && layout ? "padding-top: 85px;" : "",
    hideTabs.value && !layout.value ? "padding-top: 3rem" : "",
    !hideTabs.value && !layout.value ? "padding-top: 85px;" : "",
    props.fixedHeader ? "" : "padding-top: 0;"
  ];
});

const transitionMain = defineComponent({
  render() {
    return h(
      Transition,
      {
        mode: "out-in",
        appear: true
      },
      {
        default: () => [this.$slots.default()]
      }
    );
  },
  props: {
    route: {
      type: undefined,
      required: true
    }
  }
});

const ComponentWithTitle = (component: any, route: any) => {
  return {
    name: route?.name,
    render() {
      return h("div", { class: "flex flex-col h-full" }, [
        h(Title),
        h("div", { class: "flex-1 flex flex-col overflow-hidden" }, h(component, { key: route?.name }))
      ]);
    }
  };
};
</script>

<template>
  <section :class="[props.fixedHeader ? 'app-main' : 'app-main-nofixed-header']" :style="getSectionStyle">
    <router-view>
      <template #default="{ Component, route }">
        <transitionMain :route="route">
          <keep-alive v-if="keepAlive" :include="usePermissionStoreHook().cachePageList">
            <component :is="ComponentWithTitle(Component, route)" :key="route.name" />
          </keep-alive>
          <ComponentWithTitle v-else :component="Component" :route="route" :key="route.name" />
        </transitionMain>
      </template>
    </router-view>
  </section>
</template>

<style scoped>
.app-main {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.app-main-nofixed-header {
  width: 100%;
  min-height: 100vh;
  position: relative;
}
</style>
