<template>
  <div class="flex flex-col h-[70vh]">
    <div>
      <TitleBar class="mb-2 ml-5" title="基础信息" />
      <el-form ref="formRef" :model="form" :rules="rules" label-position="left">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item label="产品分类" prop="productCategory">
              <el-input v-model="form.productCategory" clearable placeholder="请输入产品分类" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label=" 物料编号" prop="productCode">
              <el-input v-model="form.productCode" clearable placeholder="请输入物料编号" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="型号" prop="model">
              <el-input v-model="form.model" clearable placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="规格" prop="specification">
              <el-input v-model="form.specification" clearable placeholder="请输入规格" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="电压等级" prop="voltage">
              <el-input v-model="form.voltage" clearable placeholder="请输入电压等级" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="计量单位" prop="productUnit">
              <SelectProductUnit
                class="!w-full"
                v-model="form.productUnit"
                placeholder="请选择计量单位"
                :disabled="!!form.id"
              >
              </SelectProductUnit>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
    </div>
    <div class="flex-1 flex flex-col overflow-hidden">
      <div class="flex items-center justify-between px-5">
        <TitleBar class="mb-2" title="材料定额" />
        <el-button :icon="Plus" type="primary" @click="onAddBomMaterialQuotaItem()">增加行</el-button>
      </div>
      <div class="bg-bg_color p-5 flex flex-col flex-1 overflow-hidden relative">
        <PureTable
          class="flex-1 overflow-hidden pagination"
          row-key="id"
          :data="form.bomMaterialQuotaList"
          :columns="columns"
          showOverflowTooltip
        >
          <template #rawMaterialInformationName="{ index, row }">
            <el-input
              v-model="row.bomRaw.rawName"
              placeholder="请选择"
              :suffix-icon="Search"
              readonly
              @click="onShowSelectRawMaterialInformationDialog(index)"
            />
          </template>
          <template #materialType="{ row }">
            <el-checkbox v-model="row.materialTypeBoolean" label="主材" @change="onChangeMaterialType(row)" />
          </template>
          <template #quantity="{ row }">
            <el-input-number v-model="row.quantity" :min="0" controls-position="right" />
          </template>
          <template #productUnit="{ row }">
            <SelectProductUnit class="!w-full" v-model="row.productUnit" placeholder="请选择计量单位" />
          </template>
          <template #operation="{ index }">
            <!-- <el-button link type="primary">保存</el-button>
            <el-button link type="info">取消</el-button> -->
            <el-button link type="warning" @click="onDelete(index)">删除</el-button>
          </template>
          <template #empty>
            <CxEmptyData />
          </template>
        </PureTable>
      </div>
    </div>
    <SelectRawMaterialInformationDialog
      v-model="rawMaterialInformationDialogVisible"
      @onSelect="handleOnSelectRawMaterialInformation($event)"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IBomInformationForm, IBomMaterialQuotaFormExt, IRawMaterialInformation } from "@/models";
import TitleBar from "@/components/TitleBar";
import { Plus, Search } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import SelectRawMaterialInformationDialog from "@/views/components/raw-material-information/dialog.vue";
import SelectProductUnit from "@/views/components/select-product-unit/index.vue";
import { MaterialTypeEnum } from "@/enums";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
const { columns } = useColumns();
let editIndex = 0;
const form = reactive<IBomInformationForm>({ bomMaterialQuotaList: [] });
let deleteBomMaterialQuotaList: Array<IBomMaterialQuotaFormExt> = [];
const formRef = ref<FormInstance>();
const rawMaterialInformationDialogVisible = ref();
const rules: FormRules = {
  productCategory: [{ required: true, trigger: "change", message: "产品分类不能为空" }],
  productUnit: [{ required: true, trigger: "change", message: "计量单位不能为空" }]
};

const onAddBomMaterialQuotaItem = () => {
  form.bomMaterialQuotaList.push({ bomRaw: {}, materialType: MaterialTypeEnum.AUXILIARY });
};

const handleOnSelectRawMaterialInformation = (rawMaterialInformation: IRawMaterialInformation) => {
  form.bomMaterialQuotaList[editIndex].bomRaw = rawMaterialInformation;
  form.bomMaterialQuotaList[editIndex].bomRawId = rawMaterialInformation.id;
  form.bomMaterialQuotaList[editIndex].productUnit = rawMaterialInformation?.latestCost?.productUnit;
};

const onChangeMaterialType = (data: IBomMaterialQuotaFormExt) => {
  data.materialType = data.materialTypeBoolean ? MaterialTypeEnum.AUXILIARY : MaterialTypeEnum.MAIN;
};

const onDelete = (index: number) => {
  const deleteItem = form.bomMaterialQuotaList.splice(index, 1);
  deleteBomMaterialQuotaList.push(...deleteItem);
};

const onShowSelectRawMaterialInformationDialog = (index: number) => {
  editIndex = index;
  rawMaterialInformationDialogVisible.value = true;
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(data: IBomInformationForm) {
  if (Array.isArray(data.bomMaterialQuotaList) && data.bomMaterialQuotaList.length > 0) {
    data.bomMaterialQuotaList = data.bomMaterialQuotaList.map(x => ({
      ...x,
      materialTypeBoolean: x.materialType === MaterialTypeEnum.AUXILIARY
    }));
  }
  Object.assign(form, data);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  const formValue = Object.assign({}, form);
  if (Array.isArray(deleteBomMaterialQuotaList) && deleteBomMaterialQuotaList.length > 0) {
    deleteBomMaterialQuotaList = deleteBomMaterialQuotaList.map(x => ({ ...x, deleted: true }));
    form.bomMaterialQuotaList.push(...deleteBomMaterialQuotaList);
  }
  return formValue;
}
</script>
