<template>
  <div class="chart-container">
    <v-chart class="chart" :option="chartOption" autoresize />
  </div>
</template>

<script setup lang="ts">
import { IQuotationAnalysis } from "@/models";
import { <PERSON><PERSON><PERSON>, LineChart } from "echarts/charts";
import { GridComponent, LegendComponent, TooltipComponent } from "echarts/components";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { ref } from "vue";
import VChart from "vue-echarts";

// 注册必要的组件
use([<PERSON>vas<PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, LineChart, GridComponent, TooltipComponent, LegendComponent]);

defineExpose({ refreshChart });
const moneyDisplayMode = ref();
const categoryRef = ref<Array<string>>([]);
const chartOption = ref({
  backgroundColor: "#fff",
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross",
      crossStyle: {
        color: "#999"
      }
    }
  },
  grid: {
    left: "3%",
    right: "3%",
    bottom: "12%",
    top: "8%",
    containLabel: true
  },
  legend: {
    data: ["报价单数量", "报价金额"],
    itemGap: 30,
    bottom: 0,
    textStyle: {
      fontSize: 12,
      color: "#606266"
    }
  },
  xAxis: {
    type: "category",
    data: categoryRef.value, //["3月17日", "3月18日", "3月19日", "3月20日", "3月21日", "3月22日"],
    axisPointer: {
      type: "shadow"
    },
    axisLine: {
      lineStyle: {
        color: "#606266"
      }
    },
    axisLabel: {
      color: "#909399",
      fontSize: 12
    },
    axisTick: {
      show: false
    }
  },
  yAxis: [
    {
      type: "value",
      name: "",
      min: 0,
      position: "left",
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: "#909399",
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: "#EBEEF5"
        }
      }
    },
    {
      type: "value",
      name: "",
      // min: 0,
      // max: 300,
      // interval: 50,
      position: "right",
      axisLabel: {
        formatter: function (value) {
          return value + moneyDisplayMode.value;
        },
        color: "#909399",
        fontSize: 12
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    }
  ],
  series: [
    {
      name: "报价单数量",
      type: "bar",
      data: [],
      barWidth: 25,
      itemStyle: {
        color: {
          type: "linear", // 线性渐变
          x: 0, // 渐变起始点的 x 坐标
          y: 0, // 渐变起始点的 y 坐标
          x2: 0, // 渐变结束点的 x 坐标
          y2: 1, // 渐变结束点的 y 坐标
          colorStops: [
            {
              offset: 0,
              color: "#64D9BA" // 渐变0%的颜色
            },
            {
              offset: 1,
              color: "#2DAAB3" // 渐变100%的颜色
            }
          ],
          global: false // 缺省为 false
        }
      }
    },
    {
      name: "报价金额",
      type: "line",
      yAxisIndex: 1,
      data: [],
      symbol: "circle",
      symbolSize: 8,
      itemStyle: {
        color: "#FAECD8",
        borderColor: "#FF9F43"
      },
      lineStyle: {
        width: 2,
        color: "#E6A23C"
      }
    }
  ]
});

function refreshChart(data: Array<IQuotationAnalysis>, _moneyDisplayMode: string) {
  chartOption.value.xAxis.data = data.map(x => x.quotationAnalysisDate);
  moneyDisplayMode.value = _moneyDisplayMode;
  chartOption.value.series[0].data = data.map(x => parseFloat(x.quotationAnalysisCount));
  chartOption.value.series[1].data = data.map(x => parseFloat(x.quotationAnalysisAmount));
}
</script>

<style scoped>
.chart-container {
  width: 100%;
}

.chart {
  height: 100%;
  width: 100%;
}
</style>
