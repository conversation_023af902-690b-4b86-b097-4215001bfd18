import { IEmployeeReq } from "@/models";
import { useEmployeeStore, useRoleStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { reactive } from "vue";

export const useEmployeeHook = () => {
  const employeeStore = useEmployeeStore();
  const roleStore = useRoleStore();
  const employeeState = reactive<{
    modalVisible: boolean;
  }>({
    modalVisible: false
  });

  const queryRoleEmployee = (params: IEmployeeReq) => {
    employeeStore.queryEmployee(params);
  };

  const setEmployeeModalVisible = (visible: boolean) => {
    employeeState.modalVisible = visible;
  };

  const deleteRoleEmployee = async (roleId: string, userId: string) => {
    await roleStore.removeEmployeeRole(roleId, userId);
    ElMessage.success("员工移除成功");
  };

  const bindEmployeeToRole = async (roleId: string, userIds: Array<string>) => {
    await roleStore.bindEmployeeToRole(roleId, userIds);
    ElMessage.success("员工绑定成功");
    employeeState.modalVisible = false;
  };

  const onCloseSelectEmployeeModal = () => {
    employeeState.modalVisible = false;
  };

  const onBindEmployeeToRoleModalVis = () => {
    employeeState.modalVisible = true;
  };

  return {
    employeeState,
    queryRoleEmployee,
    deleteRoleEmployee,
    setEmployeeModalVisible,
    bindEmployeeToRole,
    onCloseSelectEmployeeModal,
    onBindEmployeeToRoleModalVis
  };
};
