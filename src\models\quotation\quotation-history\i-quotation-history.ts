import { InquiryTypeEnum, ParseStatusEnum, TaskStatusEnum } from "@/enums";
import { IBase } from "@/models";

export interface IQuotationHistory extends IBase {
  /**
   * 联系人
   */
  contactPerson?: string;
  /**
   * 铜价
   */
  copperPrice?: number;
  /**
   * 询价单附件id
   */
  inquiryAttachmentId?: number;
  /**
   * 询价单附件名
   */
  inquiryAttachmentName?: string;
  /**
   * 询价单附件url
   */
  inquiryAttachmentUrl?: string;
  /**
   * 询价单位id
   */
  inquiryOrgId?: string;

  /** 询价单位 */
  inquiryOrgName?: string;
  /**
   * 询价时间
   */
  inquiryTime?: Date;
  /**
   * 操作人id
   */
  operatorId?: number;
  /**
   * 已解析数量
   */
  parsedInquiryCount?: number;
  /**
   * 解析完成时间
   */
  parseFinishTime?: Date;
  /**
   * 解析进度
   */
  parseSchedule?: number;
  /**
   * 解析开始时间
   */
  parseStartTime?: Date;
  /**
   * 解析状态
   */
  parseStatus?: ParseStatusEnum;
  /**
   * 耗时
   */
  parseTimeCost?: number;
  /**
   * 产品单价版本id
   */
  productPriceVersionId?: string;
  /**
   * 报价金额
   */
  quotationAmount?: number;
  /**
   * 报价单附件id
   */
  quotationAttachmentId?: string;

  /*** 报价单名称 */
  quotationAttachmentName?: string;
  /**
   * 报价批次号
   */
  quotationBatchNo?: string;
  /**
   * 参考
   */
  reference?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 主题
   */
  subject?: string;
  /**
   * 任务状态
   */
  taskStatus?: TaskStatusEnum;
  /**
   * 解析总数
   */
  totalInquiryParseCount?: number;

  /** 询价单类型 */
  inquiryType?: InquiryTypeEnum;

  /**
   * 询价单内容
   */
  inquiryTextContent?: string;

  /**  token用量 */
  parseTokenUsage?: number;
}
