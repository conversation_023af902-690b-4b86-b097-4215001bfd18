<template>
  <div class="overflow-hidden w-full flex flex-col flex-1 h-[70vh]">
    <div class="bg-bg_color flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="型号">
          <ElInput class="!w-[200px]" clearable v-model="state.params.unit" placeholder="请输入型号" />
        </ElFormItem>
        <ElFormItem label="规格">
          <ElInput class="!w-[200px]" clearable v-model="state.params.unit" placeholder="请输入规格" />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <div class="btn">
        <el-button :icon="Download">导出</el-button>
        <el-button :icon="FolderOpened" type="primary">导入</el-button>
        <el-button :icon="Plus" type="primary">新增</el-button>
      </div>
    </div>

    <div class="bg-bg_color flex flex-col flex-1 overflow-hidden relative">
      <div class="flex items-center justify-end mb-[10px]">
        <span class="text-base mr-2">版本号:</span>
        <el-tag type="info">20250315-1423</el-tag>
      </div>
      <div class="flex gap-8 flex-1 overflow-hidden">
        <PureTable
          class="flex-1 overflow-hidden pagination"
          row-key="id"
          :data="state.list"
          :columns="columns"
          size="small"
          :loading="loading"
          showOverflowTooltip
          v-model:pagination="pagination"
          @page-current-change="onPageCurrentChange"
          @page-size-change="onPageSizeChange"
        >
          <template #operation="data">
            <div>
              <AddEditInquiryOrganizationDialog mode="edit" :id="data.row.id">
                <template #trigger="{ openDialog }">
                  <el-button link type="primary" @click="openDialog">编辑</el-button>
                </template>
              </AddEditInquiryOrganizationDialog>
              <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除 </ElButton>
            </div>
          </template>
          <template #empty>
            <CxEmptyData />
          </template>
        </PureTable>
        <div class="specification-container">
          <div class="title">待添加型号规格</div>
          <el-scrollbar>
            <div class="specification">
              <div class="item" v-for="item in 40" :key="item">
                <span>RVV==RVVP RVVP 5*1.5</span>
                <el-icon class="delete_icon"><Delete /></el-icon>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="inquiry-unit">
import { onMounted, ref, reactive } from "vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { Plus, Download, FolderOpened, Delete } from "@element-plus/icons-vue";
import { queryInquiryOrganization, deleteInquiryOrganizationById } from "@/api/basic/inquiry-organization";
import { IInquiryOrganization } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IInquiryOrganization>;
  params: { [key: string]: string };
}>({
  list: [],
  params: {}
});

onMounted(() => {
  requestList();
});

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

// const onEdit = (id: string) => {};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteInquiryOrganizationById(id);
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryInquiryOrganization(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>

<style scoped lang="scss">
.specification-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
  padding: 20px;

  .title {
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
    letter-spacing: 0;
    color: #303133;
    padding-bottom: 10px;
  }

  .specification {
    display: flex;
    flex-direction: column;
    gap: 11px;
    width: 414px;
    padding-bottom: 20px;

    .item {
      border: 1px solid #ebeef5;
      background: #ffffff;
      padding: 14px 20px;
      font-size: 14px;
      line-height: 16px;
      letter-spacing: 0;
      color: #303339;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .delete_icon {
        display: none;
      }

      &:hover {
        border: 1px solid #b2e9d6;
        background: #e5f8f1;

        .delete_icon {
          display: block;
          color: var(--el-color-primary);
          cursor: pointer;
        }
      }
    }
  }
}
</style>
