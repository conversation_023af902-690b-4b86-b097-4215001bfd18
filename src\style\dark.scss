@use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;

/* 暗黑模式适配 */
html.dark {
  /* 自定义深色背景颜色 */
  // --el-bg-color: #020409;
  $border-style: #303030;
  $color-white: #fff;

  .navbar,
  .tags-view,
  .contextmenu,
  .sidebar-container,
  .horizontal-header,
  .sidebar-logo-container,
  .horizontal-header .el-sub-menu__title,
  .horizontal-header .submenu-title-noDropdown {
    background: var(--el-bg-color) !important;
  }

  .app-main {
    background: #020409 !important;
  }

  .frame {
    filter: invert(0.9) hue-rotate(180deg);
  }

  /* 标签页 */
  .tags-view {
    .arrow-left,
    .arrow-right {
      box-shadow: none;
      border-right: 1px solid $border-style;
    }

    .arrow-right {
      border-left: 1px solid $border-style;
    }
  }

  /* 项目配置面板 */
  .right-panel-items {
    .el-divider__text {
      --el-bg-color: var(--el-bg-color);
    }
    .el-divider--horizontal {
      border-top: none;
    }
  }

  /* element-plus */
  .el-table__cell {
    background: var(--el-bg-color);
  }
  .el-card {
    --el-card-bg-color: var(--el-bg-color);
    // border: none !important;
  }
  .el-backtop {
    --el-backtop-bg-color: var(--el-color-primary-light-9);
    --el-backtop-hover-bg-color: var(--el-color-primary);
  }
  .el-dropdown-menu__item:not(.is-disabled):hover {
    background: transparent;
  }

  /* 全局覆盖element-plus的el-dialog、el-drawer、el-message-box、el-notification组件右上角关闭图标的样式，表现更鲜明 */
  .el-icon {
    &.el-dialog__close,
    &.el-drawer__close,
    &.el-message-box__close,
    &.el-notification__closeBtn {
      &:hover {
        color: rgba(255, 255, 255, 0.85) !important;
        background-color: rgba(255, 255, 255, 0.12);
      }
    }
  }

  /* 克隆并自定义 ElMessage 样式，不会影响 ElMessage 原本样式，在 src/utils/message.ts 中调用自定义样式 ElMessage 方法即可，非暗黑模式在 src/style/element-plus.scss 文件进行了适配 */
  .pure-message {
    background-image: initial !important;
    background-color: rgb(36, 37, 37) !important;
    box-shadow: rgb(13 13 13 / 12%) 0px 3px 6px -4px, rgb(13 13 13 / 8%) 0px 6px 16px 0px,
      rgb(13 13 13 / 5%) 0px 9px 28px 8px !important;

    & .el-message__content {
      color: $color-white !important;
      pointer-events: all !important;
      background-image: initial !important;
    }

    & .el-message__closeBtn {
      &:hover {
        color: rgba(255, 255, 255, 0.85);
        background-color: rgba(255, 255, 255, 0.12);
      }
    }
  }
}
