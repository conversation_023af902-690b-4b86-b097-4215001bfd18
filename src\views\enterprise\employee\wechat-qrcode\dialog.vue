<template>
  <div>
    <el-dialog
      v-model="modelValue"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :center="true"
      align-center
      width="20%"
    >
      <WeChatQRCode
        :employeeId="props.employeeId"
        :wechatUnionId="props.wechatUnionId"
        :employeeName="props.employeeName"
      />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import WeChatQRCode from "./index.vue";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onBindSuccess"): void;
  (e: "onBindCancel"): void;
}>();

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    employeeId?: string;
    wechatUnionId?: string;
    employeeName?: string;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

function closeDialog() {
  modelValue.value = false;
  emits("onBindCancel");
}
</script>

<style scoped></style>
