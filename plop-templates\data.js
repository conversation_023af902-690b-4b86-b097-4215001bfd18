module.exports.data = {
  desc: "BOM信息",
  fields: {
    productCategory: {
      type: "string",
      desc: "产品分类",
      column: true,
      form: true,
      formFieldConfig: {
        label: "产品分类",
        placeholder: "请输入选择分类",
        type: "select",
        rule: { required: true, trigger: "change", message: "产品分类不能为空" }
      },
      search: true,
      searchFieldConfig: {
        label: "产品分类",
        placeholder: "请选择产品分类",
        type: "string"
      }
    },
    productCode: {
      type: "string",
      desc: "物料编号",
      column: true,
      form: false,
      formFieldConfig: {
        label: "物料编号",
        placeholder: "请输入物料编号",
        type: "string",
        rule: { required: false, trigger: "change", message: "物料编号不能为空" }
      },
      search: true,
      searchFieldConfig: {
        label: "物料编号",
        placeholder: "请输入物料编号",
        type: "string"
      }
    },
    productName: {
      type: "string",
      desc: "产品名称",
      column: true,
      form: true,
      formFieldConfig: {
        label: "产品名称",
        placeholder: "请输入产品名称",
        type: "string",
        rule: { required: true, trigger: "change", message: "产品名称不能为空" }
      },
      search: true,
      searchFieldConfig: {
        label: "产品名称",
        placeholder: "请输入产品名称",
        type: "string"
      }
    },
    model: {
      type: "string",
      desc: "型号",
      column: true,
      form: false,
      formFieldConfig: {
        label: "型号",
        placeholder: "请输入型号",
        type: "string",
        rule: { required: false, trigger: "change", message: "型号不能为空" }
      },
      search: true,
      searchFieldConfig: {
        label: "型号",
        placeholder: "请输入型号",
        type: "string"
      }
    },
    specification: {
      type: "string",
      desc: "规格",
      column: true,
      form: false,
      formFieldConfig: {
        label: "规格",
        placeholder: "请输入规格",
        type: "string",
        rule: { required: false, trigger: "change", message: "规格不能为空" }
      },
      search: true,
      searchFieldConfig: {
        label: "规格",
        placeholder: "请输入规格",
        type: "string"
      }
    },
    voltage: {
      type: "string",
      desc: "电压等级",
      column: true,
      form: false,
      formFieldConfig: {
        label: "电压等级",
        placeholder: "请输入电压等级",
        type: "string",
        rule: { required: false, trigger: "change", message: "电压等级不能为空" }
      },
      search: true,
      searchFieldConfig: {
        label: "电压等级",
        placeholder: "请输入电压等级",
        type: "string"
      }
    },
    productUnit: {
      type: "string",
      desc: "计量单位",
      column: true,
      form: true,
      formFieldConfig: {
        label: "计量单位",
        placeholder: "请输入计量单位",
        type: "string",
        rule: { required: true, trigger: "change", message: "计量单位不能为空" }
      },
      search: false,
      searchFieldConfig: {
        label: "计量单位",
        placeholder: "请输入计量单位",
        type: "string"
      }
    },
    source: {
      type: "string",
      desc: "数据来源",
      column: true,
      form: false,
      formFieldConfig: {
        label: "数据来源",
        placeholder: "请输入数据来源",
        type: "string",
        rule: { required: false, trigger: "change", message: "数据来源不能为空" }
      },
      search: true,
      searchFieldConfig: {
        label: "数据来源",
        placeholder: "请输入数据来源",
        type: "string"
      }
    }
  },
  /**  */
  formIsDialog: true,
  // 表单域标签的位置， 当设置为 left 或 right
  labelPosition: "left",
  needSearch: true,
  needCreateOrEdit: true,
  needDelete: true,
  formLineNumber: 1,
  viewPathPrefix: "src/views/quote-basic-data",
  modelPathPrefix: "src/models/quote-basic-data",
  apiPathPrefix: "src/api/quote-basic-data"
};
