type Datum = Record<string, any>;

export function useRowspan<T extends Datum>(
  data: Array<T>,
  field: keyof T
): Array<T & { rowSpan?: number; colSpan?: 1 }> {
  if (!Array.isArray(data) || !field) {
    return data;
  }
  const rowSpanMap: Record<string, number> = {};
  data.forEach(datum => {
    const value = datum[field];
    rowSpanMap[value] = (rowSpanMap[value] || 0) + 1;
  });
  return data.map(datum => {
    const value = datum[field];
    if (!value) {
      return { ...datum, rowSpan: 1, colSpan: 1 };
    }
    const rowSpan: number = rowSpanMap[value];
    rowSpanMap[value] = 0;
    return { ...datum, rowSpan, colSpan: 1 };
  });
}
