import { TenantStatusEnum } from "@/enums";
import { IPagingReq } from "@/models";

export interface IEmployeeReq extends IPagingReq {
  username?: string;
  mobile?: string;
  status?: TenantStatusEnum;
  createTime?: Date;
  deptId?: string;
  /**
   * 是否排除 部门筛选字段对应的人员
   * true: 排除
   */
  excludeDept?: boolean;
  keyWords?: string;

  roleId?: string;
  /**
   * 是否排除 角色筛选字段对应的人员
   * true: 排除
   */
  excludeRole?: boolean;
}
