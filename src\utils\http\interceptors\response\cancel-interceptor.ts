import Axios, { AxiosInstance } from "axios";
import { abortControllerManager } from "../abort-controller-manager";

export function cancelInterceptor(instance: AxiosInstance): number {
  return instance.interceptors.response.use(
    response => {
      abortControllerManager.delete(response.request.duplicateKey);
      return response;
    },
    error => {
      error.isCancelRequest = Axios.isCancel(error);
      if (!error.isCancelRequest) {
        abortControllerManager.delete(error.request.duplicateKey);
      }
      return Promise.reject(error);
    }
  );
}
