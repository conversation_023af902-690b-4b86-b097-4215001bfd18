import { IListResponse, OperationalStatistics } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../../util";

export const queryOperationalStatistics = () => {
  return http.post<void, IListResponse<OperationalStatistics>>(
    withApiGateway("admin-api/business/dashboard/getOperationalStatistics")
  );
};

export const queryOperationalStatisticsList = () => {
  return http.post<void, IListResponse<OperationalStatistics>>(
    withApiGateway("admin-api/business/dashboard/getOperationalStatisticsList")
  );
};

export const operationalStatisticsExport = () => {
  const url: string = withApiGateway("admin-api/business/dashboard/operationalStatisticsExport");
  return http.get<void, Blob>(
    url,
    {},
    {
      responseType: "blob"
    }
  );
};
