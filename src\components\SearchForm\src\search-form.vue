<template>
  <div class="search-header">
    <div class="header-left">
      <div class="flex">
        <div class="flex flex-wrap gap-[10px] bg-bg_color rounded">
          <el-input
            class="!w-[600px]"
            :prefix-icon="Search"
            v-model="keywords"
            clearable
            :placeholder="placeholder"
            @keyup.enter="search"
            @clear="clearKeywords"
          />
          <slot name="action" />
        </div>
        <div class="flex items-center gap-[10px] ml-3">
          <el-button :size="size" type="primary" @click="search()" ref="searchBtnRef">{{
            $t("searchForm.search")
          }}</el-button>
          <el-button :size="size" @click="reset">{{ $t("searchForm.reset") }}</el-button>
          <el-button :size="size" link type="primary" @click="toggleAdvancedSearch" v-if="hasAdvanced">
            {{ $t("searchForm.advanced") }}
            <IconifyIconOffline
              :icon="ArrowDown"
              class="ml-[8px] transition-transform"
              :class="{ 'rotate-180': showAdvancedSearch }"
            />
          </el-button>
        </div>
      </div>
      <SlideTransition :collapse="!showAdvancedSearch">
        <el-form
          ref="formRef"
          :model="form"
          :rules="props.rules"
          :label-position="props.labelPosition"
          class="bg-bg_color rounded gap-x-11 flex flex-wrap"
          @submit.prevent="search"
        >
          <div v-for="item in props.searchItems" :key="item.key" :class="item.full ? 'w-full' : ''">
            <el-form-item v-bind="getFormItemProps(item)" :prop="item.key">
              <component :is="item.component" v-bind="item.componentProps" v-model="form[item.key]" />
            </el-form-item>
          </div>
          <!-- for trigger submit event -->
          <button type="submit" class="hidden" />
        </el-form>
      </SlideTransition>
    </div>
    <div class="header-right">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import SlideTransition from "./utils/slide-transition";
import { computed, onMounted, provide, ref } from "vue";
import ArrowDown from "@iconify-icons/ep/arrow-down";
import { IKeywordField, ISearchItem, LabelPosition, SizeType } from "./types";
import { ButtonInstance, FormInstance, FormItemProps, FormRules } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { clone } from "@pureadmin/utils";
import { searchFormToken } from "./tokens";

const props = withDefaults(
  defineProps<{
    searchForm: Record<string, unknown>;
    keywordFields: Array<IKeywordField>;
    keywordFilterKey: string;
    searchItems?: Array<ISearchItem>;
    rules?: FormRules;
    placeholder?: string;
    labelPosition?: LabelPosition;
    size?: SizeType;
  }>(),
  {
    placeholder: "searchForm.placeholder",
    labelPosition: LabelPosition.RIGHT,
    size: SizeType.LARGE
  }
);
const emits = defineEmits<{
  (e: "search", advanced: boolean): void;
  (e: "reset"): void;
}>();

let initialValue: Record<string, unknown>;
const formRef = ref<FormInstance>();
const searchBtnRef = ref<ButtonInstance>();
const showAdvancedSearch = ref(false);
const form = computed(() => props.searchForm);
const hasAdvanced = computed(() => !!props.searchItems?.length);

const placeholder = computed(() => {
  if (props.placeholder) {
    return props.placeholder;
  }
  return "请输入" + props.keywordFields.map(item => item.title).join("/");
});

/** 搜索关键词 */
const keywords = ref("");

provide(searchFormToken, {
  placeholder: props.placeholder,
  keywordFields: props.keywordFields,
  searchBtnRef: searchBtnRef,
  searchCb: search
});

onMounted(() => {
  initialValue = clone(props.searchForm);
});

function getFormItemProps(item: ISearchItem): Partial<FormItemProps> {
  const { key: _key, full: _full, component: _component, componentProps: _componentProps, ...props } = item;
  return props;
}

function toggleAdvancedSearch(): void {
  showAdvancedSearch.value = !showAdvancedSearch.value;
}

async function reset(): Promise<void> {
  keywords.value = "";
  const instance: FormInstance = formRef.value;
  if (instance) {
    resetAdvancedValue();
    if (!(await instance.validate(() => {}))) {
      return;
    }
  }
  setKeywordParams();
  emits("search", !!instance);
  emits("reset");
}

async function search(): Promise<void> {
  const instance: FormInstance = formRef.value;
  if (instance && !(await instance.validate(() => {}))) {
    return;
  }
  setKeywordParams();
  emits("search", !!instance);
}

function clearKeywords(): void {
  keywords.value = "";
  search();
}

function setKeywordParams(): void {
  const keywordConfig = props.keywordFields;
  if (!Array.isArray(keywordConfig) || !keywordConfig.length) {
    return;
  }
  // 清空orFilter
  if (!keywords.value) {
    delete form.value[props.keywordFilterKey];
    return;
  }
  // orFilter赋值
  form.value[props.keywordFilterKey] = keywordConfig.map(({ key }) => ({
    [key]: keywords.value
  }));
}

function resetAdvancedValue(): void {
  props.searchItems.forEach(item => {
    const key: string = item.key;
    form.value[key] = initialValue[key];
  });
}
</script>

<style lang="scss" scoped>
.search-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .header-left {
    flex: 1;
  }
}

.el-button {
  margin: 0;
}

:deep(.el-form-item) {
  margin-bottom: 0;
  margin-top: 16px;
}

:deep(.el-form-item__label) {
  width: 110px;
}
</style>
