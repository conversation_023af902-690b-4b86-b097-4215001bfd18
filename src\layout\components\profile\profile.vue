<template>
  <el-dropdown trigger="click">
    <div class="el-dropdown-link navbar-bg-hover select-none flex-c gap-1.5">
      <Avatar :avatar="avatar" :word="wordAvatar" />
      <p v-if="nickname" class="dark:text-white ml-">{{ nickname }}</p>
    </div>
    <template #dropdown>
      <div class="el-dropdown-box">
        <el-dropdown-menu class="logout">
          <el-dropdown-item @click="onEditProfile()">
            <IconifyIconOffline :icon="UserSettingLine" style="margin: 5px" />
            {{ t("buttons.hsUserSetting") }}
          </el-dropdown-item>
          <el-dropdown-item @click="logout()">
            <IconifyIconOffline :icon="LogoutCircleRLine" style="margin: 5px" />
            {{ t("buttons.hsLoginOut") }}
          </el-dropdown-item>
        </el-dropdown-menu>
        <el-divider />
        <div class="version">{{ versionStore.getVersion }}</div>
      </div>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import UserSettingLine from "@iconify-icons/ri/user-settings-line";
import LogoutCircleRLine from "@iconify-icons/ri/logout-circle-r-line";
import { useUserStoreHook } from "@/store/modules/user";
import { useTranslationLang } from "@/layout/hooks/useTranslationLang";
import { useRouter } from "vue-router";
import Avatar from "@/components/Avatar/index.vue";
import { ref, watchEffect } from "vue";
import { useVersionStore } from "@/store/modules";
import { checkPasswordSafety } from "@/router/utils";

const router = useRouter();
const userStoreHook = useUserStoreHook();
const versionStore = useVersionStore();
const wordAvatar = ref<string>();
const avatar = ref<string>();
const nickname = ref<string>();
watchEffect(async () => {
  const profile = await userStoreHook.getProfile;
  checkPasswordSafety();
  wordAvatar.value = profile?.nickname?.charAt(0);
  avatar.value = profile?.avatarInfo?.url;
  nickname.value = profile?.nickname;
});

const { t } = useTranslationLang();

function logout() {
  useUserStoreHook().logOut();
}

const onEditProfile = () => {
  router.push("/account-profile");
};
</script>

<style scoped>
.el-dropdown-link {
  height: 48px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  cursor: pointer;
  color: #000000d9;
}

.el-dropdown-box {
  padding: 5px 10px 15px;
}

::v-deep(.el-dropdown-menu__item) {
  padding-left: 5px;
}

.el-divider {
  margin: 0 0 10px;
}

.version {
  padding-left: 12px;
}
</style>
