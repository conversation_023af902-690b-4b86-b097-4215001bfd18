import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

export enum AccountRegisterReviewStatusEnum {
  /**
   * 待审核
   */
  PendingReview = 0,

  /**
   * 审核通过
   */
  ReviewPassed = 1,

  /**
   * 审核不通过
   */
  ReviewReject = 2
}

export const AccountRegisterReviewStatusEnumMapDesc: Record<AccountRegisterReviewStatusEnum, string> = {
  [AccountRegisterReviewStatusEnum.PendingReview]: "待审核",
  [AccountRegisterReviewStatusEnum.ReviewPassed]: "审核通过",
  [AccountRegisterReviewStatusEnum.ReviewReject]: "审核拒绝"
};

export const AccountRegisterReviewStatusEnumMapColor: Record<AccountRegisterReviewStatusEnum, string> = {
  [AccountRegisterReviewStatusEnum.PendingReview]: "info",
  [AccountRegisterReviewStatusEnum.ReviewPassed]: "success",
  [AccountRegisterReviewStatusEnum.ReviewReject]: "warning"
};

export const AccountRegisterReviewStatusOptions: Array<IOption> = mapDescToOptions(
  AccountRegisterReviewStatusEnumMapDesc
);
