<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top" label-width="120px">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="版本" prop="versionId">
          <el-select
            class="!w-full"
            v-model="form.versionId"
            filterable
            clearable
            placeholder="请选择版本"
            :disabled="disabled"
          >
            <el-option
              v-for="item in productVersionList || []"
              :key="item.id"
              :label="item.versionName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="产品分类名称" prop="categoryName">
          <el-input v-model="form.categoryName" clearable placeholder="请输入产品分类名称" :disabled="disabled" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="产品ID" prop="productCode">
          <el-input v-model="form.productCode" clearable placeholder="请输入产品ID" :disabled="disabled" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="型号名称" prop="modelName">
          <el-input v-model="form.modelName" clearable placeholder="请输入型号名称" :disabled="disabled" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="电压等级" prop="voltage">
          <el-input v-model="form.voltage" clearable placeholder="请输入电压等级" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格" prop="specification">
          <el-input v-model="form.specification" clearable placeholder="请输入规格名称" :disabled="disabled" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="平方数" prop="crossSection">
          <el-input-number
            class="!w-full"
            v-model="form.crossSection"
            clearable
            controls-position="right"
            placeholder="请输入平方数"
          />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" clearable placeholder="请输入单位" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="国网标记" prop="isFromSGCC">
          <el-checkbox v-model="form.isFromSGCC" :value="true" label="国网标记" border />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="单价" prop="price">
          <el-input-number
            class="!w-full"
            v-model="form.price"
            clearable
            controls-position="right"
            placeholder="请输入单价"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="材料成本" prop="costPrice">
          <el-input-number
            class="!w-full"
            v-model="form.costPrice"
            clearable
            controls-position="right"
            placeholder="请输入材料成本"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="盘名称" prop="reelName">
          <el-input v-model="form.reelName" clearable placeholder="请输入盘名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="盘重" prop="coilWeight">
          <el-input v-model="form.coilWeight" clearable placeholder="请输入盘重" />
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" clearable type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IProductInfoForm, IProductVersion } from "@/models";
import { queryAllProductVersion } from "@/api/product-version";
import { onMounted } from "vue";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  emptyFormValue
});

const props = defineProps<{
  versionId?: string;
}>();

const form = reactive<IProductInfoForm>({
  isFromSGCC: false
});
const formRef = ref<FormInstance>();
const productVersionList = ref<Array<IProductVersion>>([]);

const rules: FormRules = {
  versionId: [{ required: true, trigger: "change", message: "版本不能为空" }],
  categoryName: [{ required: true, trigger: "change", message: "产品分类名称不能为空" }],
  modelName: [{ required: true, trigger: "change", message: "型号名称不能为空" }],
  productCode: [{ required: true, trigger: "change", message: "产品ID不能为空" }],
  unit: [{ required: true, trigger: "change", message: "单位不能为空" }],
  price: [{ required: true, trigger: "change", message: "单价不能为空" }]
};

const disabled = computed(() => {
  return !!form.id;
});

onMounted(() => {
  if (props.versionId) {
    form.versionId = props.versionId;
  }
  handleQueryProductVersion();
});

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IProductInfoForm) {
  Object.assign(form, v);
}

function emptyFormValue() {
  formRef.value.resetFields();
  form.versionId = props.versionId;
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

const handleQueryProductVersion = async () => {
  const { data } = await queryAllProductVersion();
  productVersionList.value = data;
};
</script>

<style scoped></style>
