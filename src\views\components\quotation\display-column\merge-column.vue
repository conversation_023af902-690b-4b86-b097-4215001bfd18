<template>
  <template v-if="props.column === voltageLevel">
    <el-checkbox v-model="voltageLevelMergeModelName" label="合并到型号展示" />
  </template>

  <template v-else-if="props.column === specification">
    <el-checkbox v-model="specificationMergeModelName" label="合并到型号展示" />
  </template>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { specification, voltageLevel } from "./display-column";

const emits = defineEmits<{
  (e: "update:modelSpecificationMergeModelName", value: boolean): void;
  (e: "update:modelVoltageLevelMergeModelName", value: boolean): void;
}>();

const props = withDefaults(
  defineProps<{
    column: string;
    modelSpecificationMergeModelName: boolean;
    modelVoltageLevelMergeModelName: boolean;
  }>(),
  {
    modelSpecificationMergeModelName: false,
    modelVoltageLevelMergeModelName: false
  }
);

const specificationMergeModelName = computed({
  get() {
    return props.modelSpecificationMergeModelName;
  },
  set(value: boolean) {
    emits("update:modelSpecificationMergeModelName", value);
  }
});

const voltageLevelMergeModelName = computed({
  get() {
    return props.modelVoltageLevelMergeModelName;
  },
  set(value: boolean) {
    emits("update:modelVoltageLevelMergeModelName", value);
  }
});
</script>

<style scoped lang="scss"></style>
