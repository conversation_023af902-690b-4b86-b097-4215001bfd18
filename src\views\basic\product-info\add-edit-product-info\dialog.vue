<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" />

    <el-dialog
      v-model="drawerVisible"
      :title="isEditMode ? '编辑' : '新增'"
      :append-to-body="true"
      align-center
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog()"
    >
      <ProductInfoForm ref="formRef" :versionId="versionId" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button v-if="!props.id" type="warning" @click="handleSaveContinueAddBtn" :loading="saveContinueAddLoading"
            >保存、并继续新增</el-button
          >
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import ProductInfoForm from "./form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getProductInfoById, createProductInfo, updateProductInfo } from "@/api/basic/product-info";
import { IProductCategory, IProductInfoForm } from "@/models";

const loading = ref(false);
const saveContinueAddLoading = ref(false);
let saveContinueAddTag = false;
const props = defineProps<{
  mode: "edit" | "add";
  id?: string;
  versionId?: string;
  category?: IProductCategory;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const formRef = ref<InstanceType<typeof ProductInfoForm>>();
const drawerVisible = ref(false);
/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");
/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");
const handleSaveBtn = useLoadingFn(onSave, loading);
const handleSaveContinueAddBtn = useLoadingFn(handleSaveContinueAddLoading, saveContinueAddLoading);

// 订阅弹窗开启状态，请求数据
watch(drawerVisible, async visible => {
  if (!visible || !isEditMode.value) {
    nextTick(() => {
      formRef.value?.initFormValue({
        categoryName: props?.category?.name
      });
    });
    return;
  }
  const { data } = await getProductInfoById(props.id);

  if (data && data.id) {
    const {
      id,
      versionId,
      product,
      product: { modelName, productCode, unit, reelName, coilWeight, crossSection, specification } = {},
      price,
      remark,
      costPrice
    } = data;
    const formValue: IProductInfoForm = {
      id,
      versionId,
      categoryName: product?.model?.category?.name,
      modelName,
      productCode,
      voltage: product?.model?.voltage,
      unit,
      crossSection,
      reelName,
      coilWeight,
      price,
      costPrice,
      specification,
      remark
    };
    formRef.value.initFormValue(formValue);
  }
});

async function handleSaveContinueAddLoading() {
  const result = await handleSave();
  if (!result) {
    return;
  }
  saveContinueAddTag = true;
  formRef.value.emptyFormValue();
  ElMessage({ message: isAddMode.value ? "新增成功" : "编辑成功", type: "success" });
}

/**
 *  保存按钮点击事件
 */
async function onSave() {
  const result = await handleSave();
  if (!result) {
    return;
  }
  closeDialog();
  emits("postSaveSuccess");
  ElMessage({ message: isAddMode.value ? "新增成功" : "编辑成功", type: "success" });
}

async function handleSave() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return false;
  }

  const formVal = formRef.value.getFormValue();
  if (isAddMode.value) {
    await createProductInfo(formVal);
  } else {
    await updateProductInfo(formVal);
  }
  return true;
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  drawerVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  if (saveContinueAddTag) {
    emits("postSaveSuccess");
  }
  drawerVisible.value = false;
}
</script>

<style scoped></style>
