<template>
  <div class="inline-block">
    <el-dialog
      v-model="modelValue"
      title="导入"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="40%"
    >
      <div>
        <div class="">
          <el-radio-group v-model="state.importType" @change="onImportTypeChange()">
            <el-radio value="all" :label="ImportTypeEnum.All">全量</el-radio>
            <el-radio value="append" :label="ImportTypeEnum.Append">增补</el-radio>
          </el-radio-group>

          <div class="flex gap-2 pb-3">
            <template v-if="state.importType === ImportTypeEnum.Append">
              <el-select class="flex-1" v-model="state.versionId" placeholder="请选择版本" @change="onChangeVersion()">
                <el-option
                  v-for="item in productVersionList"
                  :key="item.id"
                  :label="item.versionName"
                  :value="item.id"
                />
              </el-select>
            </template>
            <el-input
              class="flex-1"
              v-model="state.versionName"
              placeholder="请输入版本名称"
              :rules="[
                {
                  required: state.importType === ImportTypeEnum.All,
                  message: '版本不能为空',
                  trigger: 'change'
                }
              ]"
            />
          </div>
        </div>
      </div>
      <el-upload
        class="w-full"
        ref="uploadRef"
        drag
        :auto-upload="false"
        accept=".zip,.xlsx,.xls,.csv,.7z"
        :limit="1"
        :on-exceed="onExceed"
        :on-change="onChangeUploadFile"
      >
        <el-icon><upload-filled /></el-icon>
        <div>拖动上传 or <span class="text-primary">点击上传</span></div>
        <template #tip>
          <div class="el-upload__tip">支持上传.zip,.xlsx,.xls,.csv,.7-Zip</div>
        </template>
      </el-upload>
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleImportBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { uploadFile } from "@/api/upload-file";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage, genFileId, UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { computed, onMounted, reactive, ref } from "vue";
import { productInfoImportByType } from "@/api/basic/product-info";
import { IProductInfoImport, IProductVersion } from "@/models";
import { ImportTypeEnum } from "@/enums";
import { queryAllProductVersion } from "@/api/product-version";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onImportSuccess"): void;
}>();

let uploadRawFile: UploadRawFile = null;
const uploadRef = ref<UploadInstance>();
const loading = ref(false);

const handleImportBtn = useLoadingFn(onImport, loading);

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    versionId?: string;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

const state = reactive<IProductInfoImport>({
  importFileId: "",
  importType: ImportTypeEnum.All,
  versionName: ""
});

const productVersionList = ref<Array<IProductVersion>>([]);

onMounted(async () => {
  const { data } = await queryAllProductVersion();
  productVersionList.value = data;
});

const onImportTypeChange = () => {
  state.versionId = "";
  state.versionName = "";
};

const onChangeVersion = () => {
  state.versionName = productVersionList.value.find(item => item.id === state.versionId)?.versionName;
};

const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRawFile = file;
  uploadRef.value!.handleStart(file);
};

const onChangeUploadFile: UploadProps["onChange"] = uploadFile => {
  uploadRawFile = uploadFile.raw;
};

async function onImport(): Promise<void> {
  if (state.importType === ImportTypeEnum.Append && !state.versionId) {
    ElMessage.warning("版本不能为空");
    return;
  }

  if (!state.versionName?.trim()) {
    ElMessage.warning("版本名称不能为空");
    return;
  }
  if (!uploadRawFile) {
    ElMessage.warning("请选择文件");
    return;
  }
  const formData = new FormData();
  formData.append("file", uploadRawFile);
  const { data } = await uploadFile(formData);
  state.importFileId = data.id;
  await productInfoImportByType({ ...state });
  ElMessage.success("文件导入成功，后台正在自动解析并更新数据");
  emits("onImportSuccess");
  closeDialog();
}

function closeDialog() {
  modelValue.value = false;
}
</script>
