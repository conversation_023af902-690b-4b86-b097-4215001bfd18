import { fullDateFormat } from "@/consts";
import { ColumnWidth, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "企业名称",
      prop: "comName",
      width: ColumnWidth.Char10
    },
    {
      label: "企业简称",
      prop: "name",
      width: ColumnWidth.Char10
    },
    {
      label: "是否限制Token",
      prop: "name",
      slot: "tokenLimitEnabled",
      width: ColumnWidth.Char7,
      align: "center"
    },
    {
      label: "限制Token数",
      prop: "tokenLimitCount",
      align: "center",
      width: ColumnWidth.Char6
    },
    {
      label: "已用Token数",
      prop: "tokenUsedCount",
      align: "center",
      width: ColumnWidth.Char6
    },
    {
      label: "状态",
      prop: "status",
      width: TableWidth.type,
      cellRenderer(data: TableColumnRenderer) {
        const status: boolean = data.row.status;
        return status ? <CxTag type="danger">禁用</CxTag> : <CxTag type="success">启用</CxTag>;
      }
      // formatter: (row: ITenant) => formatEnum(row.status, StatusEnum, "StatusEnum")
    },
    {
      label: "联系人姓名",
      prop: "contactName",
      width: TableWidth.name
    },
    {
      label: "联系人电话",
      prop: "contactMobile",
      width: TableWidth.type
    },
    {
      label: "联系人邮箱",
      prop: "contactEmail",
      width: TableWidth.name
    },
    {
      label: "二次校验",
      prop: "quotationSecondConfirm",
      slot: "quotationSecondConfirm",
      width: ColumnWidth.Char5
    },

    {
      label: "创建时间",
      prop: "createTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];
  return { columns };
}
