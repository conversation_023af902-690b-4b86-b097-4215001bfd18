<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="计量单位：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.productUnit" placeholder="请输入计量单位" />
        </ElFormItem>

        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <div class="flex gap-4">
        <AddEditRawMaterialCostDialog mode="add" @post-save-success="onQuery()">
          <template #trigger="{ openDialog }">
            <el-button class="mb-5" :icon="Plus" type="primary" @click="openDialog">新增</el-button>
          </template>
        </AddEditRawMaterialCostDialog>
        <el-button class="mr-3" :icon="FolderOpened" type="primary" @click="onShowFileImportDialogVisible()"
          >导入</el-button
        >
      </div>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #operation="data">
          <div>
            <AddEditRawMaterialCostDialog mode="edit" :id="data.row.id" @post-save-success="onQuery()">
              <template #trigger="{ openDialog }">
                <el-button link type="primary" @click="openDialog">编辑</el-button>
              </template>
            </AddEditRawMaterialCostDialog>
            <ElButton link type="primary" @click="onViewDetail(data.row.id)"> 查看 </ElButton>
            <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
    <FileImportDialog v-model="state.fileImportDialogVisible" @onImportSuccess="onQuery()" />
  </div>
</template>

<script setup lang="ts" name="raw-material-cost">
import { onMounted, ref, reactive } from "vue";
import { Plus, FolderOpened } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryRawMaterialCost, deleteRawMaterialCostById } from "@/api/quote-basic-data/raw-material-cost";
import { IRawMaterialCost, IRawMaterialCostReq } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import AddEditRawMaterialCostDialog from "./add-edit-raw-material-cost/dialog.vue";
import { useRouter } from "vue-router";
import FileImportDialog from "./file-import/index.vue";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const router = useRouter();
const loading = ref(false);
const state = reactive<{
  list: Array<IRawMaterialCost>;
  params: IRawMaterialCostReq;
  fileImportDialogVisible: boolean;
}>({
  list: [
    {
      id: "1",
      price: 123,
      isLatest: true
    }
  ],
  params: {},
  fileImportDialogVisible: false
});

onMounted(() => {
  requestList();
});

const onShowFileImportDialogVisible = () => {
  state.fileImportDialogVisible = true;
};
const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onViewDetail = (id: string) => {
  router.push(`/basic/material-cost/detail/${id}`);
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteRawMaterialCostById(id);
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryRawMaterialCost(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
