import { IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { http } from "@/utils/http";

export const getSerialNumber = (code: string) => {
  const url: string = withApiGateway("admin-api/system/serial-number/get");
  return http.get<void, IResponse<string>>(url, { params: { code } });
};

export const getSerialNumberByParent = (code: string, parentNo: string) => {
  const url: string = withApiGateway(`admin-api/business/serial-number/${code}/${parentNo}`);
  return http.get<void, IResponse<string>>(url);
};

export const getSerialNumberEditable = (code: string) => {
  const url: string = withApiGateway("admin-api/system/serial-number/editable");
  return http.get<void, IResponse<boolean>>(url, { params: { code } });
};
