<template>
  <div>
    <div class="flex-bc mb-8">
      <div class="flex flex-col">
        <span class="text-lg">密码安全</span>
        <span class="text-sm text-secondary">设置您的新密码</span>
      </div>
    </div>
    <div class="p-5 flex">
      <div class="w-full mx-5">
        <el-form ref="formRef" :model="form" :rules="rules" size="large" label-width="100px" label-position="right">
          <el-form-item label="原密码" prop="oldPassword">
            <el-input type="password" placeholder="请输入原密码" v-model="form.oldPassword" />
          </el-form-item>

          <ElCollapse>
            <el-form-item label="新密码" prop="newPassword">
              <el-input placeholder="请输入新密码" v-model="form.newPassword" type="password" show-password />
              <div class="text-sm text-secondary mt-2" v-if="form.newPassword">
                <div>
                  密码强度:
                  <span v-if="passwordStrength === 'high'" class="text-primary"> 高</span>
                  <span v-if="passwordStrength === 'medium'" class="text-yellow-500"> 中</span>
                  <span v-if="passwordStrength === 'low'" class="text-danger"> 低</span>
                </div>
                <div>1）8-20个字符组成</div>
                <div>2）密码包含大小写字母、数字、特殊字符</div>
              </div>
            </el-form-item>
          </ElCollapse>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input placeholder="请输入确认密码" v-model="form.confirmPassword" type="password" show-password />
          </el-form-item>
          <el-form-item>
            <ElButton size="large" type="primary" @click="onConfirmEditPassword()">保存更改</ElButton>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { ElForm, ElMessage, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { useUserStore } from "@/store/modules/user";
import { IUpdatePassword } from "@/models/user";
import { getPasswordSafety } from "@/utils/get-password-safety";
const formRef = ref<FormInstance>();
const form = reactive<IUpdatePassword>({
  oldPassword: undefined,
  newPassword: undefined,
  confirmPassword: undefined
});
const rules: FormRules = {
  oldPassword: [{ required: true, message: requiredMessage("原密码"), trigger: "change" }],
  newPassword: [
    { required: true, trigger: "change", message: requiredMessage("新密码") },
    {
      trigger: ["blur", "change"],
      pattern: /^(?:(?=.*[0-9])(?=.*[a-zA-Z])|(?=.*[0-9])(?=.*[\W_])|(?=.*[a-zA-Z])(?=.*[\W_])).+$/,
      message: "密码需包含数字、字母、特殊符号中的任意两种字符"
    }
  ],
  confirmPassword: [
    { required: true, trigger: "change", message: requiredMessage("确认密码") },
    { required: true, trigger: "change", validator: validatorConfirmPassword }
  ]
};

const passwordStrength = computed(() => {
  return getPasswordSafety(form.newPassword);
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

const onConfirmEditPassword = async () => {
  if (!(await validate())) {
    return;
  }
  await useUserStore().editPassword(form);
  ElMessage.success("密码更新成功");
  useUserStore().logOut();
};

function validatorConfirmPassword(rule: any, value: string, callback: Function) {
  if (value !== form.newPassword) {
    callback("新密码和确认密码不一致");
    return;
  }

  callback();
}
</script>

<style scoped></style>
