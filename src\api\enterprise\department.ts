import { IDepartment, IDepartmentForm, IDepartmentReq, IDepartmentTree, IListResponse, IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

export const queryDepartment = (params: IDepartmentReq) => {
  return http.get<IDepartmentReq, IListResponse<IDepartment>>(withApiGateway("admin-api/system/dept/list"), { params });
};

export const queryDepartmentTree = () => {
  return http.get<void, IResponse<Array<IDepartmentTree>>>(withApiGateway("admin-api/system/dept/tree"));
};
export const addDepartment = (data: IDepartmentForm) => {
  return http.post<IDepartmentForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/dept/create"),
    { data },
    { showErrorInDialog: true }
  );
};

export const editDepartment = (data: IDepartmentForm) => {
  return http.put<IDepartmentForm, IResponse<IDepartment>>(
    withApiGateway("admin-api/system/dept/update"),
    { data },
    { showErrorInDialog: true }
  );
};

export const deleteDepartment = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway("admin-api/system/dept/delete"), { params: { id } });
};

export const getDepartmentDetailById = (id: string) => {
  return http.get<string, IResponse<IDepartment>>(withApiGateway("admin-api/system/dept/get"), { params: { id } });
};

export const deleteDepartmentEmployee = (deptId: string, userId: string): Promise<IResponse<boolean>> => {
  return http.delete(withApiGateway(`admin-api/system/dept/delete/${deptId}/${userId}`));
};

export const bindEmployeeToDepartment = (deptId: string, userIds: Array<string>): Promise<IResponse<boolean>> => {
  return http.put(withApiGateway("admin-api/system/dept/bind-user-to-dept"), { data: { deptId, userIds } });
};
