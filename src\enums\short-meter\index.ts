import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

export enum ShortMeterRuleOperatorEnum {
  /**
   * 小于
   */
  LESS_THAN = 0,

  /**
   * 小于等于
   */
  LESS_THAN_OR_EQUAL = 1
}

/**
 * 字段操作符的描述映射
 */
export const ShortMeterRuleOperatorEnumMapDesc: Record<ShortMeterRuleOperatorEnum, string> = {
  [ShortMeterRuleOperatorEnum.LESS_THAN]: "<",
  [ShortMeterRuleOperatorEnum.LESS_THAN_OR_EQUAL]: "<="
};

export const ShortMeterRuleOperatorOptions: Array<IOption> = mapDescToOptions<number>(
  ShortMeterRuleOperatorEnumMapDesc
);
