<template>
  <div class="flex flex-nowrap">
    <transition name="fade">
      <div class="flex justify-between items-center text-base mr-4 min-w-[100px]" v-if="props.count">
        <span class="txt">已选：</span>
        <span class="item mr-2">{{ count }} 项</span>
        <el-icon class="close" @click="cancel"><CircleCloseFilled /></el-icon>
      </div>
    </transition>
    <slot />
  </div>
</template>
<script setup lang="ts">
import { CircleCloseFilled } from "@element-plus/icons-vue";

const props = defineProps({
  count: {
    type: Number,
    default: 0
  }
});
const emits = defineEmits<{
  (e: "cancel");
}>();

const cancel = () => {
  emits("cancel");
};
</script>

<style scoped>
.item {
  color: var(--el-color-primary);
}

.close {
  color: var(--el-text-color-placeholder);
  cursor: pointer;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
