import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "询价单位",
      prop: "name"
    },
    {
      label: "报价次数",
      prop: "quotationCount",
      align: "center",
      width: ColumnWidth.Char4
    },
    {
      label: "可见范围",
      prop: "visibilityScope",
      slot: "visibilityScope",
      width: ColumnWidth.Char9
    },
    {
      label: "可见部门",
      prop: "visibleDepartmentNames",
      slot: "visibleDepartmentNames"
    },
    {
      label: "创建时间",
      prop: "createTime"
    },
    {
      label: "创建人",
      prop: "creatorName"
    },
    {
      label: "更新时间",
      prop: "updateTime"
    },
    {
      label: "更新人",
      prop: "updaterName"
    },
    {
      label: "备注",
      prop: "remark"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char6
    }
  ];
  return { columns };
}
