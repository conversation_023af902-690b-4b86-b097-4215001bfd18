<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <div class="flex justify-between">
        <div class="flex flex-1">
          <el-row :gutter="20">
            <el-col :span="17">
              <ElForm
                :inline="true"
                :model="state.quotationInquiryExport"
                class="p-0 party"
                label-width="70"
                label-position="left"
              >
                <el-row :gutter="20">
                  <el-col :span="8">
                    <ElFormItem label="询价单位" class="inquiry-organization !w-full" prop="inquiryCompanyId">
                      <el-select
                        clearable
                        class="!w-full"
                        placeholder="请选择询价单位"
                        filterable
                        v-model="state.quotationInquiryExport.inquiryCompanyId"
                      >
                        <el-option
                          v-for="item in state.inquiryOrganizationList || []"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        />
                      </el-select>
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="联系人" class="contract !w-full" prop="contactPerson">
                      <ElInput
                        clearable
                        placeholder="请输入联系人"
                        v-model="state.quotationInquiryExport.contactPerson"
                      />
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem class="!w-full" label="联系方式" prop="contactInfo">
                      <ElInput
                        clearable
                        placeholder="请输入联系方式"
                        v-model="state.quotationInquiryExport.contactInfo"
                      />
                    </ElFormItem>
                  </el-col>
                  <!-- <el-col :span="5">
                    <ElFormItem label="原始规格型号列" prop="subject" class="show-original-row">
                      <el-switch v-model="state.toggleOriginalRow" @change="onToggleOriginalRow()" />
                    </ElFormItem>
                  </el-col> -->

                  <el-col :span="8">
                    <ElFormItem label="铜价" prop="copperPrice" class="!w-full copper-price">
                      <ElInput type="number" v-model="state.quotationInquiryExport.copperPrice" placeholder="请输入">
                        <template #append>元/吨</template>
                      </ElInput>
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="主题" prop="subject" class="!w-full">
                      <ElInput clearable placeholder="请输入主题" v-model="state.quotationInquiryExport.subject" />
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="参考" class="rerfence !w-full" prop="reference">
                      <ElInput clearable placeholder="请输入参考" v-model="state.quotationInquiryExport.reference" />
                    </ElFormItem>
                  </el-col>
                </el-row>
              </ElForm>
            </el-col>
            <el-col :span="6">
              <ElForm :inline="true" :model="state.quotationInquiryExport" class="p-0">
                <ElFormItem label="备注" class="w-full pr-6 remark" prop="remark">
                  <ElInput
                    :rows="3"
                    resize="none"
                    class="!w-full"
                    clearable
                    placeholder="请输入"
                    type="textarea"
                    v-model="state.quotationInquiryExport.remark"
                  />
                </ElFormItem>
              </ElForm>
            </el-col>
          </el-row>
        </div>
        <div class="action">
          <div>
            <ElButton
              v-auth="PermissionKey.quotation.quotationManagementEdit"
              :icon="Discount"
              :disabled="disabledEditDiscount"
              @click="onBatchQuotationDiscount()"
              >批量折扣</ElButton
            >
            <!-- <ElButton
              v-auth="PermissionKey.quotation.quotationManagementCreate"
              :icon="CopyDocument"
              :disabled="disabledEditDiscount"
              @click="onPasteSpecification()"
              >粘贴型号规格</ElButton
            > -->
            <ElButton
              :icon="Plus"
              v-auth="PermissionKey.quotation.quotationManagementCreate"
              :disabled="disabledEditDiscount"
              type="primary"
              @click="onAddModelNameSpecificationDialogVisible()"
              >添加型号规格</ElButton
            >
          </div>
          <div class="absolute right-5 mt-2">
            <el-checkbox v-model="state.toggleOriginalRow" label="原始信息" @change="onToggleOriginalRow()" />
            <el-checkbox v-model="state.toggleDiscount" label="折扣" @change="onToggleDiscountRow()" />
          </div>
        </div>
      </div>

      <div class="flex-1 overflow-hidden">
        <el-auto-resizer v-loading="loading">
          <template #default="{ height, width }">
            <el-table-v2
              :header-class="getHeaderClass"
              class="quotation-grid"
              ref="tableRef"
              :columns="columnsRef"
              :data="state.list"
              :width="width"
              :height="height"
              row-key="id"
              fixed
            >
              <template #header-cell="{ column }">
                <span>{{ column.title }}</span>
                <template v-if="column.dataKey === 'selection'">
                  <el-checkbox
                    v-model="state.selectedAll"
                    :disabled="state.list?.length === 0"
                    label=""
                    @change="onToggleSelectedAll($event)"
                  />
                </template>
                <template v-else-if="column.dataKey === 'matchStatus'">
                  <el-popover placement="bottom">
                    <el-checkbox-group v-model="state.filterMatchStatus" @change="onChangeMatchStatus()">
                      <el-checkbox :label="MatchStatusEnum.FULL_MATCH">完全匹配</el-checkbox>
                      <el-checkbox :label="MatchStatusEnum.EQUIVALENT_MATCH">等效匹配</el-checkbox>
                      <el-checkbox :label="MatchStatusEnum.NO_MATCH">未匹配</el-checkbox>
                    </el-checkbox-group>
                    <template #reference>
                      <ElIcon
                        class="cursor-pointer"
                        :class="{ 'filter-match-status': state.filterMatchStatus?.length > 0 }"
                      >
                        <Filter />
                      </ElIcon>
                    </template>
                  </el-popover>
                </template>
              </template>
              <template #cell="{ column, rowData, rowIndex }">
                <template v-if="column.key === 'selection'">
                  <el-checkbox v-model="rowData.selected" label="" @change="onToggleSelectedRow()" />
                </template>
                <template v-else-if="column.key === 'rowIndex'"> {{ rowIndex + 1 }} </template>
                <template v-else-if="column.key === 'columnRawContent'">
                  <el-popover placement="top" :show-after="100" :popper-style="{ width: 'auto' }">
                    <template #reference>
                      <div class="columnRawContent text-ellipsis line-clamp-2 pr-1">
                        {{ rowData.columnRawContent }}
                      </div>
                    </template>
                    <div class="max-w-[500px] leading-5">{{ rowData.columnRawContent }}</div>
                  </el-popover>
                </template>
                <template v-else-if="column.key === 'modelName'">
                  <div>
                    {{ rowData.modelName }}<template v-if="rowData.voltageLevel">-{{ rowData.voltageLevel }}</template>
                  </div>
                </template>
                <template v-else-if="column.key === 'excelRowIndex'">
                  <div>{{ rowData.excelSheet }}-{{ rowData.excelRowIndex }}</div>
                </template>

                <template v-else-if="column.key === 'priceMatchRecord'">
                  <el-popover placement="top" :show-after="100" :popper-style="{ width: 'auto' }">
                    <template #reference>
                      <div
                        class="columnRawContent text-ellipsis line-clamp-2 pr-1"
                        :class="{ 'match-no-price': !rowData.isMatchPrice }"
                      >
                        {{ rowData.priceMatchRecord }}
                      </div>
                    </template>
                    <div class="max-w-[500px] leading-5">{{ rowData.priceMatchRecord }}</div>
                  </el-popover>
                </template>

                <template v-else-if="column.key === 'quantity'">
                  <EditNumberCell
                    :data="rowData"
                    field="quantity"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'quantity')"
                  />
                </template>
                <template v-else-if="column.key === 'specification'">
                  <EditStringCell
                    :data="rowData"
                    field="specification"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'specification')"
                  />
                </template>
                <template v-else-if="column.key === 'unit'">
                  <EditStringCell
                    :data="rowData"
                    field="unit"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'unit')"
                  />
                </template>
                <template v-else-if="column.key === 'shortMeterDiscount'">
                  <EditNumberCell
                    :data="rowData"
                    field="shortMeterDiscount"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'shortMeterDiscount')"
                  />
                </template>
                <template v-else-if="column.key === 'discount1'">
                  <EditNumberCell
                    :data="rowData"
                    field="discount1"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discount1')"
                  />
                </template>
                <template v-else-if="column.key === 'discount2'">
                  <EditNumberCell
                    :data="rowData"
                    field="discount2"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discount2')"
                  />
                </template>
                <template v-else-if="column.key === 'discount3'">
                  <EditNumberCell
                    :data="rowData"
                    field="discount3"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discount3')"
                  />
                </template>
                <template v-else-if="column.key === 'discount4'">
                  <EditNumberCell
                    :data="rowData"
                    field="discount4"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discount4')"
                  />
                </template>
                <template v-else-if="column.key === 'discountBeforeUnitPrice'">
                  <EditNumberCell
                    :data="rowData"
                    field="discountBeforeUnitPrice"
                    :precision="2"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discountBeforeUnitPrice')"
                  />
                </template>
                <template v-else-if="column.key === 'taxTotalAmount'">
                  <div class="text-right">{{ formatThousands(rowData.taxTotalAmount) }}</div>
                </template>

                <template v-else-if="column.key === 'matchStatus'">
                  <el-tooltip :content="MatchStatusEnumMapDesc[rowData.matchStatus]" placement="top">
                    <component v-if="rowData.matchStatus === 1" :is="icon_matching" />
                    <component v-if="rowData.matchStatus === 2" :is="icon_quivalent_matching" />
                    <component v-if="rowData.matchStatus === 3" :is="icon_unmatched" />
                    <span v-if="rowData.matchStatus === MatchStatusEnum.NOT_NEED_MATCH">--</span>
                  </el-tooltip>
                </template>
                <template v-else-if="column.key === 'action'">
                  <div class="action-group">
                    <el-button
                      :disabled="disabledEditDiscount"
                      v-auth="PermissionKey.quotation.quotationManagementEdit"
                      @click="onEditQuotationRowShow(rowData)"
                      link
                      :icon="EditPen"
                    />
                    <el-button
                      v-auth="PermissionKey.quotation.quotationManagementDelete"
                      :disabled="disabledEditDiscount"
                      type="danger"
                      @click="onDeleteQuotationRow(rowData.id)"
                      link
                      :icon="Delete"
                    />
                  </div>
                </template>
                <template v-else>
                  {{ rowData[column.key] }}
                </template>
              </template>
              <template #empty>
                <CxEmptyData />
              </template>
            </el-table-v2>
          </template>
        </el-auto-resizer>
      </div>
      <div class="flex items-center justify-center quotation-amount" v-if="state.list.length">
        <div class="select-row flex items-center text-xs" v-if="selectDeleteRows">
          <div class="py-1 rows">{{ selectDeleteRows }}</div>
          <el-button text type="danger" class="text-xs mx-1" size="small" @click="onDeleteSelectedRows()">
            删除
          </el-button>
        </div>
        <div class="text-xs font-semibold">
          含税合计总价：{{ (state.quotationInquiryProgress?.quotationAmount || 0).toLocaleString() }}
        </div>
      </div>
    </div>

    <QuotationDiscountDialog
      :taskId="props.taskId"
      v-model="state.quotationDiscountDialogVisible"
      @onSaveSuccess="handleBatchSetDiscount()"
    />
    <PasteSpecificationDialog v-model="state.pasteSpecificationDialogVisible" />

    <QuotationRowEditDialog
      v-model="state.quotationRowEditDialogVisible"
      :quotationInquiryRow="state.quotationInquiryRow"
      :is-add="state.isAddModelNameSpecification"
      :taskId="props.taskId"
      @onSaveSuccess="handleuotationRowEditSuccess($event)"
      @onSaveSCancel="handleSaveSCancel()"
    />
  </div>
</template>

<script setup lang="ts" name="inquiry-unit">
import { onMounted, ref, reactive, computed, watch, nextTick, onUnmounted } from "vue";
import { Plus, Discount, Delete, EditPen, Filter } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryInquiryOrganizationList } from "@/api/basic/inquiry-organization";
import icon_matching from "@/assets/svg/icon_matching.svg?component";
import icon_quivalent_matching from "@/assets/svg/icon_quivalent_matching.svg?component";
import icon_unmatched from "@/assets/svg/icon_unmatched.svg?component";
import {
  IInquiryOrganization,
  IQuotationInquiryDetail,
  IQuotationInquiryExport,
  IQuotationInquiryProgress
} from "@/models";
import { CheckboxValueType, Column, ElButton, ElMessage, TableV2Instance } from "element-plus";
import QuotationDiscountDialog from "@/views/components/quotation-discount/dialog.vue";
import PasteSpecificationDialog from "@/views/components/paste-specification/dialog.vue";
import CxEmptyData from "@/components/CxEmpty";
import {
  batchDeleteTaskItemByIds,
  deleteTaskItemById,
  getParseDetailByTaskItemId,
  getQuotationInquiryParseDetailList,
  getQuotationInquiryProgress
} from "@/api/quotation/quotation-inquiry";
import { MatchStatusEnum, MatchStatusEnumMapDesc, TaskParseStatusEnum } from "@/enums";
import QuotationRowEditDialog from "@/views/components/quotation-row-edit/dialog.vue";
import { useConfirm } from "@/utils/useConfirm";
import { cloneDeep } from "@pureadmin/utils";
import { PermissionKey } from "@/consts";
import EditNumberCell from "@/views/components/quotation/cell/edit-number-cell.vue";
import EditStringCell from "@/views/components/quotation/cell/edit-string-cell.vue";
import { formatThousands } from "@/utils/format";

interface IQuotationInquiryDetailExt extends IQuotationInquiryDetail {
  selected?: boolean;
}

const emits = defineEmits<{
  (event: "onQuotationInquiryExport", value: IQuotationInquiryExport);
}>();

defineExpose({ quotationInquiryParseDetailList });

const { columns, getColumnsWidthByRate } = useColumns();
const loading = ref(false);
const tableRef = ref<TableV2Instance>();
const columnsRef = ref<Array<Column>>(columns);
let allQuotationList: Array<IQuotationInquiryDetailExt> = [];
const referenceValue = 1632;
const props = withDefaults(
  defineProps<{
    taskId: string;
  }>(),
  {}
);

const state = reactive<{
  list: Array<IQuotationInquiryDetailExt>;
  quotationInquiryExport: IQuotationInquiryExport;
  quotationDiscountDialogVisible: boolean;
  pasteSpecificationDialogVisible: boolean;
  toggleOriginalRow: boolean;
  toggleDiscount: boolean;
  inquiryOrganizationList: Array<IInquiryOrganization>;
  quotationInquiryProgress: IQuotationInquiryProgress;
  quotationInquiryRow: IQuotationInquiryDetail;
  quotationRowEditDialogVisible: boolean;
  isAddModelNameSpecification: boolean;
  filterMatchStatus: Array<MatchStatusEnum>;
  selectedAll: boolean;
}>({
  list: [],
  quotationDiscountDialogVisible: false,
  pasteSpecificationDialogVisible: false,
  quotationInquiryExport: {},
  toggleOriginalRow: true,
  toggleDiscount: false,
  inquiryOrganizationList: [],
  quotationInquiryProgress: {},
  quotationInquiryRow: {},
  quotationRowEditDialogVisible: false,
  isAddModelNameSpecification: false,
  filterMatchStatus: [],
  selectedAll: false
});

const disabledEditDiscount = computed(() => state.quotationInquiryProgress?.parseStatus !== TaskParseStatusEnum.PARSED);
const selectDeleteRows = computed(() => state.list.filter(x => x.selected).length);

watch(
  () => state.quotationInquiryExport,
  () => {
    handleQuotationInquiryExport();
  },
  {
    immediate: true,
    deep: true
  }
);

onMounted(() => {
  handleQueryInquiryOrganization();
  nextTick(() => {
    calculateDynamicWidth();
  });

  window.addEventListener("resize", () => {
    nextTick(() => {
      setTimeout(() => {
        calculateDynamicWidth();
      }, 100);
    });
  });
});

onUnmounted(() => {
  window.removeEventListener("resize", calculateDynamicWidth);
});

const onToggleSelectedAll = (selected: CheckboxValueType) => {
  state.list = state.list.map(item => {
    item.selected = selected as boolean;
    return item;
  });
};

const onToggleSelectedRow = () => {
  state.selectedAll = state.list.filter(x => x.selected).length === state.list.length;
};

const onDeleteSelectedRows = async () => {
  const selectedRows = state.list.filter(x => x.selected);

  if (selectedRows.length === 0) {
    ElMessage.warning("请选择要删除的行");
    return;
  }

  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  const ids = selectedRows.map(x => x.id);
  await batchDeleteTaskItemByIds(ids);
  state.list = state.list.filter(x => !ids.includes(x.id));
  allQuotationList = allQuotationList.filter(x => !ids.includes(x.id));
  handleGetQuotationInquiryProgress();
  ElMessage.success("删除成功");
};

function quotationInquiryParseDetailList() {
  handleGetQuotationInquiryProgress();
  handleGetQuotationInquiryParseDetailList();
}

const onChangeMatchStatus = () => {
  console.log(state.filterMatchStatus);
  if (!Array.isArray(state.filterMatchStatus) || state.filterMatchStatus.length === 0) {
    state.list = cloneDeep(allQuotationList);
  } else {
    state.list = cloneDeep(allQuotationList.filter(x => state.filterMatchStatus.includes(x.matchStatus)));
  }
};

function getHeaderClass() {
  let baseClass = `cx-quotation_header `;
  if (state.toggleOriginalRow) {
    baseClass = `${baseClass} cx-quotation_header-original-row`;
  }
  return baseClass;
}

const onToggleOriginalRow = () => {
  columnsRef.value = columnsRef.value.map(x => {
    if (["excelRowIndex", "columnRawContent"].includes(x.dataKey as string)) {
      x.hidden = !state.toggleOriginalRow;
    }
    return x;
  });
  calculateDynamicWidth();
};
const onToggleDiscountRow = () => {
  columnsRef.value = columnsRef.value.map(x => {
    if (["discount1", "discount2", "discount3", "discount4", "shortMeterDiscount"].includes(x.dataKey as string)) {
      x.hidden = !state.toggleDiscount;
    }
    return x;
  });
  calculateDynamicWidth();
};

const onEditQuotationRowShow = (data: IQuotationInquiryDetail) => {
  state.quotationInquiryRow = data;
  state.isAddModelNameSpecification = false;
  state.quotationRowEditDialogVisible = true;
};

const onAddModelNameSpecificationDialogVisible = () => {
  state.quotationInquiryRow = {};
  state.isAddModelNameSpecification = true;
  state.quotationRowEditDialogVisible = true;
};

const onBatchQuotationDiscount = () => {
  state.quotationDiscountDialogVisible = true;
};

const handleBatchSetDiscount = () => {
  handleGetQuotationInquiryParseDetailList();
};

// const onPasteSpecification = () => {
//   state.pasteSpecificationDialogVisible = true;
// };

const handleSaveSCancel = () => {
  state.quotationInquiryRow = null;
};

const handleuotationRowEditSuccess = (data: IQuotationInquiryDetail) => {
  if (state.isAddModelNameSpecification) {
    state.list.push(data);
    allQuotationList = cloneDeep(state.list);
    scrollToRow(state.list.length);
    return;
  }
  handleGetQuotationInquiryParseDetailList();
};

const handleOnEditSuccess = async (data: IQuotationInquiryDetail, fieldKey: string) => {
  try {
    await handleGetParseDetailByTaskItemId(data);
  } catch (error) {
    data[fieldKey] = allQuotationList.find(x => x.id === data.id)[fieldKey];
  }
};

const handleGetQuotationInquiryParseDetailList = useLoadingFn(async () => {
  const { data } = await getQuotationInquiryParseDetailList(props.taskId);
  if (Array.isArray(data) && data.length) {
    state.list = data;
    allQuotationList = cloneDeep(data);
  } else {
    state.list = [];
    allQuotationList = [];
  }
}, loading);

const handleGetQuotationInquiryProgress = async () => {
  const { data } = await getQuotationInquiryProgress(props.taskId);
  state.quotationInquiryProgress = data;
};

const handleQueryInquiryOrganization = async () => {
  const { data } = await queryInquiryOrganizationList();
  state.inquiryOrganizationList = data;
};

const onDeleteQuotationRow = async (id: string) => {
  if (!(await useConfirm("确认删除该行报价？", "确认删除"))) {
    return;
  }

  await deleteTaskItemById(id);
  state.list = state.list.filter(x => x.id !== id);
  allQuotationList = allQuotationList.filter(x => x.id !== id);
  return ElMessage.success("删除成功");
};

function handleQuotationInquiryExport() {
  emits("onQuotationInquiryExport", {
    taskId: props.taskId,
    ...(state.quotationInquiryExport || {})
  });
}

const handleGetParseDetailByTaskItemId = async (data: IQuotationInquiryDetail) => {
  const { data: updateResult } = await getParseDetailByTaskItemId(data.id);
  const {
    specification,
    unit,
    quantity,
    shortMeterDiscount,
    discount1,
    discount2,
    discount3,
    discount4,
    taxUnitPrice,
    taxTotalAmount,
    discountBeforeUnitPrice
  } = updateResult;
  data.quantity = quantity;
  data.shortMeterDiscount = shortMeterDiscount;
  data.discount1 = discount1;
  data.discount2 = discount2;
  data.discount3 = discount3;
  data.discount4 = discount4;
  data.taxUnitPrice = taxUnitPrice;
  data.taxTotalAmount = taxTotalAmount;
  data.discountBeforeUnitPrice = discountBeforeUnitPrice;
  data.specification = specification;
  data.unit = unit;

  const quotationItem = allQuotationList.find(x => x.id === data.id);
  quotationItem.quantity = quantity;
  quotationItem.shortMeterDiscount = shortMeterDiscount;
  quotationItem.discount1 = discount1;
  quotationItem.discount2 = discount2;
  quotationItem.discount3 = discount3;
  quotationItem.discount4 = discount4;
  quotationItem.taxUnitPrice = taxUnitPrice;
  quotationItem.taxTotalAmount = taxTotalAmount;
  quotationItem.discountBeforeUnitPrice = discountBeforeUnitPrice;
  quotationItem.specification = specification;
  quotationItem.unit = unit;
};

function scrollToRow(rows: number): void {
  nextTick(() => {
    tableRef.value?.scrollToRow(rows, "start");
  });
}

const calculateDynamicWidth = () => {
  const tableWidth = tableRef.value?.$el.offsetWidth || 0;
  const rate = tableWidth / referenceValue;
  const originalColumnsWidth = getColumnsWidthByRate(rate);
  const dynamicWidthFields = ["modelName", "specification", "unit", "quantity"];
  let fixedColumnsWidth =
    originalColumnsWidth["rowIndex"] +
    originalColumnsWidth["selection"] +
    originalColumnsWidth["discountBeforeUnitPrice"] +
    originalColumnsWidth["taxUnitPrice"] +
    originalColumnsWidth["taxTotalAmount"] +
    originalColumnsWidth["priceMatchRecord"] +
    originalColumnsWidth["matchStatus"] +
    originalColumnsWidth["action"];

  if (state.toggleOriginalRow && state.toggleDiscount) {
    columnsRef.value = columnsRef.value.map(col => ({ ...col, width: originalColumnsWidth[col.dataKey as string] }));
    return;
  }

  // 显示原始行
  if (state.toggleOriginalRow) {
    fixedColumnsWidth =
      fixedColumnsWidth + originalColumnsWidth["excelRowIndex"] + originalColumnsWidth["columnRawContent"];
    const averageWidth = (tableWidth - fixedColumnsWidth) / 4;
    columnsRef.value = columnsRef.value.map(col => ({
      ...col,
      width: dynamicWidthFields.includes(col.dataKey as string)
        ? averageWidth
        : originalColumnsWidth[col.dataKey as string]
    }));
    return;
  }

  if (state.toggleDiscount) {
    fixedColumnsWidth =
      fixedColumnsWidth +
      originalColumnsWidth["discount1"] +
      originalColumnsWidth["discount2"] +
      originalColumnsWidth["discount3"] +
      originalColumnsWidth["discount4"];
    const averageWidth = (tableWidth - fixedColumnsWidth) / 4;
    columnsRef.value = columnsRef.value.map(col => ({
      ...col,
      width: dynamicWidthFields.includes(col.dataKey as string)
        ? averageWidth
        : originalColumnsWidth[col.dataKey as string]
    }));
    return;
  }
  const averageWidth = (tableWidth - fixedColumnsWidth) / 4;
  columnsRef.value = columnsRef.value.map(col => ({
    ...col,
    width: dynamicWidthFields.includes(col.dataKey as string) ? averageWidth : col.width
  }));
};
</script>

<style scoped lang="scss">
.party {
  .el-form-item {
    margin-bottom: 0.55rem;

    &.copper-price {
      :deep(.el-input__wrapper) {
        padding-right: 0;
        box-shadow: 0.0625rem 0 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
          0 0 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
          0 -0.0625rem 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
          0 0.0625rem 0 0 var(--el-input-border-color, var(--el-border-color)) inset;
      }

      :deep(.el-input-group__append) {
        padding: 0 10px;
        background-color: transparent !important;
      }
    }

    &.inquiry-organization {
      :deep(.el-form-item__label) {
        font-weight: bold !important;
        position: relative;
      }
    }

    &.show-original-row {
      width: 100%;

      :deep(.el-form-item__label) {
        white-space: nowrap;
      }

      :deep(.el-form-item__content) {
        justify-content: end;
      }
    }
  }
}

.action-group {
  width: 100%;
  display: flex;
  justify-content: space-between;

  .action-item {
    flex: 1;
    text-align: center;
    cursor: pointer;

    svg {
      color: #606266;
    }
  }
}

:deep(.cx-quotation_header) {
  .el-table-v2__header-cell {
    background-color: #fafbfc;
    color: #303339;
    font-size: 14px;
    font-weight: 500;
  }
}

.quotation-grid {
  :deep(.el-table-v2__empty) {
    height: 100% !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f5;
  }

  :deep(.cx-quotation_header) {
    .el-table-v2__header-cell {
      background-color: #fafbfc;
      color: #303339;
      font-size: 14px;
      font-weight: 500;
    }
  }

  :deep(.cx-quotation_header-original-row) {
    .el-table-v2__header-cell[data-key="excelSheet"],
    .el-table-v2__header-cell[data-key="excelRowIndex"],
    .el-table-v2__header-cell[data-key="columnRawContent"] {
      background-color: var(--el-color-warning-light-9);
    }
  }
}

.file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e4e7ed;
  height: 81px;
  width: 520px;
  margin-top: 10px;
  padding: 0 20px;
  background-color: #ffffff;
  gap: 30px;

  .file_name {
    font-size: 16px;
    font-weight: 500;
    color: #303339;
  }
}

.discount-price {
  :deep(.el-input__suffix) {
    svg {
      height: 22px;
    }
  }
}

.filter-match-status {
  color: var(--el-color-primary);
}

.quotation-amount {
  margin-top: 4px;
  // border: 1px solid #dde0e6;
  padding: 4px 0;
  border-radius: 3px;

  .select-row {
    .rows {
      background-color: var(--el-color-primary);
      color: #ffffff;
      padding: 2px 10px;
      border-radius: 4px;
    }
  }
}

.match-no-price {
  color: var(--el-color-warning);
}
</style>
