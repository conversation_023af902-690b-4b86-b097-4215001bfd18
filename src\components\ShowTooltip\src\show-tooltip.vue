<template>
  <el-tooltip
    ref="tooltipRef"
    :placement="placement"
    :effect="effect"
    :disabled="disabled"
    :content="tooltipContent || content"
    :show-after="200"
  >
    <span
      ref="contentRef"
      class="overflow-content"
      :class="[{ 'overflow-line-clamp': lineClamp }, className]"
      :style="{ '--line-clamp': lineClamp }"
      @mouseover="isOverflow"
      >{{ content }}</span
    >
  </el-tooltip>
</template>

<script setup lang="ts">
import type { ElTooltip } from "element-plus/es";
import { ref } from "vue";

const props = defineProps<{
  content?: string;
  placement?:
    | "top"
    | "top-start"
    | "top-end"
    | "bottom"
    | "bottom-start"
    | "bottom-end"
    | "left"
    | "left-start"
    | "left-end"
    | "right"
    | "right-start"
    | "right-end";
  effect?: "dark" | "light";
  className?: string;
  lineClamp?: number;
  tooltipContent?: string;
}>();

const tooltipRef = ref<InstanceType<typeof ElTooltip>>();
const contentRef = ref<HTMLSpanElement>();

const disabled = ref(true);

const isOverflow = () => {
  const el = contentRef.value;
  if (el) {
    if (props.lineClamp) {
      disabled.value = el.offsetHeight >= el.scrollHeight;
    } else {
      disabled.value = el.offsetWidth >= el.scrollWidth;
    }
  }
};
</script>

<style lang="scss" scoped>
.overflow-content {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overflow-line-clamp {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-wrap;
  -webkit-line-clamp: var(--line-clamp, 2);
  -webkit-box-orient: vertical;
}
</style>
