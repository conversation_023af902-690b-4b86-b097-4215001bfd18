import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IResponse, IShortMeterRule, IShortMeterRuleForm } from "@/models";

/** 查询短米规格配置 */
export const getShortMeterRule = () => {
  return http.get<void, IResponse<Array<IShortMeterRule>>>(
    withApiGateway("admin-api/business/productPrice/shortMeterRule")
  );
};

/**  新增 or 编辑 */
export const createOrUpdateShortMeterRule = (data: Array<IShortMeterRuleForm>) => {
  return http.post<Array<IShortMeterRuleForm>, IResponse<boolean>>(
    withApiGateway("admin-api/business/productPrice/shortMeterRule/operate"),
    { data }
  );
};

export const deleteShortMeterRuleById = (id: string) => {
  return http.delete<void, IResponse<boolean>>(withApiGateway(`admin-api/business/productPrice/shortMeterRule/${id}`));
};
