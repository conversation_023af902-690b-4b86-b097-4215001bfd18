import { ShortMeterRuleOperatorEnum } from "@/enums";

export interface IShortMeterRuleForm {
  id?: string;

  /**
   * 结束比较符号 <为0 <=为1
   */
  maxCompareOp?: ShortMeterRuleOperatorEnum;
  /**
   * 结束米数
   */
  maxMeter?: number;
  /**
   * 起始比较符号 <为0 <=为1
   */
  minCompareOp?: ShortMeterRuleOperatorEnum;
  /**
   * 起始米数
   */
  minMeter?: number;
  /**
   * 单价浮动比例（百分比值，允许负值）
   */
  unitPriceFloatRatio?: number;
}
