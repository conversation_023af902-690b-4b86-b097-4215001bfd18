import { PureHttpRequestConfig } from "@/utils/http/types";
import { formatToken, getToken } from "@/utils/auth";
import { AxiosInstance } from "axios";

/** 不需要token url */
const whiteList: Array<string> = ["auth/refresh-token", "auth/login"];

export function accessTokenInterceptor(instance: AxiosInstance): number {
  return instance.interceptors.request.use((config: PureHttpRequestConfig) => {
    if (skipAccessToken(config.url) || config.skipAccessToken) {
      return config;
    }
    const token = getToken();
    if (token) {
      config.headers["Authorization"] = formatToken(token.accessToken);
    }
    return config;
  });
}

function skipAccessToken(target: string): boolean {
  return whiteList.some(url => target.includes(url));
}
