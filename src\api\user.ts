import { http } from "@/utils/http";
import { IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { IProfile, IAuth, IUpdatePassword, IPasswordSafety, IWeChatQRCode } from "@/models/user";

/** 登录 */
export const getLogin = (username: string, password: string) => {
  const url: string = withApiGateway("admin-api/system/auth/login");
  return http.request<IResponse<IAuth>>("post", url, {
    data: { username, password }
  });
};

/** 登录 */
export const getLoginByWeChat = () => {
  const url: string = withApiGateway("admin-api/system/auth/wechat/qrcode");
  return http.request<IResponse<IWeChatQRCode>>("get", url);
};

/** 轮询扫描登录结果 */
export const checkWeChatLogin = (sceneId: string) => {
  const url: string = withApiGateway(`admin-api/system/auth/wechat/checkScan/${sceneId}`);
  return http.request<IResponse<IAuth>>("get", url);
};

/** 轮询扫码绑定结果 */
export const checkWeChatBind = (sceneId: string) => {
  const url: string = withApiGateway(`admin-api/system/user/profile/checkScan-for-bind/${sceneId}`);
  return http.request<IResponse<IAuth>>("get", url);
};

/**  解绑微信绑定 */
export const WeChatLoginUnbind = () => {
  const url: string = withApiGateway(`admin-api/system/user/profile/unbind`);
  return http.request<IResponse<IAuth>>("get", url);
};

/** 刷新token */
export const refreshAccessToken = (refreshToken: string) => {
  const url: string = withApiGateway("admin-api/system/auth/refresh-token");
  return http.post<void, IResponse<IAuth>>(url, { params: { refreshToken } }, { silentError: true });
};

/** 获取登录用户信息 */
export const getProfile = () => {
  const url: string = withApiGateway("admin-api/system/user/profile/get");
  return http.get<void, IResponse<IProfile>>(url);
};

export const switchTenant = (tenantId: string) => {
  return http.post<string, IResponse<IAuth>>(withApiGateway("admin-api/system/auth/tenant-switch"), {
    params: { tenantId }
  });
};

export const editPassword = (data: IUpdatePassword) => {
  return http.put(withApiGateway("admin-api/system/user/profile/update-password"), { data });
};

export const editAvatar = (data: FormData) => {
  return http.post<FormData, IResponse<string>>(withApiGateway("admin-api/system/user/profile/update-avatar"), {
    data,
    headers: { "Content-type": "multipart/form-data" }
  });
};

/** 查询密码强度 */
export const queryPasswordSafety = () => {
  return http.get<void, IResponse<IPasswordSafety>>(
    withApiGateway("admin-api/system/user/profile/checkPasswordStrength")
  );
};
/** 退出登录 */
export const loginOut = () => {
  const url: string = withApiGateway("admin-api/system/auth/logout");
  return http.post<void, IResponse<string>>(url, {});
};
