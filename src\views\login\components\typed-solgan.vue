<template>
  <div class="typing-container">{{ displayText }}</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";

const fullTexts = ref(["报价如此简单", "一键搞定，成交快人一步！"]);
const displayText = ref("");
const currentTextIndex = ref(0);
const currentCharIndex = ref(0);
const isWaiting = ref(false);

let typingInterval: number | undefined;
let waitInterval: number | undefined;

const typeWriter = () => {
  // 如果正在等待，直接返回
  if (isWaiting.value) return;

  // 如果已经打完所有文本，重置索引并开始等待
  if (currentTextIndex.value >= fullTexts.value.length) {
    if (typingInterval) {
      clearInterval(typingInterval);
    }

    isWaiting.value = true;

    // 5秒后重新开始
    waitInterval = setTimeout(() => {
      currentTextIndex.value = 0;
      displayText.value = "";
      currentCharIndex.value = 0;
      isWaiting.value = false;
      startTyping();
    }, 5000) as unknown as number;

    return;
  }

  const currentText = fullTexts.value[currentTextIndex.value];

  // 添加一个字符
  if (currentCharIndex.value < currentText.length) {
    displayText.value += currentText.charAt(currentCharIndex.value);
    currentCharIndex.value++;
  } else {
    // 当前文本打完后
    currentTextIndex.value++;
    currentCharIndex.value = 0;
    displayText.value += "\n";
  }
};

const startTyping = () => {
  // 清除之前的定时器（如果存在）
  if (typingInterval) {
    clearInterval(typingInterval);
  }

  // 每130ms添加一个字符
  typingInterval = setInterval(typeWriter, 150) as unknown as number;
};

// 在组件卸载时清理定时器
onUnmounted(() => {
  if (typingInterval) {
    clearInterval(typingInterval);
  }
  if (waitInterval) {
    clearTimeout(waitInterval);
  }
});

onMounted(() => {
  startTyping();
});
</script>

<style scoped>
.typing-container {
  height: 150px;
  white-space: pre-wrap;
}
</style>
