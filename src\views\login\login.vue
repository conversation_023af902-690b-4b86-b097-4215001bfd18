<template>
  <div class="login-container relative select-none">
    <img :src="bg" class="login-bg" alt="bg" />
    <BackgroundFlowAnimation />
    <div class="absolute z-10 left-32 top-16">
      <Motion :delay="100">
        <div class="login-logo-group">
          <component class="logo" :is="toRaw(logo)" />
          <el-divider direction="vertical" />
          <span class="logo-text">玲珑AI 数字员工-智能报价助手</span>
        </div>
      </Motion>
    </div>
    <div class="flex-c absolute space-x-2 right-16 top-16">
      <!-- 主题 -->
      <el-switch
        class="invisible"
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
      <!-- 国际化 -->
      <el-dropdown trigger="click" class="invisible">
        <globalization
          class="hover:text-primary-color hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'zh')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'zh')]"
              @click="translationCh"
            >
              <IconifyIconOffline class="check-zh" v-show="locale === 'zh'" :icon="Check" />
              简体中文
            </el-dropdown-item>
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'en')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'en')]"
              @click="translationEn"
            >
              <span class="check-en" v-show="locale === 'en'">
                <IconifyIconOffline :icon="Check" />
              </span>
              English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="flex-c text-base space-x-2">
        <FontIcon icon="icon-telephone" class="text-primary-color" />
        <span class="text-regular">全国服务电话</span>
        <a class="text-regular" :href="`tel:${tel}`">{{ tel }}</a>
      </div>
    </div>
    <div class="login-content w-[800px] absolute left-0 top-0">
      <div class="flex-c absolute left-32 top-1/4">
        <!-- 默认 -->
        <div v-if="loginType === 'default'" class="login-solgan flex-col-center">
          <Motion>
            <TypedSolgan />
          </Motion>
          <Motion :delay="50">
            <el-button @click="toLogin" class="login-btn" type="primary" round size="large">开始使用</el-button>
          </Motion>
        </div>
        <!-- 微信扫码登录 -->
        <div v-if="loginType === 'wechat'">
          <Motion>
            <div class="login-code">
              <div v-if="showPasswordAction" class="absolute top-2 right-2 cursor-pointer" @click="toAccountLogin">
                <img src="@/assets/img/pc-logo.png" alt="pc-logo" />
              </div>
              <div class="login-code-box flex-col-center">
                <div class="text-2xl font-medium text-center mb-10">微信安全登录</div>
                <div
                  v-if="weChatQRCode?.url"
                  class="qrcode"
                  :style="{ background: `url(${weChatQRCode.url}) no-repeat center center` }"
                />
                <div class="login-guide text-center">
                  <p>请使用微信扫扫一扫</p>
                  <p class="tip">扫码后请在微信中确认登录</p>
                </div>
                <Agreement />
              </div>
            </div>
          </Motion>
        </div>
        <!-- 新账号密码登录 -->
        <div v-if="loginType === 'account'">
          <Motion>
            <div class="login-code">
              <div class="absolute top-2 right-2 cursor-pointer" @click="toQRCodeLogin">
                <img src="@/assets/img/qr-code.png" alt="qr-code" />
              </div>
              <div class="login-account-box flex-col-center">
                <div class="text-2xl font-medium text-center mb-20">账号密码登录</div>
                <el-form
                  ref="ruleFormRef"
                  :model="form"
                  :rules="loginRules"
                  size="large"
                  class="login-form"
                  @submit.prevent
                >
                  <Motion :delay="100">
                    <el-form-item prop="username">
                      <el-input
                        clearable
                        v-model="form.username"
                        :placeholder="t('login.username')"
                        :prefix-icon="useRenderIcon(user)"
                      />
                    </el-form-item>
                  </Motion>

                  <Motion :delay="150">
                    <el-form-item prop="password">
                      <el-input
                        class="pwd"
                        clearable
                        show-password
                        v-model="form.password"
                        :placeholder="t('login.password')"
                        :prefix-icon="useRenderIcon(lock)"
                      />
                    </el-form-item>
                  </Motion>
                  <Agreement />

                  <Motion :delay="250">
                    <el-button
                      class="w-full mt-8"
                      size="large"
                      color="#00b678"
                      native-type="submit"
                      :loading="loading"
                      @click="onLogin(ruleFormRef)"
                    >
                      {{ t("login.login") }}
                    </el-button>
                  </Motion>
                </el-form>
              </div>
            </div>
          </Motion>
        </div>
      </div>
    </div>
    <div class="absolute bottom-10 left-0 right-0 text-center text-xs text-gray-400">
      <Motion :delay="300">
        <div class="text-secondary text-sm">
          Copyright © 2017-{{ currentYear }} {{ t("login.company") }}. All Rights Reserved
        </div>
      </Motion>
      <Motion :delay="350">
        <div class="flex-c gap-5 mt-2">
          <div class="flex">
            <div style="width: 16px; margin-right: 4px">
              <img style="object-fit: contain" :src="beian" alt="beian" />
            </div>
            <a href="https://beian.miit.gov.cn" target="_blank">沪ICP备18008973号-1</a>
          </div>
          <div class="flex">
            <div style="width: 16px; margin-right: 4px">
              <img style="object-fit: contain" :src="beian" alt="beian" />
            </div>
            <a href="http://www.beian.gov.cn" target="_blank">沪公网安备31011002002604号</a>
          </div>
        </div>
      </Motion>
    </div>
  </div>
</template>

<script setup lang="ts">
import bg from "@/assets/img/login_bg_xl.png";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { useLayout } from "@/layout/hooks/useLayout";
import { useNav } from "@/layout/hooks/useNav";
import { useTranslationLang } from "@/layout/hooks/useTranslationLang";
import Check from "@iconify-icons/ep/check";
import type { FormInstance } from "element-plus";
import { onMounted, ref, toRaw, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useLoginHook } from "./login-hook";
import { useLoginWeChatHook } from "./wechat-login-hook";
import Motion from "./utils/motion";
import { loginRules } from "./utils/rule";
import { darkIcon, dayIcon, globalization, lock, logo, user } from "./utils/static";
import beian from "@/assets/img/beian.png";
import { getLoginByWeChatQRCode, checkWeChatLogin } from "@/api/user-wechat";
import { IWeChatQRCode } from "@/models/user";
import { useIntervalFn } from "@vueuse/core";
import TypedSolgan from "./components/typed-solgan.vue";
import BackgroundFlowAnimation from "./components/background-flow-animation.vue";
import Agreement from "./agreement.vue";
import { useRoute } from "vue-router";

const { login, loading, form } = useLoginHook();
const { wechatLoginRedirect } = useLoginWeChatHook();
const ruleFormRef = ref<FormInstance>();
const weChatQRCode = ref<IWeChatQRCode>();
const tel = "************";

const loginType = ref("default");
const showPasswordAction = ref(false);
const route = useRoute();

const { initStorage } = useLayout();
initStorage();

const { t } = useI18n();
const { dataTheme, dataThemeChange } = useDataThemeChange();
dataThemeChange();
const { getDropdownItemStyle, getDropdownItemClass } = useNav();
const { locale, translationCh, translationEn } = useTranslationLang();
const currentYear = new Date().getFullYear();
const { pause: stopCheckWeChatLogin, resume: startCheckWeChatLogin } = useIntervalFn(handleCheckWeChatLogin, 1000, {
  immediate: false
});

watch([() => loginType.value, () => weChatQRCode.value], ([loginType, weChatQRCode]) => {
  if (loginType === "wechat" && weChatQRCode?.sceneId) {
    startCheckWeChatLogin();
  }
});

onMounted(() => {
  if (route.query.loginMode) {
    showPasswordAction.value = (route.query.loginMode as string) === "cxAddress";
  }
  document.title = "程析玲珑AI-数字员工";
});

const toLogin = async () => {
  await getWeChatQRCode();
  loginType.value = "wechat";
};

const toAccountLogin = () => {
  stopCheckWeChatLogin();
  loginType.value = "account";
};

const toQRCodeLogin = async () => {
  await getWeChatQRCode();
  loginType.value = "wechat";
};

const onLogin = async (formEl: FormInstance | undefined) => {
  loading.value = true;
  login(formEl);
};

const getWeChatQRCode = async () => {
  const { data } = await getLoginByWeChatQRCode();
  weChatQRCode.value = data;
};

async function handleCheckWeChatLogin(): Promise<void> {
  try {
    const { data } = await checkWeChatLogin(weChatQRCode.value?.sceneId);

    if (data && Object.keys(data).length) {
      await wechatLoginRedirect(data);
      stopCheckWeChatLogin();
    }
  } catch (error) {
    stopCheckWeChatLogin();
    return;
  }
}
</script>

<style lang="scss" scoped>
@media screen and (max-width: 1024px) {
  .login-container {
    min-width: 1024px;
    overflow: auto;
  }
}

:root {
  --text-color: #3d3d3d;
}

.flex-col-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .login-bg {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .login-logo-group {
    display: flex;
    align-items: center;
    gap: 0.5em;
  }

  .logo-text {
    color: var(--text-color);
    font-size: 24px;
    font-weight: 500;
  }

  .login-account {
    height: 50vh;
  }

  .login-form {
    width: 20em;
    padding-left: 0;
    padding-right: 0;
  }

  .login-content {
    height: 100%;
    background: linear-gradient(90deg, #f6fafb 29%, rgba(246, 250, 251, 0) 100%);
  }

  .login-solgan {
    @apply text-4xl lg:text-5xl font-semibold;
    line-height: 1.5;
    height: 50vh;
    color: var(--text-color);
  }

  .login-btn {
    margin-top: 1.5em;
    height: 58px;
    border-radius: 29px;
    font-size: 24px;
    padding: 18px 55px;
    font-weight: 500;
  }

  .pwd {
    font-family: "auto";
  }

  .text-primary-color {
    color: #00b678;
  }

  .login-title {
    font-size: 4.5em;
    letter-spacing: 0.05em;
  }
}

// 二维码登录
.login-code {
  position: relative;
  border-radius: 15px;
  background: #fff;
  box-shadow: 0px 4px 20px 0px rgba(17, 21, 90, 0.15);
}

.login-code-box {
  padding: 80px 60px 35px;
  align-items: center;
}

.login-account-box {
  padding: 80px 50px;
}

.login-account-box .login-form {
  width: 18em;
}

.qrcode {
  width: 180px;
  height: 180px;
  // border: 1px solid #ebeef5;
  border-radius: 4px;
  background-size: contain !important;
}

.login-guide {
  margin-top: 30px;
  color: #606266;
}

.tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.el-input {
  --el-input-focus-border: #00b678;
  --el-input-focus-border-color: #00b678;
}
</style>
