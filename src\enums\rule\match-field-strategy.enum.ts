import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

/**  型号字段匹配策略枚举 */
export enum MatchFieldStrategyEnum {
  /**
   * AI解析型号
   */
  AI_MODEL = 0,

  /**
   * 原始型号
   */
  ORIGINAL_MODEL = 1
}

/**
 * 匹配字段策略的描述映射
 */
export const MatchFieldStrategyEnumMapDesc: Record<MatchFieldStrategyEnum, string> = {
  [MatchFieldStrategyEnum.AI_MODEL]: "AI-解析型号",
  [MatchFieldStrategyEnum.ORIGINAL_MODEL]: "Excel-原始型号"
};

export const MatchFieldStrategyOptions: Array<IOption> = mapDescToOptions<number>(MatchFieldStrategyEnumMapDesc);
