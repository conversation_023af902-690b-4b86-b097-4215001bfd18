module.exports.getEnum = model => {
  const enums = model.filter(x => Array.isArray(x.enums) && x.enums.length);
  if (!Array.isArray(enums) || enums.length === 0) {
    return [];
  }
  return enums;
};

module.exports.getEnumNameArr = model => {
  const enums = model.filter(x => Array.isArray(x.enums) && x.enums.length);
  if (!Array.isArray(enums) || enums.length === 0) {
    return [];
  }
  return enums.map(x => x.prop);
};
