import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IListResponse,
  IResponse,
  IBomInformation,
  IBomInformationForm,
  IBomInformationReq,
  IBomMaterialQuota
} from "@/models";

/** 查询BOM信息分页  */
export const queryBomInformation = (data: IBomInformationReq) => {
  const url: string = withApiGateway("admin-api/business/bom/page");
  return http.post<IBomInformationReq, IListResponse<IBomInformation>>(url, {
    data
  });
};

/** 根据BOM信息id 查询详情 */
export const getBomInformationById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/bom/get?id=${id}`);
  return http.get<void, IResponse<IBomInformation>>(url);
};

/** 新增BOM信息 */
export const createBomInformation = (data: IBomInformationForm) => {
  return http.post<IBomInformationForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/bom/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑BOM信息 */
export const updateBomInformation = (data: IBomInformationForm) => {
  return http.put<IBomInformationForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/bom/update"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除BOM信息根据Id */
export const deleteBomInformationById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/business/bom/delete?id=${id}`));
};

/** 根据bomId获取bom下材料定额信息 */
export const getMaterialQuota = (bomId: string) => {
  const url: string = withApiGateway(`admin-api/business/bom/getMaterialQuota?bomId=${bomId}`);
  return http.get<void, IResponse<Array<IBomMaterialQuota>>>(url);
};

/** 批量删除BOM信息根据Id */
export const batchDeleteBomInformationById = (ids: Array<string>) => {
  return http.delete<{ idList: Array<string> }, IResponse<boolean>>(
    withApiGateway(`admin-api/business/bom/delete-batch`),
    { data: { idList: ids } }
  );
};
