import { defineComponent } from "vue";

export default defineComponent({
  name: "TitleBar",
  props: {
    title: {
      type: String
    },
    fontSize: {
      type: String,
      default: "16px"
    },
    lineHeight: {
      type: String,
      default: "24px"
    }
  },

  setup(props) {
    return () => (
      <div class="flex items-center">
        <div class="bg-primary" style={{ width: "4px", height: "15px", marginBottom: "2px" }}></div>
        <div
          style={{
            marginLeft: "10px",
            fontSize: props.fontSize,
            lineHeight: props.lineHeight
          }}
          class="text-text_color_primary font-medium"
        >
          {props.title}
        </div>
      </div>
    );
  }
});
