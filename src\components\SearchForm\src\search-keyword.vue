<template>
  <div class="flex-bc">
    <div v-if="searchLabel" class="label text-base">{{ searchLabel }}</div>
    <el-select
      class="w-[400px] md:w-[500px] lg:w-[600px]"
      v-model="keywordItems"
      multiple
      clearable
      filterable
      remote
      value-key="id"
      :size="size"
      :reserve-keyword="false"
      :placeholder="ctx.placeholder"
      :remote-method="remoteMethod"
      @blur="blur"
      @focus="focus"
      @change="change"
      @clear="clearAndSearch"
    >
      <template #prefix>
        <FontIcon class="text-secondary" icon="icon-search" />
      </template>
      <el-option
        v-for="item in options"
        :key="item.value.id"
        :label="item.label"
        :value="item.value"
        :disabled="item.disabled"
      >
        <span>{{ item.value.fieldTitle ? `${item.value.fieldTitle}：` : "" }}</span>
        <span class="text-secondary">{{ item.value.keyword }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { useKeyword } from "./keyword-hooks";
import { computed, inject, watch } from "vue";
import { searchFormToken } from "./tokens";
import { SizeType } from "./types";

const ctx = inject(searchFormToken);
const { keywordItems, options, blur, focus, clear, change, remoteMethod, generateKeywordItemsByFilters } = useKeyword();

defineExpose({
  clear,
  keywordItems
});

const props = withDefaults(
  defineProps<{
    searchLabel?: string;
    keywordFilter?: Array<Record<string, string>>;
    size?: SizeType;
  }>(),
  {
    size: SizeType.LARGE
  }
);

const searchLabel = computed(() => props.searchLabel);

watch(
  () => props.keywordFilter,
  keywordFilter => generateKeywordItemsByFilters(keywordFilter),
  { immediate: true }
);

function clearAndSearch(): void {
  clear();
  ctx.searchCb();
}
</script>

<style scoped lang="scss">
:deep(.el-select__input) {
  margin-left: 38px !important;
}

.label {
  width: 110px;
  display: inline-flex;
  justify-content: flex-end;
  align-items: flex-start;
  flex: 0 0 auto;
  height: 40px;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
}

:deep(.el-select-tags-wrapper.has-prefix):not(:empty) {
  margin-left: 38px !important;

  .el-select__tags-text {
    max-width: 290px !important;
  }
}
</style>
