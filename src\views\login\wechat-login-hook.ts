import { ref } from "vue";
import { useUserStoreHook } from "@/store/modules/user";
import { useSystemAuthStore } from "@/store/modules";
import { initRouter } from "@/router/utils";
import { useRoute, useRouter } from "vue-router";
import { message } from "@/utils/message";
// import { usePermissionStore } from "@/store/modules/permission";
import { router as routerInst } from "@/router";
import { postMessage } from "@/utils/browsingContextsCommunication";
import { TopicEnum } from "@/utils/browsingContextsCommunication/topic.enum";
import { IAuth } from "@/models/user";

export const useLoginWeChatHook = () => {
  const loading = ref(false);
  const systemAuthStore = useSystemAuthStore();
  const userStoreHook = useUserStoreHook();
  // const permissionStore = usePermissionStore();
  const router = useRouter();
  const route = useRoute();
  const wechatLoginRedirect = async (data: IAuth) => {
    loading.value = true;
    userStoreHook.loginByWeChat(data);
    userStoreHook.$reset();
    // 获取后端路由
    await initRouter()
      .then(async () => {
        message("登录成功", { type: "success" });
        postMessage(TopicEnum.LOGIN);
        // 查询授权信息
        await systemAuthStore.getBusinessLicenseAuth();
        routerInst.isReady().then(() => router.push(_getReturnPagePath()));
      })
      .catch(() => (loading.value = false));
  };

  function _getReturnPagePath(): string {
    const returnUrl: string = route.query.returnUrl as string;
    if (returnUrl) {
      return decodeURIComponent(returnUrl);
    }
    // console.log('permissionStore.wholeMenus', permissionStore.wholeMenus);
    // const { children, path } = permissionStore.wholeMenus?.[0] || { children: [], path: "/" };
    return "/home"; // children?.[0]?.path || path;
  }

  return { wechatLoginRedirect };
};
