<template>
  <div v-if="device !== 'mobile'" class="horizontal-header">
    <el-menu
      router
      ref="menuRef"
      mode="horizontal"
      class="horizontal-header-menu"
      :default-active="defaultActive"
      @select="indexPath => menuSelect(indexPath, routers)"
    >
      <el-menu-item
        v-for="route in usePermissionStoreHook().wholeMenus"
        :key="route.path"
        :index="resolvePath(route) || route.redirect"
      >
        <template #title>
          <div v-if="toRaw(route.meta.icon)" :class="['sub-menu-icon', route.meta.icon]">
            <component :is="useRenderIcon(route.meta && toRaw(route.meta.icon))" />
          </div>
          <div :style="getDivStyle">
            <span class="select-none">
              {{ transformI18n(route.meta.title) }}
            </span>
            <extraIcon :extraIcon="route.meta.extraIcon" />
          </div>
        </template>
      </el-menu-item>
    </el-menu>
    <div class="horizontal-header-right">
      <!-- 菜单搜索 -->
      <!-- <Search /> -->
      <!-- 通知 -->
      <!-- <Notice id="header-notice" /> -->
      <!-- 国际化 -->
      <!-- <el-dropdown id="header-translation" trigger="click">
        <globalization class="navbar-bg-hover w-[40px] h-[48px] p-[11px] cursor-pointer outline-none" />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'zh')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'zh')]"
              @click="translationCh"
            >
              <span class="check-zh" v-show="locale === 'zh'">
                <IconifyIconOffline :icon="Check" />
              </span>
              简体中文
            </el-dropdown-item>
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'en')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'en')]"
              @click="translationEn"
            >
              <span class="check-en" v-show="locale === 'en'">
                <IconifyIconOffline :icon="Check" />
              </span>
              English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown> -->
      <LicenseExpireTip />
      <Tenant />

      <!-- 退出登录 -->
      <el-dropdown trigger="click">
        <span class="el-dropdown-link navbar-bg-hover select-none">
          <Avatar :avatar="avatar" :word="wordAvatar" />
          <p v-if="username" class="ml-1.5 dark:text-white">{{ username }}</p>
        </span>
        <template #dropdown>
          <div class="el-dropdown-box">
            <el-dropdown-menu class="logout">
              <el-dropdown-item @click="onEditProfile()">
                <IconifyIconOffline :icon="UserSettingLine" style="margin: 5px" />
                {{ t("buttons.hsUserSetting") }}
              </el-dropdown-item>
              <el-dropdown-item @click="logout">
                <IconifyIconOffline :icon="LogoutCircleRLine" style="margin: 5px" />
                {{ t("buttons.hsLoginOut") }}
              </el-dropdown-item>
            </el-dropdown-menu>
            <el-divider />
            <div class="version">{{ versionStore.getVersion }}</div>
          </div>
        </template>
      </el-dropdown>

      <span v-if="isEnterpriseCmp" class="set-icon navbar-bg-hover" :title="t('buttons.hssystemSet')" @click="onPanel">
        <IconifyIconOffline :icon="Setting" />
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import extraIcon from "./extraIcon.vue";
// import Search from "../search/index.vue";
// import Notice from "../notice/index.vue";
import Tenant from "../tenant/tenant.vue";
import LicenseExpireTip from "../license-expire-tip/index.vue";
import { useNav } from "@/layout/hooks/useNav";
import { transformI18n } from "@/plugins/i18n";
import { ref, toRaw, watch, onMounted, nextTick, watchEffect, onUnmounted } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getParentPaths, findRouteByPath } from "@/router/utils";
import { useTranslationLang } from "../../hooks/useTranslationLang";
import { usePermissionStoreHook } from "@/store/modules/permission";
// import globalization from "@/assets/svg/globalization.svg?component";
import UserSettingLine from "@iconify-icons/ri/user-settings-line";
import LogoutCircleRLine from "@iconify-icons/ri/logout-circle-r-line";
import Setting from "@iconify-icons/ri/settings-3-line";
// import Check from "@iconify-icons/ep/check";
import Avatar from "@/components/Avatar/index.vue";
import { useUserStoreHook } from "@/store/modules/user";
import { useRouter } from "vue-router";
import { useSystemAuthStore, useVersionStore } from "@/store/modules";
import { computedAsync, useTimeoutFn } from "@vueuse/core";
import { emitter } from "@/utils/mitt";

const menuRef = ref();
const defaultActive = ref(null);
const router = useRouter();
const userStoreHook = useUserStoreHook();
const wordAvatar = ref<string>();
const avatar = ref<string>();
const nickname = ref<string>();
const systemAuthStore = useSystemAuthStore();
const versionStore = useVersionStore();
const isEnterpriseCmp = computedAsync(() => systemAuthStore.getIsEnterprise);

const visible = ref(false);

emitter.on("exportRecordAddTip", () => {
  visible.value = true;
  useTimeoutFn(() => {
    visible.value = false;
  }, 3000);
});

onUnmounted(() => {
  emitter.off("exportRecordAddTip");
});

watchEffect(async () => {
  const { nickname: _nickname, avatarInfo } = await userStoreHook.getProfile;
  wordAvatar.value = _nickname?.charAt(0);
  avatar.value = avatarInfo?.url;
  nickname.value = _nickname;
});

const { t, route } = useTranslationLang(menuRef);
const {
  device,
  routers,
  logout,
  onPanel,
  menuSelect,
  resolvePath,
  username,
  getDivStyle
  // getDropdownItemStyle,
  // getDropdownItemClass
} = useNav();

const onEditProfile = () => {
  router.push("/account-profile");
};

function getDefaultActive(routePath) {
  const wholeMenus = usePermissionStoreHook().wholeMenus;
  /** 当前路由的父级路径 */
  const parentRoutes = getParentPaths(routePath, wholeMenus)[0];
  defaultActive.value = findRouteByPath(parentRoutes, wholeMenus)?.children[0]?.path;
}

onMounted(() => {
  getDefaultActive(route.path);
});

nextTick(() => {
  menuRef.value?.handleResize();
});

watch(
  () => [route.path, usePermissionStoreHook().wholeMenus],
  () => {
    getDefaultActive(route.path);
  }
);
</script>

<style lang="scss" scoped>
:deep(.el-loading-mask) {
  opacity: 0.45;
}

.translation {
  ::v-deep(.el-dropdown-menu__item) {
    padding: 5px 40px;
  }

  .check-zh {
    position: absolute;
    left: 20px;
  }

  .check-en {
    position: absolute;
    left: 20px;
  }
}

.logout {
  max-width: 120px;

  ::v-deep(.el-dropdown-menu__item) {
    min-width: 100%;
    display: inline-flex;
    flex-wrap: wrap;
  }
}

.el-dropdown-box {
  padding: 5px 10px 15px;
}

::v-deep(.el-dropdown-menu__item) {
  padding-left: 5px;
}

.el-divider {
  margin: 0 0 10px;
}

.version {
  padding-left: 12px;
}
</style>
