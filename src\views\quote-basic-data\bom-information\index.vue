<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="产品分类：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.productCategory" placeholder="请选择产品分类" />
        </ElFormItem>
        <ElFormItem label="物料编号：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.productCode" placeholder="请输入物料编号" />
        </ElFormItem>
        <ElFormItem label="产品名称：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.productName" placeholder="请输入产品名称" />
        </ElFormItem>
        <ElFormItem label="型号：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.model" placeholder="请输入型号" />
        </ElFormItem>
        <ElFormItem label="规格：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.specification" placeholder="请输入规格" />
        </ElFormItem>
        <ElFormItem label="电压等级：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.voltage" placeholder="请输入电压等级" />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <div class="flex gap-4 mb-5">
        <!-- <el-button :icon="Upload" type="primary">导入</el-button> -->
        <AddEditBomInformationDialog mode="add" @post-save-success="onQuery()">
          <template #trigger="{ openDialog }">
            <el-button :icon="Plus" type="primary" @click="openDialog">新增</el-button>
          </template>
        </AddEditBomInformationDialog>
        <el-button class="mb-5" :icon="Delete" type="danger" :disabled="disabledBatchDelete" @click="onBatchDelete()"
          >批量删除</el-button
        >
      </div>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
        @selection-change="onSelectionChange($event)"
      >
        <template #productName="{ row }">
          <div>{{ row.model }}-{{ row.specification }}</div>
        </template>
        <template #creatorName="{ row }">
          <CXEmployee :name="row.creatorName" />
        </template>
        <template #updaterName="{ row }">
          <CXEmployee :name="row.updaterName" />
        </template>
        <template #operation="data">
          <div>
            <AddEditBomInformationDialog mode="edit" :id="data.row.id" @post-save-success="onQuery()">
              <template #trigger="{ openDialog }">
                <el-button link type="primary" @click="openDialog">编辑</el-button>
              </template>
            </AddEditBomInformationDialog>
            <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="bom-information">
import { onMounted, ref, reactive, computed } from "vue";
import { Plus, Delete } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  queryBomInformation,
  deleteBomInformationById,
  batchDeleteBomInformationById
} from "@/api/quote-basic-data/bom-information";
import { IBomInformation } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import CXEmployee from "@/components/cx-employee/index.vue";
import AddEditBomInformationDialog from "./add-edit-bom-information/dialog.vue";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const disabledBatchDelete = computed(() => state.selectedList.length === 0);
const state = reactive<{
  list: Array<IBomInformation>;
  selectedList: Array<IBomInformation>;
  params: { [key: string]: string };
}>({
  list: [],
  params: {},
  selectedList: []
});

onMounted(() => {
  requestList();
});

const onSelectionChange = (data: Array<IBomInformation>) => {
  state.selectedList = data;
};

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteBomInformationById(id);
  ElMessage.success("删除成功");
  requestList();
};

const onBatchDelete = async () => {
  if (!(await useConfirm("确认批量删除后，数据将无法恢复", "确认批量删除"))) {
    return;
  }
  await batchDeleteBomInformationById(state.selectedList.map(item => item.id));
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryBomInformation(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
