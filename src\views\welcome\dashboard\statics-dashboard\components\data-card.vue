<template>
  <section class="data-card" :style="`background: ${bgColor}`">
    <div class="data-card-header">
      <div class="icon-wrapper">
        <img :src="iconSrc" alt="icon" />
      </div>
      <div class="data-card-title">{{ title }}</div>
    </div>
    <div class="cude">
      <img :src="cudeSrc" alt="cube" />
    </div>
    <div class="illustration">
      <img :src="illustrationSrc" alt="illustration" />
    </div>
    <slot name="content" />
  </section>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  title: string;
  iconSrc: string;
  bgColor: string;
  illustrationSrc: string;
  cudeSrc: string;
}>();
const title = computed(() => props.title);
const iconSrc = computed(() => props.iconSrc);
const bgColor = computed(() => props.bgColor);
const illustrationSrc = computed(() => props.illustrationSrc);
const cudeSrc = computed(() => props.cudeSrc);
</script>

<style lang="scss" scoped>
.data-card {
  position: relative;
  padding: 30px;
  height: 100%;
}

.data-card-header {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 1;
}

.icon-wrapper {
  width: 40px;
  height: 40px;
}

.data-card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.illustration {
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.cude {
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 100%;
}

img {
  width: 100%;
  height: 100%;
}
</style>
