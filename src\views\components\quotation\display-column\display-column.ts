import { Column } from "element-plus";
import { FixedDir } from "element-plus/es/components/table-v2/src/constants";

export const voltageLevel = "voltageLevel";
export const specification = "specification";
export const modelName = "modelName";

export const resetDefaultDisplayColumn: Array<Column> = [
  {
    title: "序号",
    key: "rowIndex",
    dataKey: "rowIndex",
    width: 60,
    align: "center",
    preFreeze: true,
    fixed: FixedDir.LEFT,
    selected: true
  },
  {
    title: "原始位置",
    key: "excelRowIndex",
    dataKey: "excelRowIndex",
    width: 80,
    fixed: FixedDir.LEFT,
    preFreeze: true,
    hidden: false,
    selected: true
  },
  {
    title: "原始数据",
    key: "columnRawContent",
    dataKey: "columnRawContent",
    width: 220,
    fixed: FixedDir.LEFT,
    preFreeze: true,
    hidden: false,
    selected: true
  },
  {
    title: "型号",
    key: modelName,
    dataKey: modelName,
    width: 250,
    fixed: FixedDir.LEFT,
    preFreeze: true,
    selected: true
  },
  {
    title: "电压",
    key: voltageLevel,
    dataKey: voltageLevel,
    width: 130,
    fixed: FixedDir.LEFT,
    preFreeze: true,
    selected: true,
    voltageLevelMergeModelName: true
  },
  {
    title: "规格",
    key: specification,
    dataKey: specification,
    width: 120,
    preFreeze: true,
    fixed: FixedDir.LEFT,
    selected: true
  },
  {
    title: "AI推荐型号",
    key: "aiRecommendModel",
    dataKey: "aiRecommendModel",
    width: 220,
    hidden: true,
    selected: false
  },
  {
    title: "数量",
    key: "quantity",
    dataKey: "quantity",
    width: 110,
    selected: true
  },
  {
    title: "单位",
    key: "unit",
    dataKey: "unit",
    align: "center",
    width: 90,
    selected: true
  },
  {
    title: "短米折扣 (%)",
    key: "shortMeterDiscount",
    dataKey: "shortMeterDiscount",
    width: 105,
    hidden: false,
    selected: true
  },
  {
    title: "折扣1 (%)",
    key: "discount1",
    dataKey: "discount1",
    width: 88,
    hidden: false,
    selected: true
  },
  {
    title: "折扣2 (%)",
    key: "discount2",
    dataKey: "discount2",
    width: 88,
    hidden: false,
    selected: true
  },
  {
    title: "折扣3 (%)",
    key: "discount3",
    dataKey: "discount3",
    width: 88,
    hidden: false,
    selected: true
  },
  {
    title: "折扣4 (%)",
    key: "discount4",
    dataKey: "discount4",
    width: 88,
    hidden: false,
    selected: true
  },
  {
    title: "折扣前单价",
    key: "discountBeforeUnitPrice",
    dataKey: "discountBeforeUnitPrice",
    width: 100,
    align: "right",
    fixed: FixedDir.RIGHT,
    afterFreeze: true,
    selected: true
  },
  {
    title: "含税单价",
    key: "taxUnitPrice",
    dataKey: "taxUnitPrice",
    width: 88,
    align: "right",
    fixed: FixedDir.RIGHT,
    afterFreeze: true,
    selected: true
  },
  {
    title: "含税合计",
    key: "taxTotalAmount",
    dataKey: "taxTotalAmount",
    width: 100,
    align: "right",
    fixed: FixedDir.RIGHT,
    afterFreeze: true,
    selected: true
  },
  {
    title: "匹配说明",
    key: "priceMatchRecord",
    dataKey: "priceMatchRecord",
    width: 100,
    fixed: FixedDir.RIGHT,
    afterFreeze: true,
    selected: true
  },
  {
    title: "",
    key: "matchStatus",
    label: "匹配状态",
    dataKey: "matchStatus",
    fixed: FixedDir.RIGHT,
    afterFreeze: true,
    width: 40,
    selected: true
  }
];
