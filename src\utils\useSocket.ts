import { tryOnUnmounted } from "@vueuse/core";
import { SocketEventEnum } from "@/enums";
import { IRefreshE<PERSON>, SocketHandler } from "@/models";
import { useSocketStore } from "@/store/modules";

export function useSocket() {
  const socket = useSocketStore();

  function on(event: SocketEventEnum.REFRESH, handler: (event: IRefreshEvent) => any): () => void;
  function on(event: SocketEventEnum, handler: SocketHandler): () => void;
  function on(event: SocketEventEnum, handler: SocketHandler): () => void {
    const cancel = socket.on(event, handler);
    tryOnUnmounted(cancel);
    return cancel;
  }
  return {
    on
  };
}
