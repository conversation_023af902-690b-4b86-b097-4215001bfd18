import { fullDateFormat } from "@/consts";
import { ColumnWidth, TableWidth } from "@/enums";
import { IEmployee } from "@/models";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "用户名",
      prop: "username",
      width: TableWidth.name
    },
    {
      label: "姓名",
      prop: "nickname",
      width: TableWidth.name
    },
    {
      label: "手机号",
      prop: "mobile",
      width: TableWidth.type
    },
    {
      label: "微信绑定",
      prop: "wechatUnionId",
      slot: "wechatUnionId",
      width: ColumnWidth.Char5
    },
    {
      label: "邮箱",
      prop: "email",
      width: TableWidth.name
    },
    {
      label: "所属部门",
      prop: "dept",
      minWidth: TableWidth.largeName,
      formatter: (row: IEmployee) => row.dept?.map(x => x.name)?.join("、")
    },
    {
      label: "角色",
      prop: "roles",
      minWidth: TableWidth.largeName,
      formatter: (row: IEmployee) => row.roles?.map(x => x.name)?.join("、")
    },
    {
      label: "账号类型",
      prop: "dataPermissionType",
      slot: "dataPermissionType",
      width: ColumnWidth.Char6
    },
    {
      label: "状态",
      prop: "status",
      width: TableWidth.type,
      cellRenderer(data: TableColumnRenderer) {
        const status: boolean = data.row.status;
        return status ? <CxTag type="danger">禁用</CxTag> : <CxTag type="success">启用</CxTag>;
      }
      // formatter: (row: IEmployee) => formatEnum(row.status, StatusEnum, "StatusEnum")
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operations
    }
  ];
  return { columns };
}
