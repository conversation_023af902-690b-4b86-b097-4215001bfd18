<template>
  <div>
    <el-dialog
      v-model="modelValue"
      top="8vh"
      title="选择产品信息"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="90%"
    >
      <CategoryProductSelectDialog
        ref="categoryProductSelectRef"
        @onSelectProductInfo="handleSelectProductInfo($event)"
      />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="onConfirmSelect()">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import CategoryProductSelectDialog from "./index.vue";
import { IProductInfo } from "@/models";
import { ElMessage } from "element-plus";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onSelectProductInfo", data: IProductInfo): void;
}>();

const categoryProductSelectRef = ref<InstanceType<typeof CategoryProductSelectDialog>>();
let selectProductInfo: IProductInfo;
const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

const onConfirmSelect = () => {
  if (!selectProductInfo || Object.keys(selectProductInfo).length === 0) {
    ElMessage.warning("请选择产品信息");
    return;
  }

  emits("onSelectProductInfo", selectProductInfo);
  closeDialog();
};

const handleSelectProductInfo = (data: IProductInfo) => {
  selectProductInfo = data;
};

function closeDialog() {
  selectProductInfo = null;
  modelValue.value = false;
}
</script>

<style scoped></style>
