export enum LoginMethodEnum {
  /** 账号密码 */
  LOGIN_USERNAME = 100,

  /** 切换租户 */
  LOGIN_TENANT_SWITCH = 101,

  /** 使用社交登录 */
  LOGIN_SOCIAL = 102,

  /** 手机登陆 */
  LOGIN_MOBILE = 103,

  /** 短信验证码 */
  LOGIN_SMS = 104,

  /** 客户端授权 */
  LOGIN_CLIENT_AUTHORIZATION_CODE = 105,

  /** 微信扫码 */
  LOGIN_WECHAT = 106,

  /** 微信小程序 */
  LOGIN_MINI_APP = 107
}

export const LoginMethodEnumMapDesc: Record<LoginMethodEnum, string> = {
  [LoginMethodEnum.LOGIN_USERNAME]: "账号密码",
  [LoginMethodEnum.LOGIN_TENANT_SWITCH]: "切换租户",
  [LoginMethodEnum.LOGIN_SOCIAL]: "使用社交登录",
  [LoginMethodEnum.LOGIN_MOBILE]: "手机登陆",
  [LoginMethodEnum.LOGIN_SMS]: "短信验证码",
  [LoginMethodEnum.LOGIN_CLIENT_AUTHORIZATION_CODE]: "客户端授权",
  [LoginMethodEnum.LOGIN_WECHAT]: "微信扫码",
  [LoginMethodEnum.LOGIN_MINI_APP]: "微信小程序"
};
