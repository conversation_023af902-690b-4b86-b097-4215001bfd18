import { MaterialTypeEnum, ProductUnitEnum } from "@/enums/quote-basic-data";
import { IRawMaterialInformation } from "../raw-material-information";
import { IBase } from "@/models";

export interface IBomMaterialQuota extends IBase {
  /**
   * 所属BOM ID
   * @example "BOM-2023-001"
   */
  bomId?: string;

  /**
   * 选用原材料ID
   * @example "RAW-1001"
   */
  bomRawId?: string;

  /**
   * 主/辅材类型
   * @see MaterialTypeEnum
   */
  materialType?: MaterialTypeEnum;

  /**
   * 材料定额数量
   * @minimum 0
   * @example 2.5
   */
  quantity?: number;

  /**
   * 计量单位
   * @example "米", "千克", "个"
   */
  productUnit?: ProductUnitEnum;

  /** 原材料 */
  bomRaw?: IRawMaterialInformation;
}
