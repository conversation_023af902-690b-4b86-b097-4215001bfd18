import { inject, nextTick, ref } from "vue";
import { I<PERSON>ey<PERSON><PERSON>ield, IKeywordPrompt } from "@/components/SearchForm";
import { searchFormToken } from "./tokens";

interface ListItem {
  label: string;
  value: IKeywordPrompt;
  disabled?: boolean;
}

const ENTER_KEY = "Enter";

export function useKeyword() {
  const ctx = inject(searchFormToken);
  const keywordFields: Array<IKeywordField> = ctx.keywordFields || [];
  const searchCb = ctx.searchCb;
  const searchBtnRef = ctx.searchBtnRef;
  let changed = false;
  let keywordChanged = false;
  const keyword = ref("");
  const options = ref<Array<ListItem>>([]);
  const keywordItems = ref<Array<IKeywordPrompt>>([]);

  const enterListener = async (e: KeyboardEvent) => {
    if (e.key !== ENTER_KEY) {
      return;
    }
    if (changed) {
      return;
    }
    if (keyword.value && keywordChanged) {
      removeAllFieldItem();
      await generateAllFieldItemByKeyword(keyword.value);
    }
    updateKeywordAndOptions("");
    searchCb();
  };

  const blur = (el: FocusEvent) => {
    if (el.relatedTarget === searchBtnRef.value.$el) {
      if (keyword.value && !keywordItems.value.length) {
        generateAllFieldItemByKeyword(keyword.value);
      }
    }
    el.target.removeEventListener("keydown", enterListener);
  };

  const focus = (el: FocusEvent) => {
    el.target.addEventListener("keydown", enterListener);
  };

  const remoteMethod = (query: string) => {
    if (changed) {
      return;
    }
    updateKeywordAndOptions(query);
  };

  const change = () => {
    changed = true;
    keywordChanged = false;
    setTimeout(() => (changed = false), 10);
  };

  const clear = () => {
    keyword.value = "";
    keywordItems.value = [];
  };

  const generateAllFieldItemByKeyword = async (ky: string) => {
    const item = { id: `${ky}_`, label: ky, field: "", fieldTitle: "", keyword: ky };
    options.value = [];
    await nextTick(() => keywordItems.value.push(item));
  };

  function removeAllFieldItem(): void {
    const index: number = keywordItems.value.findIndex(({ field }) => !field);
    if (index < 0) {
      return;
    }
    keywordItems.value.splice(index, 1);
  }

  function updateKeywordAndOptions(query: string): void {
    keywordChanged = true;
    keyword.value = query;
    if (!query) {
      options.value = [];
      return;
    }
    const allFields = [{ title: "", key: "" }, ...keywordFields];
    options.value = allFields.map(field => {
      const value: IKeywordPrompt = _generateKeywordPrompt(query, field);
      return { label: value.label, value, disabled: !field.key };
    });
  }

  function generateKeywordItemsByFilters(filters: Array<Record<string, string>>) {
    keywordItems.value = [];
    if (!Array.isArray(filters) || !filters.length) {
      return;
    }
    _generateFilterMap(filters).forEach((keys, query) => {
      const isAllField = keywordFields.every(field => keys.has(field.key));
      if (isAllField) {
        generateAllFieldItemByKeyword(query);
        return;
      }
      keys.forEach(key => {
        const field = keywordFields.find(f => f.key === key);
        if (!field) {
          return;
        }
        keywordItems.value.push(_generateKeywordPrompt(query, field));
      });
    });
  }

  function _generateFilterMap(filters: Array<Record<string, string>>) {
    const filterMap = new Map<string, Set<string>>();
    filters.forEach(filter => {
      const key = Object.keys(filter)[0];
      if (!key) {
        return;
      }
      const value = filter[key];
      if (!value) {
        return;
      }
      filterMap.has(value) ? filterMap.get(value).add(key) : filterMap.set(value, new Set([key]));
    });
    return filterMap;
  }

  function _generateKeywordPrompt(query: string, field: IKeywordField): IKeywordPrompt {
    const label = field.title ? `${field.title}: ${query}` : query;
    return {
      id: `${query}_${field.key}`,
      label,
      field: field.key,
      keyword: query,
      fieldTitle: field.title
    };
  }

  return {
    keyword,
    options,
    keywordItems,
    blur,
    focus,
    clear,
    change,
    remoteMethod,
    generateKeywordItemsByFilters
  };
}
