<template>
  <div
    class="quick-access"
    :class="{ 'no-permission': props.noPermission }"
    :style="`background: ${bgColor}`"
    @mouseover="hover = true"
    @mouseleave="hover = false"
    @click="navigateTo"
  >
    <div class="content">
      <div class="title" :style="`color: ${titleColor}`">{{ title }}</div>
      <img v-if="icon" :src="icon" alt="icon" class="icon" />
    </div>
    <transition name="slide-fade" v-if="!props.noPermission">
      <div v-if="hover" class="action-button">
        <img :src="arrowIcon" alt="arrow-icon" />
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import arrowIcon from "@/assets/img/arrow-left-circle-fill.png";
import { ref } from "vue";
import { useRouter } from "vue-router";

const props = defineProps<{
  title: string;
  path: string;
  titleColor: string;
  bgColor: string;
  icon?: string;
  noPermission?: boolean;
}>();

const hover = ref(false);

const router = useRouter();

const navigateTo = () => {
  if (props.noPermission) {
    return;
  }
  router.push(props.path);
};
</script>

<style scoped lang="scss">
.quick-access {
  position: relative;
  border-radius: 8px;
  padding: 20px;
  margin: 20px;
  width: 220px;
  height: 220px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border-image: linear-gradient(180deg, #ffffff 0%, #6bbebc 100%) 1;
  box-shadow: 0px 1px 3px 0px rgba(21, 105, 137, 0.2), 0px 2px 4px 0px rgba(37, 121, 118, 0.16);
  overflow: hidden;
  cursor: pointer;

  &.no-permission {
    cursor: not-allowed;
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0px 1px 3px 0px #6bbebc, 0px 2px 4px 0px #ffffff;
  }

  &:hover .icon {
    transform: scale(1.05);
  }
}

/* 过渡动画 */
.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

.content {
  width: 100%;
  height: 100%;
}

.icon {
  position: absolute;
  width: 120px;
  bottom: -10px;
  right: 15px;
  transition: all 0.2s ease;
}

.title {
  padding: 10px 0 0 20px;
  font-weight: 500;
  font-size: 24px;
  margin-bottom: 5px;
}

.action-button {
  width: 40px;
  height: 40px;
  position: absolute;
  bottom: 20px;
  left: 20px;
  border-radius: 50%;

  img {
    width: 100%;
    height: 100%;
  }
}

.grid-item-wide {
  background: linear-gradient(180deg, #13b2b2 0%, #90e4d6 100%);
  box-shadow: 0px 3px 15px 0px rgba(21, 105, 137, 0.08);
}

.grid-item-wide .title {
  color: #fff;
  font-size: 36px;
}

.grid-item-wide .icon {
  width: 450px;
  right: 2px;
}

.grid-item-wide .action-button {
  display: none;
}
</style>
