<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="询价单位：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.name" placeholder="请输入询价单位" />
        </ElFormItem>

        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <AddEditInquiryOrganizationDialog
        v-auth="PermissionKey.meta.metaCustomerCreate"
        mode="add"
        @post-save-success="onQuery()"
      >
        <template #trigger="{ openDialog }">
          <el-button class="mb-5" :icon="Plus" type="primary" @click="openDialog">新增</el-button>
        </template>
      </AddEditInquiryOrganizationDialog>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #visibilityScope="{ row }">
          <CXTag :type="InquiryOrganizationVisibilityScopeEnumMapColor[row.visibilityScope]">{{
            InquiryOrganizationVisibilityScopeEnumMapDesc[row.visibilityScope]
          }}</CXTag>
        </template>
        <template #visibleDepartmentNames="{ row }">
          <template v-if="row.visibleDepartmentNames?.length > 0">
            <div>
              {{ row.visibleDepartmentNames.join(",") }}
            </div>
          </template>
        </template>
        <template #operation="data">
          <div>
            <AddEditInquiryOrganizationDialog
              v-auth="PermissionKey.meta.metaCustomerEdit"
              mode="edit"
              :id="data.row.id"
              @post-save-success="onQuery()"
            >
              <template #trigger="{ openDialog }">
                <el-button link type="primary" @click="openDialog">编辑</el-button>
              </template>
            </AddEditInquiryOrganizationDialog>
            <ElButton link type="danger" v-auth="PermissionKey.meta.metaCustomerDelete" @click="onDelete(data.row.id)">
              删除
            </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="inquiry-unit">
import { onMounted, ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryInquiryOrganization, deleteInquiryOrganizationById } from "@/api/basic/inquiry-organization";
import { IInquiryOrganization, IInquiryOrganizationReq } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import AddEditInquiryOrganizationDialog from "./add-edit-inquiry-organization/dialog.vue";
import { PermissionKey } from "@/consts/permission-key";
import CXTag from "@/components/CxTag/index.vue";
import { InquiryOrganizationVisibilityScopeEnumMapColor, InquiryOrganizationVisibilityScopeEnumMapDesc } from "@/enums";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IInquiryOrganization>;
  params: IInquiryOrganizationReq;
}>({
  list: [],
  params: {}
});

onMounted(() => {
  requestList();
});

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteInquiryOrganizationById(id);
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params: IInquiryOrganizationReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryInquiryOrganization(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
