import { http } from "@/utils/http";
import { withApiGateway } from "./util";
import { IResponse, IUpLoadFileRes } from "@/models";
/**
 * 上传文件
 */
export const uploadFile = (fileData: FormData) => {
  const url = withApiGateway(`admin-api/infra/file/upload`);
  return http.post<FormData, IResponse<IUpLoadFileRes>>(url, {
    data: fileData,
    headers: { "Content-type": "multipart/form-data" }
  });
};

/**
 * 根据文件 Id 获取文件链接（含有效期）
 */
export const getFileShareUrl = (fileId: string) => {
  const url = withApiGateway(`admin-api/infra/file/shareFile/${fileId}`);
  return http.get<void, IResponse<string>>(url);
};

/**
 * 下载文件
 * to do use
 */
export const downLoadFile = (fileId: string) => {
  const url = withApiGateway(`admin-api/infra/file/downloadFile/${fileId}`);
  return http.get<void, Blob>(url, {
    responseType: "blob"
  });
};
