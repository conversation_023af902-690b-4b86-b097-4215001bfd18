export const PermissionKey = {
  workbench: {
    /** 首页 */
    workbench: "home",
    /** 数据看板 */
    workbenchView: "dashboard",
    dashboardView: "dashboard:view"
  },

  /** 基础数据 */
  meta: {
    /** 产品信息  */
    metaPproduct: "meta:product",
    /** 产品信息-查看  */
    metaPproductView: "meta:product:view",
    /** 产品信息-导入导出 1  */
    metaPproductImportExport: "meta:product:batch",
    /** 产品信息-新增 1 */
    metaPproductCreate: "meta:product:create",
    /** 产品信息-编辑 1 */
    metaPproductEdit: "meta:product:edit",
    /** 产品信息-删除 1  */
    metaPproductDelete: "meta:product:delete",

    /** 询价单位 */
    metaCustomer: "meta:customer",
    /** 询价单位-查看*/
    metaCustomerView: "meta:customer:view",
    /** 询价单位-新增 1*/
    metaCustomerCreate: "meta:customer:create",
    /** 询价单位-编辑 1 */
    metaCustomerEdit: "meta:customer:edit",
    /** 询价单位-删除 1 */
    metaCustomerDelete: "meta:customer:delete",
    /** 等效命名 */
    metaNameMap: "meta:name-map",
    /** 等效命名-查看 */
    metaNameMapView: "meta:name-map:view",
    /** 等效命名-导入导出 1*/
    metaNameMapImportAndExport: "meta:name-map:batch",
    /** 等效命名-新增 1*/
    metaNameMapCreate: "meta:name-map:create",
    /** 等效命名-编辑 1*/
    metaNameMapEdit: "meta:name-map:edit",
    /** 等效命名-删除 1*/
    metaNameMapDelete: "meta:name-map:delete",
    /** 报价模板 */
    metaQuotationTemp: "meta:quotation-temp",
    /** 报价模板-查看 */
    metaQuotationTempView: "meta:quotation-temp:view",
    /** 报价模板-新增 1 */
    metaQuotationTempCreate: "meta:quotation-temp:create",
    /** 报价模板-编辑 1 */
    metaQuotationTempEdit: "meta:quotation-temp:edit",
    /** 报价模板-删除 1 */
    metaQuotationTempDelete: "meta:quotation-temp:delete"
  },

  log: {
    /** 日志管理    */
    log: "log",
    /** 日志管理 */
    logLogin: "log:login",
    /** 日志管理-查看 */
    logLoginview: "log:login:view",
    /** 接口日志 */
    logOpenApi: "log:open-api",
    /** 接口日志-查看 */
    logOpenApiView: "log:open-api:view"
  },

  /** 企业管理   */
  enterprise: {
    /** 企业管理   */
    enterprise: "enterprise",

    /** 企业信息 */
    enterpriseInfo: "enterprise:info",
    /** 企业信息-查看 */
    enterpriseInfoView: "enterprise:info:view",
    /** 企业信息-编辑 1*/
    enterpriseInfoEdit: "enterprise:info:edit",
    /**  员工管理    */
    enterpriseEmployee: "enterprise:employee",
    /** 员工管理-查看 */
    enterpriseEmployeeView: "enterprise:employee:view",
    /** 员工管理-新增 1*/
    enterpriseEmployeeCreate: "enterprise:employee:create",
    /** 员工管理-编辑 1*/
    enterpriseEmployeeEdit: "enterprise:employee:edit",
    /** 员工管理-删除 */
    enterpriseEmployeeDelete: "enterprise:employee:delete",

    /** 员工管理-重置密码 1*/
    enterpriseEmployeeResetPassword: "enterprise:employee:reset-password",

    /** 部门管理   */
    enterpriseDepartment: "enterprise:department",
    /** 部门管理-查看   */
    enterpriseDepartmentView: "enterprise:department:view",
    /** 部门管理-新增 1*/
    enterpriseDepartmentCreate: "enterprise:department:create",
    /** 部门管理-编辑 1*/
    enterpriseDepartmentEdit: "enterprise:department:edit",
    /** 部门管理-删除 1*/
    enterpriseDepartmentDelete: "enterprise:department:delete",

    /** 部门员工  */
    enterpriseDepartmentUser: "enterprise:department:user",
    /** 部门员工-查看  */
    enterpriseDepartmentUserView: "enterprise:department:view",
    /** 部门员工- 添加员工 1*/
    enterpriseDepartmentUserCreate: "enterprise:department:user:create",
    /** 部门员工- 编辑 1*/
    enterpriseDepartmentUserEdit: "enterprise:department:user:edit",
    /** 部门员工- 移除员工 1*/
    enterpriseDepartmentUserRemove: "enterprise:department:remove",

    /** 角色 */
    enterpriseRole: "enterprise:role",
    /** 角色-查看 */
    enterpriseRoleView: "enterprise:role:view",
    /** 角色-新增 1*/
    enterpriseRoleCreate: "enterprise:role:create",
    /** 角色-编辑 1*/
    enterpriseRoleEdit: "enterprise:role:edit",
    /** 角色-删除 */
    enterpriseRoleDelete: "enterprise:role:delete",

    /** 权限*/
    enterpriseRolePermission: "enterprise:role:permission",
    /** 权限-查看 1*/
    enterpriseRolePermissionView: "enterprise:role:permission:view",
    /** 权限-修改 1*/
    enterpriseRolePermissionEdit: "enterprise:role:permission:edit",

    /** 角色成员 */
    enterpriseRoleUser: "enterprise:role:user",
    /** 角色-角色选择员工 */
    enterpriseRoleUserCreate: "enterprise:role:user:create",
    /** 角色-角色  移除员工 */
    enterpriseRoleUserRemove: "enterprise:role:user:remove"
  },

  /** 报价 */
  quotation: {
    quotation: "quotation",
    /** 报价管理 */
    quotationManagement: "quotation:management",
    /** 报价管理-查看 1*/
    quotationManagementView: "quotation:management:view",
    /** 报价管理-新增 1*/
    quotationManagementCreate: "quotation:management:create",
    /** 报价管理-编辑 1*/
    quotationManagementEdit: "quotation:management:edit",
    /** 报价管理-删除 1*/
    quotationManagementDelete: "quotation:management:delete",
    /** 报价管理-重新报价 1*/
    quotationManagementExport: "quotation:management:export",
    /** 报价历史 */
    quotationHistory: "quotation:history",
    /** 报价历史-查看 */
    quotationHistoryView: "quotation:history:view",
    /** 报价历史-重新报价 1*/
    quotationHistoryrReQuote: "quotation:history:re-quote",
    /** 询价风险 */
    quotationRisk: "quotation:risk",
    /** 询价风险-查看 */
    quotationRiskView: "quotation:risk:view",
    /** 询价风险-处理 1*/
    quotationRiskHandle: "quotation:risk:handle",
    /** 询价风险-配置 */
    quotationRiskConfig: "quotation:risk:config",
    /** 询价风险-配置查看 */
    // quotationRiskConfigView: "quotation:risk:view",
    /** 风险配置-查看 1*/
    quotationRiskConfigView: "quotation:risk:config:view",
    /** 询价风险-配置- 编辑 1*/
    quotationRiskConfigEdit: "quotation:risk:config:edit"
  }
};
