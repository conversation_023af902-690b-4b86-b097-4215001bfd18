import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

/**
 * 范围操作符枚举
 */
export enum ScopeOperatorEnum {
  /**
   * 无匹配
   */
  NONE = 0,

  /**
   * 等于
   */
  EQUALS = 1,

  /**
   * 不等于
   */
  NOT_EQUALS = 2,

  /**
   * 包含
   */
  CONTAINS = 3,

  /**
   * 不包含
   */
  NOT_CONTAINS = 4,

  /**
   * 在列表
   */
  IN_LIST = 5,

  /**
   * 不在列表
   */
  NOT_IN_LIST = 6,

  /**
   * 开始于
   */
  STARTS_WITH = 7,

  /**
   * 结束于
   */
  ENDS_WITH = 8
}

/**
 * 范围操作符的描述映射
 */
export const ScopeOperatorEnumMapDesc: Record<ScopeOperatorEnum, string> = {
  [ScopeOperatorEnum.NONE]: "无匹配",
  [ScopeOperatorEnum.EQUALS]: "等于",
  [ScopeOperatorEnum.NOT_EQUALS]: "不等于",
  [ScopeOperatorEnum.CONTAINS]: "包含",
  [ScopeOperatorEnum.NOT_CONTAINS]: "不包含",
  [ScopeOperatorEnum.IN_LIST]: "在列表",
  [ScopeOperatorEnum.NOT_IN_LIST]: "不在列表",
  [ScopeOperatorEnum.STARTS_WITH]: "开始于",
  [ScopeOperatorEnum.ENDS_WITH]: "结束于"
};

export const ScopeOperatorOptions: Array<IOption> = mapDescToOptions<number>(ScopeOperatorEnumMapDesc);
