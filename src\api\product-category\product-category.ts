import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse, IProductCategory, IProductCategoryForm, IProductCategoryReq } from "@/models";

/** 查询全部产品分类 */
export const queryAllProductCategory = (params?: IProductCategoryReq) => {
  const url: string = withApiGateway("admin-api/business/productCategory");
  return http.get<IProductCategoryReq, IResponse<Array<IProductCategory>>>(url, {
    params
  });
};

/** 查询产品分类分页  */
export const queryProductCategory = (params: IProductCategoryReq) => {
  const url: string = withApiGateway("admin-api/business/productCategory");
  return http.get<IProductCategoryReq, IListResponse<IProductCategory>>(url, {
    params
  });
};

/** 根据产品分类id 查询详情 */
export const getProductCategoryById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/productCategory/${id}`);
  return http.get<string, IResponse<IProductCategory>>(url);
};

/** 新增产品分类 */
export const createProductCategory = (data: IProductCategoryForm) => {
  return http.post<IProductCategoryForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/productCategory"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑产品分类 */
export const updateProductCategory = (data: IProductCategoryForm) => {
  return http.put<IProductCategoryForm, IResponse<boolean>>(
    withApiGateway(`admin-api/business/productCategory/${data.id}`),
    { data },
    {
      showErrorInDialog: true
    }
  );
};

/** 删除产品分类根据Id */
export const deleteProductCategoryById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/business/productCategory/${id}`));
};
