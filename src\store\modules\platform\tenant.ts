import { ITenant, ITenantForm, ITenantInitReq, ITenantReq } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/platform/tenant";
import { downloadByUrl } from "@/utils/file";

export const useTenantStore = defineStore({
  id: "cx-tenant",
  state: () => ({
    tenants: [] as Array<ITenant>,
    total: 0 as number,
    tenant: {} as ITenant,
    /** 当前登录账号所含有租户 */
    currentUserTenants: [] as Array<ITenant>,
    loading: false as boolean,
    tenantForm: {} as ITenantForm
  }),
  actions: {
    async queryTenant(params?: ITenantReq) {
      this.loading = true;
      const res = await api.queryTenant(params);
      this.tenants = res.data.list;
      this.total = res.data.total;
      this.loading = false;
    },

    async addTenant(data: ITenantForm) {
      return api.addTenant(data);
    },

    async tenantInit(data: ITenantInitReq) {
      return api.tenantInit(data);
    },

    async editTenant(data: ITenantForm) {
      return api.editTenant(data);
    },

    async deleteTenant(id: string) {
      return api.deleteTenantById(id);
    },

    async getTenantDetailById(id: string) {
      const res = await api.getTenantDetailById(id);
      this.tenant = res.data;
      return res.data;
    },

    async queryTenantsByCurrentUser() {
      const res = await api.queryTenantsByCurrentUser();
      this.currentUserTenants = res.data;
    },

    async bindUserToTenant(userIds: Array<string>, tenantId: string) {
      return api.bindUserToTenant(userIds, tenantId);
    },

    async editEnterpriseInfo(data: ITenantForm) {
      return api.editEnterpriseInfo(data);
    },

    setTenantForm(tenantForm: Partial<ITenantForm>) {
      this.tenantForm = tenantForm;
    },
    downloadGatewayInterface(tenantId: string) {
      api.downloadGatewayInterface(tenantId).then(res => {
        const { name, downloadUrl } = res.data;
        downloadByUrl(downloadUrl, name);
      });
    }
  }
});
