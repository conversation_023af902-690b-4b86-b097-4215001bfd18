<template>
  <div class="loader">
    <div class="dot" />
    <div class="dot" />
    <div class="dot" />
    <div class="dot" />
    <div class="dot" />
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
.loader {
  position: absolute;
  top: 50%;
  left: 40%;
  margin-left: 10%;
  transform: translate3d(-50%, -50%, 0);
}

.dot {
  width: 24px;
  height: 24px;
  background: var(--el-color-primary);
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}

.dot:nth-child(1) {
  animation-delay: 0.1s;
  background-color: var(--el-color-primary), 50;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
  background-color: var(--el-color-primary), 100;
}

.dot:nth-child(3) {
  animation-delay: 0.3s;
  background-color: var(--el-color-primary), 150;
}

.dot:nth-child(4) {
  animation-delay: 0.4s;
  background-color: var(--el-color-primary), 200;
}

.dot:nth-child(5) {
  animation-delay: 0.5s;
  background-color: var(--el-color-primary), 250;
}

@keyframes slider {
  0% {
    transform: scale(1);
  }

  50% {
    opacity: 0.3;
    transform: scale(2);
  }

  100% {
    transform: scale(1);
  }
}
</style>
