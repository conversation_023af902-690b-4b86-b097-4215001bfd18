import { IPagingReq } from "@/models";

export interface IAccountReviewReq extends IPagingReq {
  id?: string;
  /**
   * 姓名
   */
  username?: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 所属企业
   */
  enterprise?: string;
  /**
   * 所在部门
   */
  department?: string;
  /**
   * 岗位
   */
  position?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 注册申请时间
   */
  applyTime?: string;
  /**
   * 审核时间
   */
  reviewTime?: string;
  /**
   * 审核人
   */
  reviewerName?: string;
  /**
   * 审核状态
   */
  reviewStatus?: string;
}
