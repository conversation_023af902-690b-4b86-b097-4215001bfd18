<template>
  <div class="bg-bg_color pt-[8px] px-6 flex justify-between">
    <ElForm :inline="true" :model="state.params" size="large" class="ml-6 flex-1">
      <ElFormItem label="综合搜索：" class="w-1/4">
        <ElInput class="w-full" clearable v-model="state.params.keyWords" placeholder="请输入企业名称/用户名/手机号" />
      </ElFormItem>

      <ElFormItem label="状态：" class="w-1/5">
        <ElSelect v-model="state.params.status" placeholder="请选择状态" class="w-full" clearable filterable>
          <ElOption v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem>
        <ElButton size="large" type="primary" @click="onConfirmQuery()">搜索</ElButton>
        <ElButton size="large" @click="onResetQuery()">重置</ElButton>
      </ElFormItem>
    </ElForm>
    <ElButton size="large" type="primary" :icon="Plus" @click="onAddAccountModalVis()">新增账号</ElButton>
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      size="large"
      :data="accountStore.accounts"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="accountStore.loading"
      @page-size-change="queryAccount"
      @page-current-change="queryAccount"
    >
      <template #dataPermissionType="{ row }">
        <CXTag :type="DataPermissionTypeEnumMapColor[row.dataPermissionType]">{{
          DataPermissionTypeEnumMapDesc[row.dataPermissionType]
        }}</CXTag>
      </template>
      <template #operation="data">
        <div>
          <ElButton type="primary" link @click="onEditAccountModalVis(data.row)"> 编辑 </ElButton>
          <reset-password-dialog :id="data.row.id">
            <template #trigger="{ openDialog }">
              <ElButton link type="primary" @click="openDialog"> 重置密码 </ElButton>
            </template>
          </reset-password-dialog>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>

    <el-dialog
      :title="getAccountFormModalTitle()"
      destroy-on-close
      align-center
      width="40%"
      v-model="state.accountFormModalVis"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="onCloseAccountModal()"
    >
      <AccountForm ref="accountFormRef" :isAdd="state.isAddAccount" />
      <template #footer>
        <el-button @click="onCancelAccountFormModal()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSaveEmployee()">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { reactive, ref, watch } from "vue";
import { IAccount, IAccountForm, IAccountReq, IOption } from "@/models";
import { ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import AccountForm from "./account-form.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { usePageStoreHook } from "@/store/modules/page";
import { useAccountStore } from "@/store/modules";
import { useRoute } from "vue-router";
import { StatusEnum, DataPermissionTypeEnumMapDesc, DataPermissionTypeEnumMapColor } from "@/enums";
import ResetPasswordDialog from "@/views/enterprise/employee/reset-password-dialog/index.vue";
import CXTag from "@/components/CxTag/index.vue";

const stateOptions: Array<IOption> = [
  { label: "全部", value: -1 },
  { label: "启用", value: 0 },
  { label: "禁用", value: 1 }
];

usePageStoreHook().setTitle((useRoute().meta?.title as string) || "账号管理");
const { columns } = useColumns();
const { pagination } = useTableConfig();
const accountStore = useAccountStore();
pagination.pageSize = 20;

const state = reactive<{
  accountFormModalVis: boolean;
  isAddAccount: boolean;
  params: IAccountReq;
}>({
  accountFormModalVis: false,
  isAddAccount: false,
  params: {}
});

watch(
  () => accountStore.total,
  () => {
    pagination.total = accountStore.total;
  },
  {
    immediate: true
  }
);

const saveLoading = ref<boolean>(false);
const accountFormRef = ref<InstanceType<typeof AccountForm>>();

queryAccount();

const getAccountFormModalTitle = () => (state.isAddAccount ? "新增账号" : "编辑账号");
const handleSaveEmployee = useLoadingFn(onAddAccount, saveLoading);

const onAddAccountModalVis = () => {
  state.isAddAccount = true;
  state.accountFormModalVis = true;
  accountStore.setAccountForm({ status: StatusEnum.ENABLE });
};

async function onAddAccount() {
  const formValue: IAccountForm | false = await accountFormRef.value.getValidValue().catch(() => false);

  if (!formValue) {
    return;
  }

  if (!formValue.email) {
    delete formValue.email;
  }

  if (!formValue.id) {
    await accountStore.addAccount(formValue);
  } else {
    await accountStore.editAccount(formValue);
  }

  state.accountFormModalVis = false;
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  pagination.currentPage = 1;
  queryAccount();
}

const onEditAccountModalVis = async (account: IAccount) => {
  state.accountFormModalVis = true;
  state.isAddAccount = false;
  accountStore.setAccountForm(account);
};

const onResetQuery = () => {
  state.params = {};
  pagination.currentPage = 1;
  queryAccount();
};

const onConfirmQuery = () => {
  pagination.currentPage = 1;
  queryAccount();
};

const onCancelAccountFormModal = () => {
  state.accountFormModalVis = false;
};

function queryAccount() {
  accountStore.queryAccount({
    ...state.params,
    status: state.params?.status < 0 ? undefined : state.params.status,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  });
}

const onCloseAccountModal = () => {
  accountStore.setAccountForm();
};
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
