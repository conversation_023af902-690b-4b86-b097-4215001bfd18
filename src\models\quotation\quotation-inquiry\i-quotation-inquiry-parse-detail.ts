import { ParseStatusEnum, TaskStatusEnum } from "@/enums";
import { IBase } from "@/models/i-base";

/** 询价单解析详情 */
export interface IQuotationInquiryParseDetail extends IBase {
  /**
   * 联系人
   */
  contactPerson?: string;
  /**
   * 铜价
   */
  copperPrice?: number;
  /**
   * 询价单附件id
   */
  inquiryAttachmentId?: number;
  /**
   * 询价单附件名
   */
  inquiryAttachmentName?: string;
  /**
   * 询价单附件url
   */
  inquiryAttachmentUrl?: string;
  /**
   * 询价单位id
   */
  inquiryOrgId?: number;
  /**
   * 询价时间
   */
  inquiryTime?: Date;
  /**
   * 操作人id
   */
  operatorId?: number;
  /**
   * 解析完成时间
   */
  parseFinishTime?: Date;
  /**
   * 解析状态
   */
  parseStatus?: ParseStatusEnum;
  /**
   * 产品单价版本id
   */
  productPriceVersionId?: number;
  /**
   * 报价金额
   */
  quotationAmount?: number;
  /**
   * 报价单附件id
   */
  quotationAttachmentId?: number;
  /**
   * 报价批次号
   */
  quotationBatchNo?: string;
  /**
   * 参考
   */
  reference?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 主题
   */
  subject?: string;
  /**
   * 任务状态
   */
  taskStatus?: TaskStatusEnum;
}
