<svg xmlns="http://www.w3.org/2000/svg" width="124" height="120" fill="none"><defs><filter id="a" width="135" height="101" x="-24" y="-20" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="4"/><feGaussianBlur stdDeviation="6"/><feColorMatrix values="0 0 0 0 0.868692934513092 0 0 0 0 0.904699981212616 0 0 0 0 0.877094566822052 0 0 0 0.5 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter></defs><g style="mix-blend-mode:passthrough"><rect width="120" height="120" rx="0" style="mix-blend-mode:passthrough"/><g style="mix-blend-mode:passthrough"><path fill="var(--el-color-primary-light-9)" fill-rule="evenodd" d="M23.255 33.025q.063-.365.14-.727.08-.361.173-.72.093-.357.202-.71.109-.354.232-.702.123-.348.261-.69.138-.342.29-.678.153-.336.32-.664.166-.329.346-.65.18-.32.374-.632.193-.313.4-.616.207-.303.426-.597t.45-.578q.232-.283.475-.556.243-.273.498-.535.254-.262.52-.512.265-.25.54-.488.276-.238.562-.464.285-.225.58-.437.295-.213.598-.412.304-.199.615-.384.312-.185.631-.356.32-.17.645-.327.326-.157.659-.298.332-.142.67-.269.338-.126.681-.238.343-.111.69-.207.348-.096.7-.176.35-.08.705-.144.354-.065.71-.113.357-.048.715-.08.359-.033.718-.049.36-.016.719-.016.377 0 .753.018.376.017.751.053.375.035.748.088.373.053.743.124.37.07.738.158.367.088.73.192.362.105.72.227.357.122.709.26.351.14.697.294.345.155.683.326.338.171.668.358.33.186.652.389.321.202.633.419.313.216.615.448.302.231.594.477.292.245.572.504.28.259.55.53.269.272.525.556.256.284.5.58t.474.603q.23.307.447.624.216.318.419.645.202.328.39.664.187.337.36.682.171.345.328.697.157.353.298.713.142.36.267.726.125.366.233.737.11.372.201.748.093.376.168.756.075.38.134.764.058.383.1.769.795-.129 1.6-.129.258 0 .515.013.258.014.514.04.257.026.512.066.255.04.508.092.252.053.502.119.25.065.497.144.247.078.49.169.242.091.48.194.238.104.471.22.233.115.46.242.228.127.45.266.22.139.434.289.215.15.422.31.207.162.406.333.2.171.39.352.191.182.374.373.182.19.355.39t.337.41q.163.208.317.425.153.217.296.442.144.224.276.456.133.231.254.47.122.238.232.482.11.244.209.494.098.249.185.503.087.255.162.513.075.258.137.52.063.262.113.527t.088.532q.038.267.063.536.026.269.038.538.013.27.013.54t-.013.54q-.012.27-.038.538-.025.269-.063.536-.037.267-.088.532-.05.265-.113.527-.062.262-.137.52-.075.258-.162.513-.087.254-.185.503-.099.25-.21.494-.11.244-.23.482-.122.239-.255.47-.132.232-.276.456-.143.225-.296.442-.154.217-.317.425-.164.21-.337.41-.173.2-.355.39-.183.191-.374.373-.19.18-.39.352-.2.171-.406.332-.207.161-.422.311-.214.15-.435.289-.221.139-.448.266-.228.127-.46.243-.234.115-.472.219-.238.103-.48.194-.243.091-.49.17-.247.078-.497.143-.25.066-.503.119-.252.052-.507.092t-.512.066q-.256.026-.514.04-.257.013-.515.013-.692 0-1.378-.095-.686-.095-1.354-.284-.668-.188-1.307-.467-.638-.28-1.237-.643V56H24.122v-.115Q23.315 56 22.5 56q-.282 0-.564-.014t-.563-.041q-.281-.028-.56-.07-.28-.04-.557-.096-.276-.055-.55-.124-.274-.068-.544-.15-.27-.082-.536-.177-.266-.095-.527-.203-.26-.108-.516-.23-.255-.12-.504-.253t-.491-.278q-.242-.145-.477-.302t-.462-.325q-.226-.168-.445-.347-.218-.18-.427-.37-.21-.189-.409-.388-.2-.2-.389-.41-.19-.208-.369-.427-.179-.218-.347-.445-.168-.226-.325-.46-.157-.236-.302-.478t-.278-.49q-.133-.25-.254-.505-.12-.255-.229-.516-.108-.261-.203-.527-.095-.266-.177-.536-.082-.27-.15-.544-.069-.274-.124-.55-.055-.277-.097-.557-.041-.279-.069-.56-.027-.28-.041-.563Q11 44.782 11 44.5t.014-.564q.014-.282.041-.563.028-.281.07-.56.04-.28.096-.556.055-.277.124-.551.068-.274.15-.544.082-.27.177-.536.095-.266.203-.527.108-.26.23-.516.12-.255.253-.504t.278-.491q.145-.242.302-.477t.325-.461q.168-.227.347-.446.18-.218.37-.427.189-.21.388-.409.2-.2.41-.389.208-.19.426-.369.219-.179.445-.347.227-.168.462-.325.235-.157.477-.302t.49-.278q.25-.133.505-.254.255-.12.516-.229.261-.108.527-.203.266-.095.536-.177.27-.082.544-.15.274-.069.55-.124.277-.055.557-.096.279-.042.56-.07.28-.027.563-.041.282-.014.564-.014.378 0 .755.025Z" style="mix-blend-mode:passthrough"/><g style="mix-blend-mode:passthrough"><g style="mix-blend-mode:passthrough"><g filter="url(#a)" style="mix-blend-mode:passthrough" transform="matrix(-1 0 0 1 212 0)"><rect width="87" height="53" x="106" y="41" fill="#FFF" rx="5"/></g><path fill="var(--el-color-primary-light-7)" d="M106 45.5a4.5 4.5 0 0 1 4.5-4.5h78a4.5 4.5 0 0 1 4.5 4.5V50h-87Z" style="mix-blend-mode:passthrough" transform="matrix(-1 0 0 1 212 0)"/></g><rect width="15" height="4" x="24" y="56" fill="var(--el-color-primary-light-8)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="24" y="65" fill="var(--el-color-primary-light-8)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="24" y="83" fill="var(--el-color-primary-light-8)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="24" y="74" fill="var(--el-color-primary-light-8)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="63" y="56" fill="var(--el-color-primary-light-8)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="63" y="65" fill="var(--el-color-primary-light-8)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="63" y="83" fill="var(--el-color-primary-light-8)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="63" y="74" fill="var(--el-color-primary-light-8)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="44" y="56" fill="var(--el-color-primary-light-9)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="44" y="65" fill="var(--el-color-primary-light-9)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="44" y="83" fill="var(--el-color-primary-light-9)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="44" y="74" fill="var(--el-color-primary-light-9)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="83" y="56" fill="var(--el-color-primary-light-9)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="83" y="65" fill="var(--el-color-primary-light-9)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="83" y="83" fill="var(--el-color-primary-light-9)" rx="0" style="mix-blend-mode:passthrough"/><rect width="15" height="4" x="83" y="74" fill="var(--el-color-primary-light-9)" rx="0" style="mix-blend-mode:passthrough"/></g><g style="mix-blend-mode:passthrough"><circle cx="105" cy="90" r="10" fill="var(--el-color-primary-light-5)"/><path fill="#FFF" d="M101.5 90.25a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0Zm4.75 0a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0Zm4.75 0a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0Z" style="mix-blend-mode:passthrough"/></g><path fill="var(--el-color-primary-light-9)" d="M90.5 32a4.001 4.001 0 1 0-.003-8.002A4.001 4.001 0 0 0 90.501 32ZM11 96a4.001 4.001 0 1 0-.003-8.002A4.001 4.001 0 0 0 11.001 96Zm93-60a2 2 0 1 0-.001-4.001A2 2 0 0 0 104 36Z" style="mix-blend-mode:passthrough"/></g></g></svg>