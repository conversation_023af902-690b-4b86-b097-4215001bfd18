<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="销售/备注：">
          <ElInput class="!w-[220px]" clearable placeholder="请输入销售/处理备注" />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </div> -->

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <div class="flex justify-between items-center mb-2">
        <el-radio-group class="mb-3" v-model="state.params.riskStatus" @change="onChangeRiskStatus()">
          <el-radio-button :label="RiskStatusEnum.ALLL">全部</el-radio-button>
          <el-radio-button :label="RiskStatusEnum.UNDISPOSED"
            >未处理<template v-if="state.inquiryRiskSummary?.unDisposedSize"
              >({{ state.inquiryRiskSummary?.unDisposedSize }})</template
            >
          </el-radio-button>
          <el-radio-button :label="RiskStatusEnum.DISPOSED"
            >已处理
            <template v-if="state.inquiryRiskSummary?.disposedSize"
              >({{ state.inquiryRiskSummary?.disposedSize }})</template
            >
          </el-radio-button>
        </el-radio-group>
        <div class="flex justify-between gap-4 items-center">
          <div class="text-xs flex items-center" v-if="selectTotal > 0">
            已选中:
            <div class="text-primary mx-1">{{ selectTotal }}</div>
            项目
            <div class="text-xs w-7 text-center mt-1 cursor-pointer" @click="onClearSelect()">
              <el-icon color="#606266" size="15"><CircleClose /></el-icon>
            </div>
          </div>
          <!-- <el-button type="primary" @click="onBatchHandleRisk()">批量处理</el-button> -->
          <el-button
            type="primary"
            v-auth="PermissionKey.quotation.quotationRiskConfigView"
            @click="onRedirectRiskConfig()"
            >询价风险配置</el-button
          >
        </div>
      </div>
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #checkbox="{ row }">
          <el-checkbox v-model="row.checkbox" :value="true" :disabled="row.riskStatus === RiskStatusEnum.DISPOSED" />
        </template>
        <template #highRiskSimilarity="{ row }">
          <span v-if="row.highRiskSimilarity">{{ row.highRiskSimilarity }}%</span>
        </template>
        <template #operatorName="{ row }">
          <CxEmployee :name="row.operatorName" />
        </template>
        <template #riskStatus="{ row }">
          <CXTag :type="RiskStatusEnumMapColor[row.riskStatus]">{{ RiskStatusEnumMapDesc[row.riskStatus] }}</CXTag>
        </template>
        <template #similarityInquiryOperatorName="{ row }">
          <div class="flex gap-1" v-if="row.similarityInquiryOperatorName?.length">
            <CxEmployee v-for="(item, index) in row.similarityInquiryOperatorName" :name="item" :key="index" />
          </div>
        </template>
        <template #operation="data">
          <div>
            <ElButton link type="primary" @click="onRedirectDetail(data.row.id)">详情</ElButton>
            <ElButton
              v-if="data.row.riskStatus === RiskStatusEnum.UNDISPOSED"
              link
              v-auth="PermissionKey.quotation.quotationRiskHandle"
              type="primary"
              @click="onHandleRiskRecord(data.row.id)"
              >处理</ElButton
            >
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
    <RiskRecordHandleDialog
      :id="state.id"
      :ids="state.ids"
      v-model="state.riskRecordHandleDialogVisible"
      @onSaveSuccess="handleHandleRiskSuccess()"
    />
  </div>
</template>

<script setup lang="ts" name="riskRecord">
import { onMounted, ref, reactive, computed } from "vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IInquiryRisk, IInquiryRiskReq, IInquiryRiskSummary } from "@/models";
import { ElButton } from "element-plus";
import CxEmployee from "@/components/cx-employee/index.vue";
import RiskRecordHandleDialog from "./handle/dialog.vue";
import { useRouter } from "vue-router";
import CXTag from "@/components/CxTag/index.vue";
import { getCurrentUserRiskSummary, getInquiryRiskPage } from "@/api/quotation/inquiry-risk/inquiry-risk";
import { RiskStatusEnumMapDesc, RiskStatusEnumMapColor, RiskStatusEnum } from "@/enums";
import { CircleClose } from "@element-plus/icons-vue";
import { PermissionKey } from "@/consts/permission-key";

interface IInquiryRiskExt extends IInquiryRisk {
  checkbox?: boolean;
}

const router = useRouter();
const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IInquiryRiskExt>;
  params: IInquiryRiskReq;
  id?: string;
  riskRecordHandleDialogVisible: boolean;
  inquiryRiskSummary?: IInquiryRiskSummary;
  ids: Array<string>;
}>({
  list: [],
  ids: [],
  params: {
    riskStatus: RiskStatusEnum.ALLL
  },
  riskRecordHandleDialogVisible: false
});

const selectTotal = computed(() => state.list.filter(item => item.checkbox).length);

onMounted(() => {
  handleGetCurrentUserRiskSummary();
  requestList();
});

const onClearSelect = () => {
  state.list = state.list.map(x => ({ ...x, checkbox: false }));
};

const onChangeRiskStatus = () => {
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const handleHandleRiskSuccess = () => {
  requestList();
};

const onRedirectDetail = (id: string) => {
  router.push(`/quotation/risk/detail/${id}`);
};

const onHandleRiskRecord = (id: string) => {
  state.id = id;
  state.ids = [id];
  state.riskRecordHandleDialogVisible = true;
};

const requestList = useLoadingFn(async () => {
  let params: IInquiryRiskReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  if (state.params?.riskStatus === RiskStatusEnum.ALLL) {
    delete params.riskStatus;
  }

  const { data } = await getInquiryRiskPage(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);

const handleGetCurrentUserRiskSummary = async () => {
  const { data } = await getCurrentUserRiskSummary();
  state.inquiryRiskSummary = data;
};
const onRedirectRiskConfig = () => {
  router.push("/quotation/inquiry-risk");
};
</script>
