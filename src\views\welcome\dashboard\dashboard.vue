<template>
  <div class="h-full m-6 overflow-hidden">
    <el-scrollbar>
      <div class="flex flex-col gap-5 w-full h-full min-w-[1200px]">
        <div class="statics-contianer">
          <div class="quotation">
            <StaticsTodayQuotation :data="dataStatistics" />
          </div>
          <div class="main-card">
            <StaticsCard :data="dataStatistics" />
          </div>
          <div class="monitorning">
            <StaticsAIMonitoring :data="dataStatistics" />
          </div>
        </div>
        <div class="operation-container">
          <Operation @onSelectDate="handleSelectDate($event)" @onRefresh="handleRefresh()" />
        </div>
        <div class="operational-container">
          <div class="inquire-ranking">
            <InquireRanking ref="inquireRankingRef" :dateRange="dateRange" />
          </div>
          <div class="quotation-analysis">
            <QuotationAnalysis ref="quotationAnalysisRef" />
          </div>
          <div class="sales-ranking">
            <SalesRanking ref="salesRankingRef" :dateRange="dateRange" />
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { IDataStatistics, IDateRange } from "@/models";
import InquireRanking from "./operational-dashboard/inquire-ranking.vue";
import Operation from "./operational-dashboard/operation.vue";
import QuotationAnalysis from "./operational-dashboard/quotation-analysis.vue";
import SalesRanking from "./operational-dashboard/sales-ranking.vue";
import StaticsAIMonitoring from "./statics-dashboard/statics-ai-monitoring.vue";
import StaticsCard from "./statics-dashboard/statics-card.vue";
import StaticsTodayQuotation from "./statics-dashboard/statics-today-quotation.vue";
import { onMounted, ref } from "vue";
import { getDataStatistics } from "@/api/dashboard";

const dateRange = ref<IDateRange>();
const dataStatistics = ref<IDataStatistics>();
const inquireRankingRef = ref<InstanceType<typeof InquireRanking>>();
const quotationAnalysisRef = ref<InstanceType<typeof QuotationAnalysis>>();
const salesRankingRef = ref<InstanceType<typeof SalesRanking>>();

onMounted(async () => {
  const { data } = await getDataStatistics();
  dataStatistics.value = data;
});

const handleSelectDate = (date: IDateRange) => {
  dateRange.value = date;
};

const handleRefresh = () => {
  inquireRankingRef.value.handleGetInquiryOrganizationList(dateRange.value);
  quotationAnalysisRef.value.handleGetQuotationAnalysisList();
  salesRankingRef.value.handleSalespersonRankList(dateRange.value);
};
</script>

<style lang="scss" scoped>
:deep(.el-scrollbar__view) {
  height: 100%;
}

.statics-contianer {
  width: 100%;
  display: flex;
  gap: 20px;

  .quotation {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 20px;
    width: 18%;
  }

  .monitorning {
    width: 25%;
  }

  .main-card {
    flex: 1;
  }
}

.operation-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 10px 20px;
  background: var(--el-bg-color);
}

.operational-container {
  width: 100%;
  flex: 1 1 0%;
  min-height: 300px;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  overflow: hidden;

  > div {
    background: #fff;
  }

  .inquire-ranking {
    width: 25%;
  }

  .sales-ranking {
    width: 35%;
  }

  .quotation-analysis {
    flex: 1;
  }
}
</style>
