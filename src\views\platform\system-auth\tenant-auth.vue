<template>
  <div>
    <div class="auth">
      <div class="flex justify-between items-center mb-2">
        <div class="left flex items-center">
          <div class="text-[16px] font-medium">
            {{ props?.businessLicense?.tenantName }}（{{ props?.businessLicense?.supplierCode }}）
          </div>
          <el-tag class="mr-2" type="warning" size="small" effect="dark" disable-transitions>{{
            props.businessLicense?.authorization?.editionName
          }}</el-tag>
          <div>
            <CxTag
              class="mr-1"
              size="small"
              :iconify="Checkbox"
              v-for="item in props.businessLicense?.authorization?.integrationChannel"
              :key="item"
              >{{ item }}
            </CxTag>
          </div>
          <el-tag v-if="props.businessLicense?.authorization?.expireDays <= 0" class="ml-2" type="info">已过期</el-tag>
        </div>
        <div class="flex items-center">
          <div class="text-base mr-2">
            有效截止期：
            {{ formatDate(props.businessLicense?.authorization?.expireDate, dateFormat) }}（剩余{{
              props?.businessLicense?.authorization?.expireDays
            }}天）
          </div>
          <el-button type="primary" :plain="true" @click="onReAuth()">重新授权</el-button>
        </div>
      </div>

      <div class="flex items-center text-base mb-2">
        <div class="mr-[10%]">
          <div class="mb-2">
            <span class="text-secondary">物资品类：</span>
            <span>{{ props.businessLicense?.authorization?.categoryNames?.join("，") }}</span>
          </div>
          <div>
            <span class="text-secondary">最大账号数：</span>
            <span>{{ props.businessLicense?.authorization?.accountNumber }}</span>
          </div>
        </div>

        <div>
          <div class="mb-2">
            <span class="text-secondary">物资种类：</span>
            <span>{{ props.businessLicense?.authorization?.subCategoryNames?.join("，") }}</span>
          </div>
          <div>
            <span class="text-secondary">设备数量：</span>
            <span>{{ props.businessLicense?.authorization?.deviceNumber }}</span>
          </div>
        </div>
      </div>

      <div class="flex">
        <span class="text-base mr-1 text-secondary">功能列表：</span>

        <div class="flex flex-wrap flex-1 gap-1">
          <div
            class="flex items-center"
            v-for="(feature, index) in props.businessLicense?.authorization?.featureList || []"
            :key="index"
          >
            <CxTag
              size="small"
              :iconify="feature.flag ? Checkbox : Indeterminate"
              :type="feature.flag ? 'success' : 'info'"
              >{{ feature.displayName }}
            </CxTag>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="reAuthDialogVisible"
      title="重新授权"
      width="35%"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <AuthForm
        @onCancelAuth="handleCancelAuth()"
        :businessLicense="props.businessLicense"
        @on-auth-success="handleAuthSuccess"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { IBusinessLicense } from "@/models";
import AuthForm from "./auth-form.vue";
import { ref } from "vue";
import { formatDate } from "@/utils/format";
import { dateFormat } from "@/consts";
import Indeterminate from "@iconify-icons/ri/indeterminate-circle-fill";
import Checkbox from "@iconify-icons/ri/checkbox-circle-fill";
import CxTag from "@/components/CxTag/index.vue";

const emits = defineEmits<{
  (e: "onAuthSuccess");
}>();

const props = withDefaults(
  defineProps<{
    businessLicense?: IBusinessLicense;
  }>(),
  { businessLicense: undefined }
);

const reAuthDialogVisible = ref<boolean>();

const onReAuth = () => {
  reAuthDialogVisible.value = true;
};

const handleCancelAuth = () => {
  reAuthDialogVisible.value = false;
};

const handleAuthSuccess = () => {
  reAuthDialogVisible.value = false;
  emits("onAuthSuccess");
};
</script>

<style scoped lang="scss">
.auth {
  background-color: #ffffffff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.12);
}
</style>
