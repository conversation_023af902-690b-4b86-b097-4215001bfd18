<template>
  <div class="container">
    <div class="title">用户注册申请</div>
    <template v-if="isMobile && isFollow">
      <div class="form">
        <el-form label-position="top" :label-width="80" :model="form" :rules="rules" ref="formRef">
          <el-form-item label="手机号" prop="phone" required>
            <el-input v-model="form.phone" placeholder="请输入手机号码" maxlength="20" />
          </el-form-item>

          <el-form-item label="姓名" prop="username">
            <el-input v-model="form.username" placeholder="请输入姓名" maxlength="20" />
          </el-form-item>

          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="20" />
          </el-form-item>
          <el-form-item label="所属企业" prop="enterprise">
            <el-input v-model="form.enterprise" placeholder="请输入所属企业" maxlength="20" />
          </el-form-item>

          <el-form-item label="所在部门" prop="department">
            <el-input v-model="form.department" placeholder="请输入所在部门" maxlength="20" />
          </el-form-item>
          <el-form-item label="岗位" prop="position">
            <el-input v-model="form.position" placeholder="请输入岗位" maxlength="20" />
          </el-form-item>
        </el-form>
      </div>
      <div class="reg">
        <div>
          <div class="tip">
            注册审核通过后，会收到微信消息提醒，访问
            <text class="link">https://ai.cxist.com</text>
            扫码登录
          </div>
          <el-button
            class="w-full"
            type="primary"
            :loading="loading"
            :disabled="registerResult"
            @click="handleSubmitAccountRegister()"
            >{{ registerResult ? "注册成功，请等待审核" : "注册" }}</el-button
          >
        </div>
      </div>
    </template>
    <template v-else>
      <WechatRregisterGuid :mobile="isMobile" :follow="isFollow" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { createRegister, getWeChatOpenId } from "@/api/platform/account-review";
import { IAccountRegister } from "@/models/platform/account-review";
import { ElMessage, FormRules } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { useRoute } from "vue-router";
import WechatRregisterGuid from "./wechat-rregister-guid.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getConfig } from "@/config";
import { AccountRegisterSourceEnum } from "@/enums";

const route = useRoute();
const phoneReg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const formRef = ref();
const loading = ref(false);
const isMobile = ref<boolean>(true);
const isFollow = ref<boolean>(true);
const registerResult = ref(false);
const appId = getConfig().AppId;
const registerUrl = `${window.location.host}/register`;
const weChatRedirectUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appId=${appId}&redirect_uri=${encodeURIComponent(
  registerUrl
)}&response_type=code&scope=snsapi_userinfo&state=STATE&connect_redirect=1#wechat_redirect`;

const handleSubmitAccountRegister = useLoadingFn(onSubmitReg, loading);

const form = reactive<IAccountRegister>({
  username: undefined,
  phone: undefined,
  enterprise: undefined,
  email: undefined,
  department: undefined,
  position: undefined,
  wechatUnionId: undefined,
  wechatMpOpenId: undefined,
  registerSource: AccountRegisterSourceEnum.MP
});

const rules: FormRules = {
  username: [{ required: true, trigger: "change", message: "姓名不能为空" }],
  phone: [{ validator: validatorPhone, trigger: "change", message: "手机号码不能为空" }],
  email: [{ validator: validatorEmail, trigger: "change", message: "邮箱格式不正确" }],
  enterprise: [{ required: true, trigger: "change", message: "公司名称不能为空" }],
  department: [{ required: true, trigger: "change", message: "所在部门不能为空" }],
  position: [{ required: true, trigger: "change", message: "岗位不能为空" }]
};

onMounted(async () => {
  if (!isWeChatBrowser()) {
    isMobile.value = false;
    return;
  }
  if (!route.query?.code || !route.query?.state) {
    location.href = weChatRedirectUrl;
    return;
  }
  const { code, state } = route.query;
  const { data } = await getWeChatOpenId({ code: code as string, state: state as string });
  form.wechatMpOpenId = data?.openId;
  form.wechatUnionId = data?.unionId;
  isFollow.value = data.follow;
});
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

async function onSubmitReg() {
  const validResult = await validateForm();
  if (!validResult) {
    return;
  }
  const { data } = await createRegister(form);
  registerResult.value = data;
  ElMessage.success("注册成功,请等待审核");
}

function validatorPhone(rule: any, value: any, callback: any) {
  if (!value) {
    callback(new Error("手机号码不能为空"));
    return;
  }
  if (!phoneReg.test(value)) {
    callback(new Error("手机号码格式不正确"));
    return;
  }
  callback();
}
function validatorEmail(rule: any, value: any, callback: any) {
  if (!value) {
    callback();
    return;
  }
  if (!emailRegex.test(value)) {
    callback(new Error("邮箱格式不正确"));
    return;
  }
  callback();
}

function isWeChatBrowser(): boolean {
  const ua = navigator.userAgent.toLowerCase();
  return /micromessenger/.test(ua);
}
</script>

<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: column;
  font-size: 20px;
  background-color: #ffffff;
  height: 100%;

  .title {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    padding-top: 20px;
    margin-bottom: 10px;
  }

  .form {
    flex: 1;
  }

  .reg {
    margin: 20px;

    .tip {
      margin-bottom: 10px;
      font-size: 12px;

      .link {
        color: #00b678;
      }
    }
  }
}
</style>
