import { Directive, type DirectiveBinding } from "vue";
import { useTrack } from "@/utils/useTrack";
import { useEventListener, useThrottleFn } from "@vueuse/core";

const { sendUserOperation } = useTrack();

export const track: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    addEventListener(el, binding.value, binding.arg);
  }
};

function addEventListener(el: HTMLElement, pointCode: string, event = "click") {
  if (!el || !pointCode || !event) {
    return;
  }
  return useEventListener(
    el,
    event,
    useThrottleFn(() => sendUserOperation(pointCode), 300)
  );
}
