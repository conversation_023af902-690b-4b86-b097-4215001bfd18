import { TenantStatusEnum } from "@/enums";
import { IDepartment, IDepartmentForm } from "@/models";
import { useDepartmentStore } from "@/store/modules";
import { useConfirm } from "@/utils/useConfirm";
import { ElMessage } from "element-plus";
import { reactive } from "vue";

export const useDepartmentHook = () => {
  const departmentState = reactive<{
    departmentModalVisible: boolean;
    activateDepartmentId?: string;
    department: IDepartment;
  }>({
    departmentModalVisible: false,
    activateDepartmentId: undefined,
    department: undefined
  });
  const departmentStore = useDepartmentStore();

  const setDepartmentModalVisible = (visible: boolean) => {
    departmentState.departmentModalVisible = visible;
  };

  const onDepartmentOperationShow = (data: IDepartment) => {
    departmentState.activateDepartmentId = data.id;
  };

  const onDepartmentOperationHide = () => {
    departmentState.activateDepartmentId = undefined;
  };

  const onConfirmSaveDepartment = async (department: IDepartmentForm) => {
    if (!department.id) {
      await departmentStore.addDepartment(department);
    } else {
      const departmentInfo = await departmentStore.editDepartment(department);
      departmentState.department = departmentInfo.data;
    }

    ElMessage.success(department.id ? "编辑成功" : "新增成功");
    departmentState.departmentModalVisible = false;
    await departmentStore.queryDepartmentTree();
  };

  const onCancelAddDepartmentModal = () => {
    departmentState.departmentModalVisible = false;
  };

  const onCloseAddDepartmentFormModal = () => {
    departmentState.departmentModalVisible = false;
    departmentStore.setDepartmentForm();
  };

  const onDeleteDepartment = async (department: IDepartment) => {
    if (!department || Object.keys(department).length === 0) {
      ElMessage.warning("请选择部门");
      return;
    }

    if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
      return;
    }

    await departmentStore.deleteDepartment(department.id);
    ElMessage.success("删除成功");
    if (department.id === departmentState.department?.id) {
      departmentState.department = undefined;
    }
    departmentStore.queryDepartmentTree();
  };

  const onAddSubDepartment = (department?: IDepartment) => {
    if (!department || Object.keys(department).length === 0) {
      ElMessage.warning("请选择部门");
      return;
    }

    departmentStore.setDepartmentForm({ status: TenantStatusEnum.ENABLE, parentId: department.id });
    departmentState.departmentModalVisible = true;
  };

  const onAddDepartment = () => {
    departmentStore.setDepartmentForm({ status: TenantStatusEnum.ENABLE });
    departmentState.departmentModalVisible = true;
  };

  const onEditDepartment = (department?: IDepartment) => {
    if (!department || Object.keys(department).length === 0) {
      ElMessage.warning("请选择部门");
      return;
    }
    departmentStore.setDepartmentForm({
      ...department,
      parentId: !parseInt(department.parentId) ? undefined : department.parentId
    });
    departmentState.departmentModalVisible = true;
  };

  const onChooseDepartment = (department: IDepartment, node?: { parent: { data: IDepartment } }) => {
    departmentState.department = { ...department, parentDepartment: node?.parent?.data };
  };
  return {
    departmentState,
    setDepartmentModalVisible,
    onDepartmentOperationShow,
    onDepartmentOperationHide,
    onConfirmSaveDepartment,
    onCancelAddDepartmentModal,
    onCloseAddDepartmentFormModal,
    onDeleteDepartment,
    onAddSubDepartment,
    onEditDepartment,
    onChooseDepartment,
    onAddDepartment
  };
};
