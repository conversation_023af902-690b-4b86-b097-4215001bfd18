<template>
  <el-drawer
    v-model="modelValue"
    :with-header="false"
    class="display-column"
    custom-clas="display-column"
    size="20%"
    :close-on-click-modal="false"
    style="--fullscreen-bg: #ffffff"
  >
    <div class="flex flex-col h-full">
      <div class="flex-1">
        <div class="flex items-center header">
          <el-checkbox v-model="state.selectedAll" label="全选" @change="onToggleSelectedAll($event)" />
          <div class="flex-1 text-center">显示列</div>
        </div>

        <div>
          <div class="text-base my-2">前冻结列</div>
          <draggable :list="state.preFreeze" class="freeze-col" group="a" item-key="name">
            <template #item="{ element }">
              <div class="item flex items-center justify-between">
                <el-checkbox
                  v-model="element.selected"
                  :label="element.title || element.label"
                  @change="onToggleColumnSelected($event, element)"
                />
                <MergeColumn
                  :column="element.key"
                  v-model:model-specification-merge-model-name="state.specificationMergeModelName"
                  v-model:model-voltage-level-merge-model-name="state.voltageLevelMergeModelName"
                  @update:modelSpecificationMergeModelName="
                    handleToggleModelSpecificationMergeModelName($event, element)
                  "
                  @update:modelVoltageLevelMergeModelName="handleToggleModelVoltageLevelMergeModelName($event, element)"
                />

                <el-tooltip effect="dark" content="拖动排序" placement="top" :show-after="100">
                  <el-icon class="cursor-pointer lock"><Sort /></el-icon>
                </el-tooltip>
              </div>
            </template>

            <template #footer>
              <div v-if="state.preFreeze?.length === 0" class="h-[50px] flex items-center justify-center text-base">
                前冻结列区域
              </div>
            </template>
          </draggable>
        </div>

        <div class="cols flex flex-col px-[10px] mt-2">
          <draggable :list="state.unFreeze" class="list-group" group="a" item-key="name">
            <template #item="{ element }">
              <div class="item add-freeze flex items-center justify-between">
                <el-checkbox
                  v-model="element.selected"
                  :label="element.title || element.label"
                  @change="onToggleColumnSelected($event, element)"
                />
                <MergeColumn
                  :column="element.key"
                  v-model:model-specification-merge-model-name="state.specificationMergeModelName"
                  v-model:model-voltage-level-merge-model-name="state.voltageLevelMergeModelName"
                  @update:modelSpecificationMergeModelName="
                    handleToggleModelSpecificationMergeModelName($event, element)
                  "
                  @update:modelVoltageLevelMergeModelName="handleToggleModelVoltageLevelMergeModelName($event, element)"
                />
                <el-tooltip effect="dark" content="拖动排序" placement="top" :show-after="100">
                  <el-icon class="cursor-pointer lock"><Sort /></el-icon>
                </el-tooltip>
              </div>
            </template>
          </draggable>
        </div>
        <div>
          <div class="text-base my-2">后冻结列</div>

          <div class="freeze-col">
            <draggable :list="state.afterFreeze" class="list-group" group="a" item-key="name">
              <template #item="{ element }">
                <div class="item flex items-center justify-between">
                  <el-checkbox
                    v-model="element.selected"
                    :label="element.title || element.label"
                    @change="onToggleColumnSelected($event, element)"
                  />
                  <MergeColumn
                    :column="element.key"
                    v-model:model-specification-merge-model-name="state.specificationMergeModelName"
                    v-model:model-voltage-level-merge-model-name="state.voltageLevelMergeModelName"
                    @update:modelSpecificationMergeModelName="
                      handleToggleModelSpecificationMergeModelName($event, element)
                    "
                    @update:modelVoltageLevelMergeModelName="
                      handleToggleModelVoltageLevelMergeModelName($event, element)
                    "
                  />
                  <el-tooltip effect="dark" content="拖动排序" placement="top" :show-after="100">
                    <el-icon class="cursor-pointer lock"><Sort /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <template #footer>
                <div v-if="state.afterFreeze?.length === 0" class="h-[50px] flex items-center justify-center text-base">
                  前冻结列区域
                </div>
              </template>
            </draggable>
          </div>
        </div>
      </div>
      <div class="action flex justify-end">
        <el-button @click="onCancel()">取消</el-button>
        <el-button @click="onReset()">重置</el-button>
        <el-button type="primary" @click="onConfirm()">确认</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { CheckboxValueType, Column } from "element-plus";
import { computed, reactive, watch, watchEffect } from "vue";
import { Sort } from "@element-plus/icons-vue";
import { cloneDeep, storageLocal } from "@pureadmin/utils";
import { storageLocalForQuotationSetColumns } from "@/consts";
import { FixedDir } from "element-plus/es/components/table-v2/src/constants";
import { useUserStore } from "@/store/modules/user";
import { resetDefaultDisplayColumn, specification, voltageLevel } from "./display-column";
import draggable from "vuedraggable";
import MergeColumn from "./merge-column.vue";

const emits = defineEmits<{
  (event: "update:modelValue", value: boolean);
  (e: "onSetColumn", value: Array<Column>);
}>();

const props = withDefaults(
  defineProps<{
    modelValue: boolean;
  }>(),
  {
    modelValue: false
  }
);

const state = reactive<{
  selectedAll: boolean;
  preFreeze: Array<Column>;
  unFreeze: Array<Column>;
  afterFreeze: Array<Column>;
  specificationMergeModelName: boolean;
  voltageLevelMergeModelName: boolean;
}>({
  selectedAll: false,
  preFreeze: [],
  unFreeze: [],
  afterFreeze: [],
  specificationMergeModelName: false,
  voltageLevelMergeModelName: true
});

const userStore = useUserStore();
watchEffect(() => {
  state.selectedAll =
    state.preFreeze?.every(x => x.selected) &&
    state.unFreeze?.every(x => x.selected) &&
    state.afterFreeze?.every(x => x.selected);
});

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

watch(
  () => [props.modelValue, userStore.profile],
  () => {
    let columns = storageLocal().getItem<Array<Column>>(storageLocalForQuotationSetColumns);
    if (!userStore.profile?.tenantInfo?.quotationSecondConfirm) {
      const excludeFileds = ["aiRecommendModel", "aiParseModel"];
      columns = columns?.filter(x => !excludeFileds.includes(x.dataKey as string));
    }

    if (Array.isArray(columns) && columns.length) {
      columns = columns.filter(x => !["selection", "action"].includes(x.dataKey as string));
      handleSetDefaultColumSort(columns);
      return;
    }
    handleSetDefaultColumSort(resetDefaultDisplayColumn);
  },
  {
    immediate: true
  }
);

const onToggleSelectedAll = (selected: CheckboxValueType) => {
  const isSelected = selected as boolean;
  state.preFreeze = handleToggleSelected(state.preFreeze, isSelected);
  state.unFreeze = handleToggleSelected(state.unFreeze, isSelected);
  state.afterFreeze = handleToggleSelected(state.afterFreeze, isSelected);
};

const handleToggleSelected = (list: Array<Column>, selected: boolean) => {
  return list.map(item => {
    item.selected = selected;
    item.hidden = !item.selected;
    return item;
  });
};

const onToggleColumnSelected = (selected: CheckboxValueType, column: Column) => {
  column.hidden = !selected;
  if (column.key === voltageLevel && !selected) {
    state.voltageLevelMergeModelName = false;
  } else if (column.key === specification && !selected) {
    state.specificationMergeModelName = false;
  }
};

const handleToggleModelSpecificationMergeModelName = (selected: boolean, column: Column) => {
  if (selected) {
    column.selected = true;
  }
};
const handleToggleModelVoltageLevelMergeModelName = (selected: boolean, column: Column) => {
  if (selected) {
    column.selected = true;
  }
};

const onCancel = () => {
  modelValue.value = false;
};

const onReset = () => {
  handleSetDefaultColumSort(resetDefaultDisplayColumn);
};

function handleSetDefaultColumSort(columns: Array<Column>) {
  state.preFreeze = columns.filter(x => x.preFreeze);
  state.unFreeze = columns.filter(x => !x.afterFreeze && !x.preFreeze);
  state.afterFreeze = columns.filter(x => x.afterFreeze);
  state.voltageLevelMergeModelName = columns.find(x => x.key === voltageLevel)?.voltageLevelMergeModelName;
  state.specificationMergeModelName = columns.find(x => x.key === specification)?.specificationMergeModelName;
}

const onConfirm = () => {
  let allColumns: Array<Column> = [
    ...cloneDeep(state.preFreeze).map(item => ({ ...item, fixed: FixedDir.LEFT, preFreeze: true })),
    ...cloneDeep(state.unFreeze).map(item => ({ ...item, fixed: undefined, preFreeze: false, afterFreeze: false })),
    ...cloneDeep(state.afterFreeze.map(item => ({ ...item, fixed: FixedDir.RIGHT, afterFreeze: true })))
  ];

  allColumns = allColumns.map(item => {
    if (item.key === voltageLevel) {
      item.voltageLevelMergeModelName = state.voltageLevelMergeModelName;
      item.hidden = item.selected && state.voltageLevelMergeModelName ? true : !item.selected;
    } else if (item.key === specification) {
      item.specificationMergeModelName = state.specificationMergeModelName;
      item.hidden = item.selected && state.specificationMergeModelName ? true : !item.selected;
    }
    return item;
  });

  storageLocal().setItem(storageLocalForQuotationSetColumns, allColumns);
  modelValue.value = false;
  emits("onSetColumn", allColumns);
};
</script>

<style scoped lang="scss">
.header {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 6px;
}

.freeze-col {
  border: 1px dashed #dde0e6;
  border-radius: 3px;
  min-height: 60px;
  background-color: #fafbfc;
  padding: 0 10px;

  .item {
    &:hover {
      cursor: pointer;

      .lock {
        visibility: visible;
      }
    }
  }
}

.lock {
  visibility: hidden;
}

.add-freeze {
  &:hover {
    cursor: pointer;

    .lock {
      visibility: visible;
    }
  }
}
</style>
