import {} from "@/models";
import { <PERSON>ck<PERSON>ethod } from "vite-plugin-mock";

const list = [
  {
    id: "1",
    userName: "xiaoming",
    name: "小明",
    code: "01",
    ip: "192.168.29,51",
    loginTime: "2023-06-06T07:48:41.552Z",
    device: "Windows 10/ Chrome 95.0.4638/ Other"
  },
  {
    id: "1",
    userName: "xiaoz<PERSON>",
    name: "校长",
    code: "01",
    ip: "192.168.29,51",
    loginTime: "2023-06-06T07:48:41.552Z",
    device: "Windows 10/ Chrome 95.0.4638/ Other"
  }
];

export default [
  {
    url: "/api/login-log",
    method: "get",
    response: ({ query }) => {
      const { keyWords } = query;
      return {
        code: "",
        msg: "",
        data: {
          total: 2,
          list: keyWords ? list.filter(x => x.userName.indexOf(keyWords) >= 0 || x.name.indexOf(keyWords) >= 0) : list
        }
      };
    }
  }
] as <PERSON>ckMethod[];
