import { defineStore } from "pinia";
import * as api from "@/api/menu";
import { IRoleMenu } from "@/models/menu";

export const useMenuStore = defineStore({
  id: "cx-menu",
  state: () => ({}),
  actions: {
    async queryMenuTree() {
      return api.queryMenuTree();
    },

    async getRoleMenuByRoleId(roleId: string) {
      return (await api.getRoleMenuByRoleId(roleId)).data;
    },
    async editRoleMenu(data: IRoleMenu) {
      return api.editRoleMenu(data);
    }
  }
});
