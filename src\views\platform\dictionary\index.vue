<template>
  <div class="flex h-full py-5 px-6 gap-5">
    <div class="w-1/5 bg-bg_color p-5 overflow-hidden">
      <!-- <div class="flex-bc add-dict">
        <el-button class="w-full" :icon="Plus" @click="onAddDictionary()">新增字典类型</el-button>
      </div> -->
      <el-scrollbar max-height="calc(100% - 40px)">
        <div class="dict-box">
          <div
            class="item"
            :class="{ 'activate-dict': state.activateDictionary?.id === item.id }"
            v-for="item in dictionaryStore.dictionaries || []"
            :key="item.id"
            @click="onChooseDict(item)"
          >
            <div>{{ item.name }}</div>
            <div class="flex items-center edit-icon" @click="onEditDictionary(item)">
              <el-icon><EditPen /></el-icon>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="bg-bg_color p-5 flex flex-col flex-1 overflow-hidden">
      <div class="text-right pb-3">
        <ElButton type="primary" :icon="Plus" @click="onAddDictionaryOption()">新增用户字典</ElButton>
      </div>
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        size="large"
        :data="dictionaryStore.dictionaryOptions"
        :columns="columns"
        showOverflowTooltip
      >
        <template #operation="data">
          <ElButton link type="primary" @click="onEditDictionaryOption(data.row)">编辑</ElButton>
        </template>
        <template #empty>
          <CxEmpty />
        </template>
      </PureTable>
    </div>
  </div>

  <ElDialog
    :title="dictionaryModalTitle"
    align-center
    destroy-on-close
    v-model="state.dictionaryModalVisible"
    :close-on-click-modal="false"
    class="middle"
    :close-on-press-escape="false"
    @close="onCloseDictionary()"
  >
    <DictionaryForm ref="dictionaryFormRef" />

    <template #footer>
      <el-button @click="onCancelAddDictionaryModal()">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="handleSaveDictionary()">保存</el-button>
    </template>
  </ElDialog>

  <ElDialog
    :title="dictionaryOptionModalTitle"
    destroy-on-close
    v-model="state.dictionaryOptionModalVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onCloseDictionaryOption()"
  >
    <DictionaryOptionForm ref="dictionaryOptionFormRef" />

    <template #footer>
      <el-button @click="onCancelAddDictionaryOptionModal()">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="handleSaveDictionaryOption()">保存</el-button>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
import { ElButton, ElIcon, ElMessage } from "element-plus";
import CxEmpty from "@/components/CxEmpty";
import { computed, reactive, ref, watch } from "vue";
import { EditPen } from "@element-plus/icons-vue";
import { useDictionaryStore } from "@/store/modules";
import DictionaryForm from "./dictionary-form.vue";
import DictionaryOptionForm from "./dictionary-option-form.vue";
import { IDictionary, IDictionaryForm, IDictionaryOption, IDictionaryOptionForm } from "@/models";
import { Plus } from "@element-plus/icons-vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useColumns } from "./columns";
import { StatusEnum } from "@/enums";

usePageStoreHook().setTitle((useRoute().meta?.title as string) || "用户字典");
const { columns } = useColumns();
const dictionaryStore = useDictionaryStore();

const state = reactive<{
  dictionaryModalVisible: boolean;
  activateDictionary: IDictionary;
  dictionaryOptionModalVisible: boolean;
  activateDictionaryOption: IDictionary;
}>({
  dictionaryModalVisible: false,
  activateDictionary: undefined,
  dictionaryOptionModalVisible: false,
  activateDictionaryOption: undefined
});

dictionaryStore.queryAllDictionary();

watch(
  () => dictionaryStore.dictionaries,
  dicts => {
    if (!state.activateDictionary && Array.isArray(dicts) && dicts.length) {
      state.activateDictionary = dicts[0];
      dictionaryStore.getDictionaryOptions({ parentCode: state.activateDictionary.code });
    }
  }
);

const dictionaryFormRef = ref<InstanceType<typeof DictionaryForm>>();
const dictionaryOptionFormRef = ref<InstanceType<typeof DictionaryOptionForm>>();

const saveLoading = ref<boolean>(false);

const handleSaveDictionary = useLoadingFn(onSaveDictionary, saveLoading);
const handleSaveDictionaryOption = useLoadingFn(onSaveDictionaryOption, saveLoading);

const dictionaryModalTitle = computed(() => (state.activateDictionary?.id ? "编辑字典类型" : "新增字典类型"));
const dictionaryOptionModalTitle = computed(() => (state.activateDictionaryOption?.id ? "编辑字典" : "新增字典"));

/** 字典子项管理 */

const onAddDictionaryOption = () => {
  dictionaryStore.setDictionaryOptionForm({ status: StatusEnum.ENABLE });
  state.dictionaryOptionModalVisible = true;
};

const onEditDictionaryOption = (editDictionaryOption: IDictionaryOption) => {
  if (!editDictionaryOption || Object.keys(editDictionaryOption).length === 0) {
    ElMessage.warning("请选择字典项");
    return;
  }
  state.activateDictionaryOption = editDictionaryOption;
  dictionaryStore.setDictionaryOptionForm({ ...editDictionaryOption });
  state.dictionaryOptionModalVisible = true;
};

async function onSaveDictionaryOption() {
  const dictionaryOption: IDictionaryOptionForm | false = await dictionaryOptionFormRef.value
    .getValidValue()
    .catch(() => false);
  if (!dictionaryOption) {
    return;
  }

  if (!dictionaryOption.id) {
    await dictionaryStore.addDictionaryOption({ ...dictionaryOption, parentCode: state.activateDictionary.code });
  } else {
    await dictionaryStore.editDictionaryOption(dictionaryOption);
  }

  state.dictionaryOptionModalVisible = false;
  dictionaryStore.getDictionaryOptions({ parentCode: state.activateDictionary.code });
}

const onCloseDictionaryOption = () => {
  dictionaryStore.setDictionaryOptionForm();
  state.activateDictionaryOption = undefined;
};

const onCancelAddDictionaryOptionModal = () => {
  state.dictionaryOptionModalVisible = false;
};

/** 字典管理 */

async function onSaveDictionary() {
  const dictionary: IDictionaryForm | false = await dictionaryFormRef.value.getValidValue().catch(() => false);
  if (!dictionary) {
    return;
  }

  if (!dictionary.id) {
    await dictionaryStore.addDictionary(dictionary);
  } else {
    await dictionaryStore.editDictionary(dictionary);
  }

  state.dictionaryModalVisible = false;
  dictionaryStore.queryAllDictionary();
}

const onChooseDict = (dictionary: IDictionary) => {
  state.activateDictionary = dictionary;
  dictionaryStore.getDictionaryOptions({ parentCode: state.activateDictionary.code });
};

const onEditDictionary = (dictionary: IDictionary) => {
  if (!dictionary || Object.keys(dictionary).length === 0) {
    ElMessage.warning("请选择字典");
    return;
  }
  state.activateDictionary = dictionary;
  dictionaryStore.setDictionaryForm({ ...dictionary });
  state.dictionaryModalVisible = true;
};

const onCancelAddDictionaryModal = () => {
  state.dictionaryModalVisible = false;
};

// const onAddDictionary = () => {
//   state.activateDictionary = undefined;
//   state.dictionaryModalVisible = true;
// };

const onCloseDictionary = () => {
  dictionaryStore.setDictionaryForm();
};
</script>

<style scoped lang="scss">
.add-dict {
  padding-bottom: 12px;
}

.dict-box {
  .item {
    @apply cursor-pointer text-base text-regular p-0.5 pl-2 flex-bc;

    .edit-icon {
      visibility: hidden;
      border-radius: 3px;
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;

      &:hover {
        color: var(--el-color-primary);
        // background-color: rgba(0, 0, 0, 0.06);
      }
    }

    &:hover {
      background: var(--el-fill-color-light);

      .edit-icon {
        visibility: visible;
      }
    }

    &.activate-dict {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
  }
}
</style>
