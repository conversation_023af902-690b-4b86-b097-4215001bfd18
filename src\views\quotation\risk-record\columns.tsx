import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    // {
    //   label: "选择",
    //   prop: "checkbox",
    //   slot: "checkbox",
    //   width: ColumnWidth.Char2
    // },
    {
      label: "告警时间",
      prop: "riskTime"
    },
    {
      label: "销售人员",
      prop: "operatorName",
      slot: "operatorName"
    },
    {
      label: "询价单位",
      prop: "inquiryOrgName"
    },
    {
      label: "相似度",
      prop: "highRiskSimilarity",
      slot: "highRiskSimilarity",
      align: "center"
    },
    {
      label: "相似询价单数量",
      prop: "similarityInquiryTaskSize",
      align: "center"
    },
    {
      label: "关联销售",
      prop: "similarityInquiryOperatorName",
      slot: "similarityInquiryOperatorName"
    },
    {
      label: "处理状态",
      prop: "riskStatus",
      slot: "riskStatus"
    },
    {
      label: "处理时间",
      prop: "riskDisposeTime"
    },
    {
      label: "备注",
      prop: "riskRemark"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char6
    }
  ];
  return { columns };
}
