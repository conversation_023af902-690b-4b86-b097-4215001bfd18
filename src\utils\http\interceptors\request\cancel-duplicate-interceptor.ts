import { abortControllerManager } from "../abort-controller-manager";
import { PureHttpRequestConfig } from "@/utils/http/types";
import { AxiosInstance } from "axios";

export function cancelDuplicateInterceptor(instance: AxiosInstance): number {
  return instance.interceptors.request.use((config: PureHttpRequestConfig) => {
    const duplicateKey = config.duplicateKey;
    if (duplicateKey) {
      if (config.signal) {
        console.error("请求存在signal，无法取消重复");
      } else {
        abortControllerManager.abort(duplicateKey);
        config.signal = abortControllerManager.generateSignal(duplicateKey);
      }
    }
    return config;
  });
}
