<template>
  <div class="w-full edit-cell" :class="{ 'edit-model': !isEdit }">
    <div v-show="!isEdit" class="flex flex-1 h-full w-full items-center justify-end" @click="onToggelEdit()">
      <slot>{{ value }}</slot>
    </div>
    <el-input-number
      v-show="isEdit"
      v-model="value"
      :controls="false"
      :precision="props.precision"
      class="discount-price"
      @blur="onEditRowBlur()"
      :disabled="props.disabled"
    />
  </div>
</template>

<script setup lang="ts">
import { IQuotationInquiryDetail } from "@/models";
import { ref, watch } from "vue";
import { quotationInquiryEditItem } from "@/api/quotation/quotation-inquiry";
import { ElMessage } from "element-plus";

const emits = defineEmits<{
  (e: "onEditSuccess"): void;
}>();

const props = withDefaults(
  defineProps<{
    field?: string;
    data: IQuotationInquiryDetail;
    disabled: boolean;
    precision?: number;
    handleOnEditSuccess?: Function;
  }>(),
  {
    data: () => ({}),
    disabled: true
  }
);

const isEdit = ref(true);
const value = ref();

watch(
  () => props.data,
  data => {
    if (data && Object.keys(data).length > 0) {
      value.value = data[props.field];
    }
  },
  {
    immediate: true,
    deep: true
  }
);

const onToggelEdit = () => {
  if (props.disabled) {
    return;
  }
  isEdit.value = !isEdit.value;
};

const onEditRowBlur = async () => {
  try {
    await quotationInquiryEditItem({ ...props.data, [props.field]: value.value });
    emits("onEditSuccess");
    props.handleOnEditSuccess && props.handleOnEditSuccess();
    onToggelEdit();
  } catch (_error) {
    ElMessage.warning("修改失败");
  }
};
</script>

<style scoped lang="scss">
.edit-cell {
  height: 100%;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  &.edit-model {
    padding-right: 6px;

    &:hover {
      border: 1px dotted var(--el-color-primary);
    }
  }

  .edit {
    width: 30px;
    text-align: center;
    visibility: hidden;
  }

  &:hover {
    .edit {
      visibility: visible;
      cursor: pointer;
      width: 30px;
      text-align: center;
      color: var(--el-color-primary);
    }
  }
}
</style>
