<template>
  <div>
    <el-dialog
      v-model="modelValue"
      title="粘贴型号规格"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
    >
      <PasteSpecification ref="pasteSpecificationRef" @onChangeContent="handleChangeContent($event)" />
      <template #footer>
        <span>
          <el-button :disabled="loading" @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">解析并保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import PasteSpecification from "./index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { pasteContentInquiry } from "@/api/quotation/quotation-inquiry";
import { useVersionStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { QuotationInquiryParseEnum } from "@/enums/quotation-inquiry-parse-source-type-enum";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onAnalysisSuccess", val: string): void;
}>();

let pastedContent: string;
const versionStore = useVersionStore();

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});
const loading = ref(false);
const pasteSpecificationRef = ref<InstanceType<typeof PasteSpecification>>();
const handleSaveBtn = useLoadingFn(onSave, loading);

const handleChangeContent = (content: string) => {
  pastedContent = content;
};

/**
 *  保存按钮点击事件
 */
async function onSave() {
  if (!pastedContent) {
    ElMessage.warning("型号规格不能为空");
    return;
  }
  const { data } = await pasteContentInquiry({
    versionId: versionStore.productVersion?.id,
    pastedContent,
    sourceType: QuotationInquiryParseEnum.WEB
  });
  emits("onAnalysisSuccess", data);
  modelValue.value = false;
}
function closeDialog() {
  modelValue.value = false;
}
</script>

<style scoped></style>
