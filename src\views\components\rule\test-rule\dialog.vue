<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" />
    <el-dialog
      v-model="drawerVisible"
      title="规则测试"
      width="60%"
      class="!mt-[6vh]"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog()"
    >
      <TestRule class="h-[70vh]" :ruleScope="props.ruleScope" />
      <template #footer>
        <el-button type="primary" @click="closeDialog">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import TestRule from "./index.vue";
import { RuleScopeEnum } from "@/enums";

const props = defineProps<{
  ruleScope: RuleScopeEnum;
}>();

const drawerVisible = ref(false);

// 订阅弹窗开启状态，请求数据
watch(drawerVisible, async visible => {
  if (!visible) {
    return;
  }
});

/**
 * @description: 开启dialog
 */
function openDialog() {
  drawerVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  drawerVisible.value = false;
}
</script>

<style scoped></style>
