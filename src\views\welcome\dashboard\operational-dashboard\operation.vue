<template>
  <div class="headline">运营看板</div>
  <div class="content">
    <div class="button-group">
      <el-button v-for="button in buttons" :key="button.text" text @click="onSelectDate(button.range)">
        {{ button.text }}
      </el-button>
    </div>
    <el-date-picker
      v-model="dateValue"
      type="daterange"
      range-separator="To"
      :clearable="false"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      @change="onChangeDate($event)"
    />
    <el-button class="reset-btn" :icon="RefreshLeft" @click="onRefresh()" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { RefreshLeft } from "@element-plus/icons-vue";
import { IDateRange } from "@/models";
import dayjs from "dayjs";
import { fullDateFormat } from "@/consts";

const emits = defineEmits<{
  (event: "onSelectDate", value: IDateRange);
  (event: "onRefresh");
}>();
const dateValue = ref<[string, string]>(["", ""]);
const buttons = ref([]);
const now = dayjs();
onMounted(() => {
  buttons.value = [
    {
      text: "30天内",
      range: {
        startDay: now.subtract(30, "day").startOf("day").format(fullDateFormat),
        endDay: now.format(fullDateFormat)
      }
    },
    {
      text: "90天内",
      range: {
        startDay: now.subtract(90, "day").startOf("day").format(fullDateFormat),
        endDay: now.format(fullDateFormat)
      }
    },
    {
      text: "180天内",
      range: {
        startDay: now.subtract(180, "day").startOf("day").format(fullDateFormat),
        endDay: now.format(fullDateFormat)
      }
    }
  ];
  const startDay = now.startOf("day").format(fullDateFormat);
  const endDay = now.endOf("day").format(fullDateFormat);
  dateValue.value = [startDay, endDay];
  handleSelectDate({ startDay, endDay });
});

const onChangeDate = (event: any) => {
  if (!event) {
    handleSelectDate({});
    return;
  }
  const [startDay, endDay] = event;
  handleSelectDate({
    startDay: dayjs(startDay).startOf("day").format(fullDateFormat),
    endDay: dayjs(endDay).endOf("day").format(fullDateFormat)
  });
};

const onSelectDate = (data: IDateRange) => {
  dateValue.value = [data.startDay, data.endDay];
  handleSelectDate(data);
};

function handleSelectDate(data?: IDateRange) {
  emits("onSelectDate", data);
}

function onRefresh() {
  emits("onRefresh");
}
</script>

<style lang="scss" scoped>
.headline {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.reset-btn {
  padding: 8px;
}
</style>
