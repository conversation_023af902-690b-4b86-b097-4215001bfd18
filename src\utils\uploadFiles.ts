import { isEmpty } from "lodash-unified";

/**
 * 文件上传时校验文件类型
 */
export function validUploadFileType(currentFileType: string, ruleFileType: string[]): boolean {
  // 没有类型的文件
  if (!currentFileType) {
    return true;
  }
  const pos = currentFileType?.lastIndexOf("/");
  const fileType = currentFileType?.substring(pos + 1);
  return !isEmpty(ruleFileType) && !ruleFileType.some(item => item.includes(fileType));
}
