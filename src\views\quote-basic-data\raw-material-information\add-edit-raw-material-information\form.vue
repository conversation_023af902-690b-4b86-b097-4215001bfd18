<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="原材料类型" prop="type">
          <el-input v-model="form.type" clearable placeholder="请输入原材料类型" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label=" 材料编码" prop="materialCode">
          <el-input v-model="form.materialCode" clearable placeholder="请输入 材料编码" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="原材料名称" prop="rawName">
          <el-input v-model="form.rawName" clearable placeholder="请输入原材料名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="原材料型号" prop="model">
          <el-input v-model="form.rawModel" clearable placeholder="请输入原材料型号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="原材料规格" prop="rawSpec">
          <el-input v-model="form.rawSpec" clearable placeholder="请输入原材料规格" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计量单位" prop="productUnit">
          <SelectProductUnit
            class="!w-full"
            v-model="form.productUnit"
            placeholder="请选择计量单位"
            :disabled="!!form.id"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="原材料单价成本" prop="price">
          <el-input-number
            class="!w-full"
            v-model="form.price"
            :min="0"
            controls-position="right"
            placeholder="请输入原材料单价成本"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IRawMaterialInformationForm } from "@/models";
import SelectProductUnit from "@/views/components/select-product-unit/index.vue";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});

const form = reactive<IRawMaterialInformationForm>({});
const formRef = ref<FormInstance>();

const rules: FormRules = {
  rawName: [{ required: true, trigger: "change", message: "原材料名称不能为空" }],
  rawSpec: [{ required: true, trigger: "change", message: "原材料规格不能为空" }],
  productUnit: [{ required: true, trigger: "change", message: "计量单位不能为空" }],
  price: [{ required: true, trigger: "change", message: "原材料单价成本不能为空" }]
};

onMounted(() => {});

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IRawMaterialInformationForm) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}
</script>

<style scoped></style>
