import { withApiGateway } from "../util";
import { http } from "@/utils/http";
import { IResponse } from "@/models";
import { getToken } from "@/utils/auth";

/** 新增操作日志记录 */
export const addTrackRecord = (pointCode: string) => {
  const url = withApiGateway(`admin-api/infra/track/addTrackRecordV2`);
  const data = {
    pointCode,
    authorization: getToken()?.accessToken
  };
  if (navigator?.sendBeacon) {
    const blob = new Blob([JSON.stringify(data)], { type: "application/json" });
    return navigator.sendBeacon(url, blob);
  }
  return http.post<{ pointCode: string; authorization?: string }, IResponse<boolean>>(url, { data });
};
