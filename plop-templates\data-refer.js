module.exports.data = {
  desc: "质量追溯",
  fields: {
    name: {
      type: "string",
      desc: "企业名称",
      column: true,
      form: true,
      formFieldConfig: {
        label: "企业名称",
        placeholder: "请输入企业名称",
        type: "string",
        rule: { required: true, trigger: "change", message: "企业名称不能为空" }
      },
      search: true,
      searchFieldConfig: {
        label: "企业名称",
        placeholder: "请输入企业名称",
        type: "string"
      }
    },
    unit: {
      type: "string",
      desc: "单元",
      column: true,
      form: true,
      formFieldConfig: {
        label: "单元",
        placeholder: "请输入单元",
        rule: { required: true, trigger: "change", message: "单元不能为空" }
      },
      search: true,
      searchFieldConfig: {
        label: "单元",
        placeholder: "请输入单元名称",
        type: "string"
      }
    },
    level: {
      type: "number",
      desc: "等级",
      column: true,
      form: true,
      formFieldConfig: {
        label: "等级",
        type: "number",
        placeholder: "请输入等级",
        rule: { required: true, trigger: "change", message: "等级不能为空" }
      },
      search: true,
      searchFieldConfig: {
        type: "number",
        label: "等级",
        placeholder: "请输入等级名称"
      }
    },
    publishDate: {
      type: "string",
      desc: "发布日期",
      column: true,
      form: true,
      formFieldConfig: {
        label: "发布日期",
        type: "date",
        placeholder: "请选择发布日期",
        rule: { required: true, trigger: "change", message: "发布日期不能为空" }
      },
      search: true,
      searchFieldConfig: {
        type: "date",
        label: "等级",
        placeholder: "请选择发布日期"
      }
    },
    remark: {
      type: "string",
      desc: "备注",
      form: true,
      formFieldConfig: {
        label: "备注",
        type: "textarea",
        placeholder: "请输入备注",
        rule: { required: true, trigger: "change", message: "备注不能为空" }
      }
    }
  },
  needSearch: true,
  needCreateOrEdit: true,
  needDelete: true,
  formLineNumber: 1,
  // 默认src路径，自定义路径为false 如果要改成生成的文件都需要再一个文件夹下，比如src/view/xxx 则需要把src=src/view/xxx isDefaultPath=true
  pathPrefix: "src"
};
