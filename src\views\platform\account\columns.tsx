import { fullDateFormat } from "@/consts";
import { ColumnWidth, TableWidth } from "@/enums";
import { IAccount } from "@/models";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "用户名",
      prop: "username",
      width: TableWidth.name
    },
    {
      label: "姓名",
      prop: "nickname",
      width: TableWidth.name
    },
    {
      label: "手机号",
      prop: "mobile",
      width: TableWidth.type
    },
    {
      label: "邮箱",
      prop: "email",
      width: TableWidth.name
    },
    {
      label: "所属企业",
      prop: "tenantIds",
      minWidth: TableWidth.largeName,
      formatter: (row: IAccount) => row.tenantNames?.join("、")
    },
    {
      label: "账号类型",
      prop: "dataPermissionType",
      slot: "dataPermissionType",
      width: ColumnWidth.Char6
    },
    {
      label: "状态",
      prop: "status",
      cellRenderer(data: TableColumnRenderer) {
        const status: boolean = data.row.status;
        return status ? <CxTag type="danger">禁用</CxTag> : <CxTag type="success">启用</CxTag>;
      }
      // formatter: (row: ITenant) => formatEnum(row.status, StatusEnum, "StatusEnum")
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operations
    }
  ];
  return { columns };
}
