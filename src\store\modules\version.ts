import * as productVersionApi from "@/api/product-version";
import { getConfig } from "@/config";
import { IProductVersion } from "@/models";
import { defineStore } from "pinia";

export const useVersionStore = defineStore({
  id: "cx-version",
  state: () => ({
    version: getConfig().Version,
    productVersion: {} as IProductVersion
  }),
  getters: {
    getVersion() {
      return this.version;
    },
    async getProductLatestVersion(state) {
      if (state.productVersion && Object.keys(state.productVersion).length) {
        return state.productVersion;
      } else {
        const { data } = await productVersionApi.getLatestVersion();
        state.productVersion = data || {};
      }
      return state.productVersion || {};
    }
  },
  actions: {
    async getLatestVersion() {
      const { data } = await productVersionApi.getLatestVersion();
      this.productVersion = data || {};
    },
    setLatestVersion(productVersion: IProductVersion) {
      this.productVersion = productVersion;
    }
  }
});
