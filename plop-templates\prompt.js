const {
  getMode,
  getTableFields,
  getSearchFields,
  getFormFields,
  getSpan,
  getFormNumberOfSubarrays
} = require("./util/get-model");
const { data: modelData } = require("./data.js");
const { getEnum, getEnumNameArr } = require("./util/get-enum");
const { getCombinedPath } = require("./util/combined-path");

module.exports = {
  description: "新建一个页面",
  prompts: [
    {
      type: "input",
      name: "name",
      message: "页面名称:",
      validate(name) {
        if (!name) {
          return "请输入页面名称";
        }
        return true;
      }
    }
  ],
  actions: data => {
    // 格式为:xx-yy-tt
    const name = "{{dashCase name}}";

    const formFields = getFormFields();
    let actions = [
      {
        type: "add",
        path: getCombinedPath(`${name}/index.vue`, modelData.viewPathPrefix),
        templateFile: "plop-templates/view/view.txt",
        data: {
          cols: getMode(),
          searchFields: getSearchFields(),
          needSearch: modelData.needSearch,
          needCreateOrEdit: modelData.needCreateOrEdit,
          needDelete: modelData.needDelete
        }
      },
      // 表格列
      {
        type: "add",
        path: getCombinedPath(`${name}/columns.tsx`, modelData.viewPathPrefix),
        templateFile: "plop-templates/view/columns.txt",
        data: {
          tableFields: getTableFields()
        }
      },
      // 新增 模型文件夹 和 模型
      {
        type: "add",
        path: getCombinedPath(`${name}/i-${name}.ts`, modelData.modelPathPrefix),
        templateFile: "plop-templates/model/model.txt",
        data: {
          cols: getMode(),
          formFields: formFields,
          needCreateOrEdit: modelData.needCreateOrEdit,
          enumArr: getEnumNameArr(getMode())
        }
      },
      // 新增 导出当前文件夹 模型
      {
        type: "add",
        path: getCombinedPath(`${name}/index.ts`, modelData.modelPathPrefix),
        templateFile: "plop-templates/model/inside-index.txt"
      },
      // 追加 导出当前文件夹
      {
        type: "append",
        path: getCombinedPath(`index.ts`, modelData.modelPathPrefix),
        templateFile: "plop-templates/model/outside-index.txt"
      },
      ...getEnum(getMode()).map(item => ({
        type: "add",
        path: getCombinedPath(`enums/${item.prop}.enum.ts`, 'src'),
        templateFile: "plop-templates/enum/enum.txt",
        data: {
          enums: item.enums,
          enumName: item.prop
        }
      })),

      ...getEnum(getMode()).map(item => ({
        type: "append",
        path: getCombinedPath(`enums/index.ts`, 'src'),
        templateFile: "plop-templates/enum/index.txt",
        data: {
          enumName: `${item.prop}.enum`
        }
      })),
      // 新增 api 文件夹 和api
      {
        type: "add",
        path: getCombinedPath(`${name}/${name}.ts`, modelData.apiPathPrefix),
        templateFile: "plop-templates/api/api.txt",
        data: {
          desc: modelData.desc
        }
      },
      // 新增 api 文件夹 导出api
      {
        type: "add",
        path: getCombinedPath(`${name}/index.ts`, modelData.apiPathPrefix),
        templateFile: "plop-templates/api/inside-index.txt"
      },
      // 追加 api 统一导出 新增api文件夹
      {
        type: "append",
        path: getCombinedPath(`index.ts`, modelData.apiPathPrefix),
        templateFile: "plop-templates/api/outside-index.txt"
      }
    ];

    //#region  暂时不生成
    let cpsName = "{{properCase cpsName}}";
    const cpsItem = [
      {
        type: "add",
        path: `src/views/${name}/components/${cpsName}/index.vue`,
        templateFile: "plop-templates/view/components/index.cps.vue"
      },
      {
        type: "add",
        path: `src/views/${name}/components/${cpsName}/index.scss`,
        templateFile: "plop-templates/view/components/index.cps.scss"
      }
    ];

    const routerItem = [
      {
        type: "append",
        pattern: /routes*\:* \[/,
        path: "src/router/index.ts",
        templateFile: "plop-templates/router/index.txt",
        data: {
          name: "{{name}}",
          menu: "{{menu}}"
        }
      }
    ];

    //#endregion

    let formAction = [];
    // 判断是否需要表单
    if (modelData.needCreateOrEdit) {
      formAction = [
        // 新增 表单模型
        {
          type: "add",
          path: getCombinedPath(`${name}/i-${name}-form.ts`, modelData.modelPathPrefix),
          templateFile: "plop-templates/model/model-form.txt",
          data: {
            formFields: formFields
          }
        },

        // 新增 导出模型表单
        {
          type: "append",
          path: getCombinedPath(`${name}/index.ts`, modelData.modelPathPrefix),
          templateFile: "plop-templates/model/inside-form-index.txt"
        },

        {
          type: "add",
          path: getCombinedPath(`${name}/add-edit-{{name}}/form.vue`, modelData.viewPathPrefix),
          templateFile: "plop-templates/view/add-edit-form/form.txt",
          data: {
            formFields: getFormNumberOfSubarrays(formFields),
            span: getSpan(),
            existRule: formFields.some(x => x.formFieldConfig?.rule && Object.keys(x.formFieldConfig?.rule).length),
            existFle: formFields.some(x => x.formFieldConfig?.type && x.formFieldConfig?.type === 'file'),
            labelPosition: modelData.labelPosition
          }
        },
        {
          type: "add",
          path: getCombinedPath(`${name}/add-edit-{{name}}/dialog.vue`, modelData.viewPathPrefix),
          templateFile: "plop-templates/view/add-edit-form/dialog.txt",
          data: {
            formIsDialog: modelData.formIsDialog,
            formIsDrawer: !modelData.formIsDialog,
          }
        }
      ];
    }

    let searchModel = [];
    if (modelData.needSearch) {
      searchModel = [
        {
          type: "add",
          path: getCombinedPath(`${name}/i-${name}-req.ts`, modelData.modelPathPrefix),
          templateFile: "plop-templates/model/model-req.txt",
          data: {
            reqFields: getSearchFields()
          }
        },
        {
          type: "append",
          path: getCombinedPath(`${name}/index.ts`, modelData.modelPathPrefix),
          templateFile: "plop-templates/model/model-req-index.txt"
        }
      ];
    }

    if (searchModel.length) {
      actions = [...actions, ...searchModel];
    }

    if (formAction.length) {
      actions = [...actions, ...formAction];
    }

    if (data.wantCps && data.wantRouter) {
      return [...actions, ...cpsItem, ...routerItem];
    } else if (data.wantCps) {
      return [...actions, ...cpsItem];
    } else if (data.wantRouter) {
      return [...actions, ...routerItem];
    }
    return actions;
  }
};
