import { AccountRegisterReviewStatusEnum, AccountRegisterSourceEnum } from "@/enums";
import { IBase } from "@/models";

export interface IAccountReview extends IBase {
  /**
   * 姓名
   */
  username?: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * OpenId
   */
  wechatOpenId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 所属企业
   */
  enterprise?: string;
  /**
   * 所在部门
   */
  department?: string;
  /**
   * 岗位
   */
  position?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 注册申请时间
   */
  applyTime?: string;
  /**
   * 审核时间
   */
  reviewTime?: string;
  /**
   * 审核人
   */
  reviewerId?: string;
  /**
   * 审核人
   */
  reviewerName?: string;
  /**
   * 审核状态
   */
  reviewStatus?: AccountRegisterReviewStatusEnum;
  /**
   * 审核备注
   */
  reviewRemark?: string;
  /**
   * 指定租户
   */
  tenantId?: string;

  /** 审核来源 */
  registerSource: AccountRegisterSourceEnum;

  wechatUnionId?: string;
  wechatMaOpenId?: string;
  wechatMpOpenId?: string;
}
