<template>
  <el-select class="!w-full" v-model="modelValue" clearable :placeholder="placeholder" :filterable="filterable">
    <el-option v-for="item in allProduct || []" :key="item.id" :label="item.productCode" :value="item.productCode" />
  </el-select>
</template>

<script setup lang="ts">
import { queryAllProductByModelId } from "@/api/product";
import { IProduct } from "@/models";
import { computed, ref, watch } from "vue";

const emits = defineEmits<{
  (e: "update:modelValue", val?: string): void;
}>();
const allProduct = ref<Array<IProduct>>([]);

const props = withDefaults(
  defineProps<{
    modelValue?: string;
    placeholder?: string;
    modelId?: string;
    filterable?: boolean;
  }>(),
  {
    placeholder: "请选择型号",
    filterable: true
  }
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: string) {
    emits("update:modelValue", val);
  }
});

watch(
  () => props.modelId,
  async (modelId: string) => {
    modelValue.value = null;
    if (modelId) {
      const { data } = await queryAllProductByModelId(props.modelId);
      allProduct.value = data;
    } else {
      allProduct.value = [];
    }
  }
);
</script>
