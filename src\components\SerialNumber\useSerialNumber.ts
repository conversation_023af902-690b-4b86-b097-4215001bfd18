import { ref, Ref } from "vue";
import { getSerialNumber, getSerialNumberByParent, getSerialNumberEditable } from "@/api/serial-number";

const editableMap: Map<string, Ref<boolean>> = new Map();
const fetching = new Set<string>();

export function useSerialNumber() {
  function getEditable(code: string): Ref<boolean> {
    fetchEditable(code);
    return editableMap.get(code);
  }

  function generateNo(code: string, parentNo?: string) {
    if (parentNo) {
      return getSerialNumberByParent(code, parentNo).then(res => res.data);
    }
    return getSerialNumber(code).then(res => res.data);
  }

  function fetchEditable(code) {
    const shouldFetch = !editableMap.get(code) && !fetching.has(code);
    if (!shouldFetch) {
      return;
    }
    const editableRef = ref(false);
    editableMap.set(code, editableRef);
    fetching.add(code);
    getSerialNumberEditable(code)
      .then(res => (editableRef.value = res.data))
      .finally(() => fetching.delete(code));
  }

  return {
    getEditable,
    generateNo
  };
}
