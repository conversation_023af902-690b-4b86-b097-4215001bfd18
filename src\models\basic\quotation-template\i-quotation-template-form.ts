import { StatusEnum } from "@/enums";
import { IOption } from "@/models/i-option";
import { mapDescToOptions } from "@/utils/enum";

export interface IQuotationTemplateForm {
  id?: string;
  /**
   * 模板编号
   */
  templateCode?: string;
  /**
   * 启用状态
   */
  status?: StatusEnum;
  /**
   * 备注
   */
  remark?: string;

  /**询价单附件 */
  inquiryAttachmentId?: string;
}

export const QuotationTemplateStatusMapDesc: Record<StatusEnum, string> = {
  [StatusEnum.ENABLE]: "启用",
  [StatusEnum.DISABLE]: "未启用"
};

export const QuotationTemplateStatusOption: Array<IOption> = mapDescToOptions(QuotationTemplateStatusMapDesc).reverse();
