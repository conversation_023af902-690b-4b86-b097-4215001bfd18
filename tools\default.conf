client_max_body_size 1000M;
client_body_buffer_size 1000M;
server {
    listen 80;
    sendfile on;
    default_type application/octet-stream;
    gzip on;
    gzip_http_version 1.1;
    gzip_disable "MSIE [1-6]\.";
    gzip_min_length 256;
    gzip_vary on;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_comp_level 6;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, DELETE, PUT, PATCH, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
    root /usr/share/nginx/html;
    location / {
        try_files $uri $uri/ /index.html =404;
    }

    location /api {
        rewrite /api/?(.*)$ /$1 break;
        proxy_pass ${API_HOST};
        proxy_set_header Host $host;
        proxy_set_header Cookie $http_cookie;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_redirect default;
        proxy_buffer_size         128k;
    	proxy_buffers             4 256k;
    	proxy_busy_buffers_size   256k;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Headers X-Requested-With;
        add_header Access-Control-Allow-Methods GET,POST,DELETE,PUT,PATCH,OPTIONS;
    }

    location ~/socket.io/(.*) {
        set $new_cookie $http_cookie;
        if ($http_cookie ~* "(.*)(?:^|;\s*)io=[^;]*(.*)") {
            set $new_cookie $1$2;
        }
        proxy_set_header Cookie $new_cookie;
        proxy_pass ${WEBSOCKET_HOST};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $http_host;
        proxy_set_header X-NginX-Proxy true;
        proxy_hide_header Access-Control-Allow-Origin;
        proxy_redirect off;
    }
}
