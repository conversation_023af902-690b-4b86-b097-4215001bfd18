<template>
  <div class="inline-block">
    <el-dialog
      v-model="modelValue"
      title="风险处理"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="40%"
    >
      <RiskRecordHandle ref="formRef" :risk-id="props.id" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import RiskRecordHandle from "./index.vue";
import { handleBatchInquiryRisk } from "@/api/quotation/inquiry-risk/inquiry-risk";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onSaveSuccess"): void;
}>();

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    id?: string;
    ids?: Array<string>;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});
const loading = ref(false);
const formRef = ref<InstanceType<typeof RiskRecordHandle>>();
const handleSaveBtn = useLoadingFn(onSave, loading);

/**
 *  保存按钮点击事件
 */
async function onSave() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }

  const formVal = formRef.value.getFormValue();
  formVal.riskIdList = props.ids;
  await handleBatchInquiryRisk(formVal);
  closeDialog();
  emits("onSaveSuccess");
}
function closeDialog() {
  modelValue.value = false;
}
</script>

<style scoped></style>
