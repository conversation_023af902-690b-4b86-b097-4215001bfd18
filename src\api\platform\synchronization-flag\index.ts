import {
  ISynchronizationFlagReq,
  ISynchronizationFlag,
  ISynchronizationFlagForm,
  IListResponse,
  IResponse
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../../util";

/** 同步标识检索 */
export const querySynchronizationFlag = (params: ISynchronizationFlagReq) => {
  return http.post<ISynchronizationFlagReq, IListResponse<ISynchronizationFlag>>(
    withApiGateway("admin-api/system/syncFlag/getPage"),
    { data: { ...params } }
  );
};

/** 同步标识新增 */
export const createSynchronizationFlag = (data: ISynchronizationFlagForm) => {
  return http.post<ISynchronizationFlagForm, IResponse<string>>(
    withApiGateway("admin-api/system/syncFlag/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 同步标识编辑修改 */
export const editSynchronizationFlag = (data: ISynchronizationFlagForm) => {
  return http.put<ISynchronizationFlagForm, IResponse<boolean>>(
    withApiGateway(`admin-api/system/syncFlag/update/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};
