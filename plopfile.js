const viewGenerator = require("./plop-templates/prompt");

module.exports = plop => {
  plop.setGenerator("view", viewGenerator);
  plop.setHelper("equal", function (a, b) {
    return a === b;
  }),
    plop.setHelper("or", function (a, b) {
      return a || b;
    }),
    plop.setHelper("formRule", function (rules) {
      const formFields = [];
      rules.forEach(children => {
        children.forEach(child => {
          formFields.push(child);
        });
      });

      let formRule = formFields.filter(x => x.formFieldConfig?.rule && Object.keys(x.formFieldConfig?.rule).length);
      formRule = formRule.reduce((pre, cur) => {
        pre[cur.prop] = [cur.formFieldConfig.rule];
        return pre;
      }, {});
      return JSON.stringify(formRule);
    });
};
