import { Dialog, DialogAction, DialogInstance, DialogOptions, DialogSlot } from "./dialog.type";
import { AppContext } from "vue";
import { createVNode, render } from "vue";
import CxDialogConstructor from "./index.vue";
import { hasOwn } from "@vueuse/core";
import { isFunction } from "@pureadmin/utils";

function normalizeOptions<T>(options: DialogOptions<T>) {
  const { footer, ...others } = options;
  const slots: Record<string, DialogSlot<T>> = {};
  let actions: Array<DialogAction> = undefined;
  if (hasOwn(options, "footer")) {
    isFunction(footer) ? (slots.footer = footer) : (actions = Array.isArray(footer) ? footer : []);
  }
  return { slots, actions, others };
}

function createContainer() {
  return document.createElement("div");
}

function createDialogVNode<T>(options: DialogOptions<T>, onDestroy: () => void) {
  const { slots, actions, others } = normalizeOptions(options);
  return createVNode(CxDialogConstructor, { ...others, actions, onDestroy }, slots);
}

const dialog: Dialog = {
  _context: null,
  create<T>(options: DialogOptions<T>, appContext?: null | AppContext): DialogInstance<T> {
    const container = createContainer();
    const vnode = createDialogVNode(options, () => render(null, container));
    vnode.appContext = appContext || dialog._context;
    render(vnode, container);
    document.body.appendChild(container.firstElementChild);
    const vm = vnode.component;
    return {
      instanceRef: vm.exposed.contentInstance,
      close: () => (vm.exposed.visible.value = false)
    };
  }
};

export default dialog;
