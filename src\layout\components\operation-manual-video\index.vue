<template>
  <div class="inline-block" v-if="showOperationManualVideo">
    <slot name="trigger" :open-dialog="openDialog">
      <div class="set-icon navbar-bg-hover" @click="onPlayOperationManualVideo()">
        <svg
          t="1747807601205"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="3011"
          width="18"
          height="18"
        >
          <path
            d="M512 960C265.6 960 64 758.4 64 512S265.6 64 512 64s448 201.6 448 448-201.6 448-448 448z m0-832C300.8 128 128 300.8 128 512s172.8 384 384 384 384-172.8 384-384S723.2 128 512 128z"
            fill="#333333"
            p-id="3012"
          />
          <path
            d="M512 608c-17.6 0-32-14.4-32-32 0-43.2 41.6-80 72-105.6 6.4-4.8 12.8-9.6 16-14.4 14.4-14.4 24-35.2 24-56 0-44.8-35.2-80-80-80s-80 35.2-80 80c0 17.6-14.4 32-32 32s-32-14.4-32-32c0-80 64-144 144-144s144 64 144 144c0 38.4-14.4 75.2-41.6 102.4-4.8 4.8-12.8 11.2-20.8 17.6-14.4 12.8-49.6 41.6-49.6 56 0 17.6-14.4 32-32 32zM512 768c-17.6 0-32-14.4-32-32v-32c0-17.6 14.4-32 32-32s32 14.4 32 32v32c0 17.6-14.4 32-32 32z"
            fill="#333333"
            p-id="3013"
          />
        </svg>
      </div>
    </slot>
    <el-dialog
      v-model="drawerVisible"
      title="操作视频"
      width="70%"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog()"
    >
      <CXPlayer :url="url" :autoplay="false" :is-live="false" :volume="0.4" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from "vue";
import CXPlayer from "@/components/cx-player";
import { useUserStore } from "@/store/modules/user";

const drawerVisible = ref(false);
const url = "https://ai-1316127475.cos.ap-shanghai.myqcloud.com/video/ai_guidance_0520_min.mp4";
const userStore = useUserStore();
const showOperationManualVideo = ref();
watchEffect(() => {
  showOperationManualVideo.value = ["1914722037374976002", "1"].includes(userStore.profile?.tenantInfo?.id);
});

function openDialog() {
  drawerVisible.value = true;
}

function closeDialog() {
  drawerVisible.value = false;
}

function onPlayOperationManualVideo() {
  drawerVisible.value = true;
}
</script>
