<template>
  <div class="inline-block">
    <el-dialog
      v-model="dialogVisible"
      title="初始化租户数据"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="handleDialogClosed"
    >
      <base-info-form ref="baseInfoFormRef" v-show="activeStep === 0" />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleClickSaveBtn" :loading="loading"> 保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import BaseInfoForm from "./base-info-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ITenantInitReq } from "@/models";
import { tenantInit } from "@/api";

const loading = ref(false);

const targetTenantId = ref<string>();

const emits = defineEmits(["postSaveSuccess"]);

const dialogVisible = ref(false);
const activeStep = ref(0);
const baseInfoFormRef = ref<InstanceType<typeof BaseInfoForm>>();

const requestTenantInit = useLoadingFn(async (params: ITenantInitReq) => {
  const { data } = await tenantInit(params);
  return data;
}, loading);

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const validResult = await baseInfoFormRef.value.validateForm().catch(() => false);
  if (!validResult) {
    return;
  }

  const finallyForm = {
    ...baseInfoFormRef.value.getFormValue(),
    targetTenantId: targetTenantId.value
  };

  requestTenantInit(finallyForm);

  // 处理保存后续事件
  closeDialog();

  ElMessage({
    message: "初始化开始，数据量较大请耐心等待10-30分钟后去相关页面查看",
    type: "success"
  });

  emits("postSaveSuccess");
};

function handleDialogClosed() {
  activeStep.value = 0;
}

/**
 * @description: 开启dialog
 */
function openDialog(id: string) {
  targetTenantId.value = id;
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}

defineExpose({
  openDialog
});
</script>

<style scoped></style>
