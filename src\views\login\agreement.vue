<template>
  <div class="additional-container">
    登录视为您已阅读并同意
    <span @click="showTermsDialog()">服务条款</span> 和
    <span @click="showPrivacyDialog()">隐私政策</span>
  </div>
  <el-dialog
    v-model="dialogVisible"
    title="帮助文档"
    width="600px"
    :append-to-body="true"
    :modal="false"
    class="agreement-dialog"
    destroy-on-close
    :key="dialogKey"
    @open="handleDialogOpen"
    draggable
  >
    <el-scrollbar height="470px">
      <MarkdownIt :filePath="currentMarkdownPath" />
    </el-scrollbar>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import MarkdownIt from "./components/markdown-it.vue";

const dialogVisible = ref(false);
const currentMarkdownPath = ref("");
const dialogKey = ref(0);

const showTermsDialog = () => {
  currentMarkdownPath.value = "./markdown/Terms.md";
  dialogKey.value++;
  dialogVisible.value = true;
};

const showPrivacyDialog = () => {
  currentMarkdownPath.value = "./markdown/Privacy.md";
  dialogKey.value++;
  dialogVisible.value = true;
};

const handleDialogOpen = () => {
  document.body.style.overflow = "hidden";
};
</script>

<style lang="scss" scoped>
.additional-container {
  font-size: 12px;
  color: #41464f;
  text-align: left;
  margin: 20px 0;
}

.additional-container span {
  color: #00b678;
  cursor: pointer;
}

.additional-container span:hover {
  text-decoration: underline;
}
</style>
<style>
.agreement-dialog .el-dialog__header {
  background-color: #f5f7fa;
  margin-right: 0;
}
</style>
