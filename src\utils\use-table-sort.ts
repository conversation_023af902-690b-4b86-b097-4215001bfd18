import { ref } from "vue";
import { handleOrderType } from "./sortByOrderType";
import { ISortReq } from "@/models/i-paging-req";

/**
 * @description: 表格排序参数钩子
 * @param {Function} postSortChange 排序后回调函数
 */
export function useTableSort(postSortChange?: (params: ISortReq) => void) {
  /**
   * 排序参数
   */
  const sortParams = ref<ISortReq>({
    orderByField: "",
    orderByType: ""
  });

  /**
   * 排序句柄，供表格@sort-change事件使用
   */
  function handleSortChange({ prop, order }) {
    if (order) {
      sortParams.value.orderByField = prop;
      sortParams.value.orderByType = handleOrderType(order);
    } else {
      sortParams.value.orderByField = "";
      sortParams.value.orderByType = "";
    }
    postSortChange && postSortChange(sortParams.value);
  }

  return {
    sortParams,
    handleSortChange
  };
}
