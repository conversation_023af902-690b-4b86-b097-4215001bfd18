import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

/**
 * 字段操作符枚举
 */
export enum FieldOperatorEnum {
  /**
   * 小于
   */
  LESS_THAN = 0,

  /**
   * 小于等于
   */
  LESS_THAN_OR_EQUAL = 1,

  /**
   * 大于
   */
  GREATER_THAN = 2,

  /**
   * 大于等于
   */
  GREATER_THAN_OR_EQUAL = 3,

  /**
   * 等于
   */
  EQUAL = 4,

  /**
   * 不等于
   */
  NOT_EQUAL = 5
}

/**
 * 字段操作符的描述映射
 */
export const FieldOperatorEnumMapDesc: Record<FieldOperatorEnum, string> = {
  [FieldOperatorEnum.LESS_THAN]: "小于",
  [FieldOperatorEnum.LESS_THAN_OR_EQUAL]: "小于等于",
  [FieldOperatorEnum.GREATER_THAN]: "大于",
  [FieldOperatorEnum.GREATER_THAN_OR_EQUAL]: "大于等于",
  [FieldOperatorEnum.EQUAL]: "等于",
  [FieldOperatorEnum.NOT_EQUAL]: "不等于"
};

export const FieldOperatorOptions: Array<IOption> = mapDescToOptions<number>(FieldOperatorEnumMapDesc);
