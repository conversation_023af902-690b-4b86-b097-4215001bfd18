// import Mock from "mockjs";
import { MockMethod } from "vite-plugin-mock";
export default [
  {
    url: "/api/auto-collect-charts",
    method: "get",
    response: () => {
      return {
        code: "0",
        msg: "success",
        data: {
          "list|5-10": [
            {
              "id|+1": 100,
              "productBatchNo|+1": 1,
              "deviceNo|1": "3guM2wej",
              "collectPoint|1": "退火电流",
              "values|6-10": [
                {
                  timestamp: '@DATETIME("yyyy-MM-dd HH:mm:ss")',
                  "value|1-500": 500
                }
              ]
            }
          ]
        }
      };
    }
  }
] as MockMethod[];
