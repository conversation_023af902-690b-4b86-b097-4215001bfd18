<template>
  <el-button-group class="btn-group">
    <el-button :class="{ 'is-active': active === 'list' }" @click="handleClick('list')">
      <FontIcon icon="icon-list" />
    </el-button>
    <el-button :class="{ 'is-active': active === 'card' }" @click="handleClick('card')">
      <IconifyIconOffline :icon="Menu" />
    </el-button>
  </el-button-group>
</template>

<script setup lang="ts">
import Menu from "@iconify-icons/ep/menu";
import { useVModels } from "@vueuse/core";

const props = defineProps<{
  modelValue: "card" | "list";
}>();

const emits = defineEmits<{
  (event: "update:modelValue", value: string);
}>();

const { modelValue: active } = useVModels(props, emits);

const handleClick = (value: "card" | "list") => {
  active.value = value;
};
</script>

<style scoped lang="scss">
.btn-group {
  .el-button {
    padding: 8px;

    svg {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
