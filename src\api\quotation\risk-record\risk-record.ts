import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse, IRiskRecord, IRiskRecordForm, IRiskRecordReq } from "@/models";

/** 查询全部风险记录 */
export const queryAllRiskRecord = (params?: IRiskRecordReq) => {
  const url: string = withApiGateway(`admin-api/business/risk-record/all-risk-record`);
  return http.get<IRiskRecordReq, IResponse<Array<IRiskRecord>>>(url, {
    params
  });
};

/** 查询风险记录分页  */
export const queryRiskRecord = (params: IRiskRecordReq) => {
  const url: string = withApiGateway(`admin-api/business/risk-record/risk-record`);
  return http.get<IRiskRecordReq, IListResponse<IRiskRecord>>(url, {
    params
  });
};

/** 根据风险记录id 查询详情 */
export const getRiskRecordById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/risk-record/risk-record-detail-by-id/${id}`);
  return http.get<string, IResponse<IRiskRecord>>(url);
};

/** 新增风险记录 */
export const createRiskRecord = (data: IRiskRecordForm) => {
  return http.post<IRiskRecordForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/risk-record/risk-record"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑风险记录 */
export const updateRiskRecord = (data: IRiskRecordForm) => {
  return http.put<IRiskRecordForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/risk-record/risk-record"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除风险记录根据Id */
export const deleteRiskRecordById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(
    withApiGateway(`admin-api/business/risk-record/risk-record-by-id/${id}`)
  );
};
