<template>
  <div class="ai-card">
    <div class="title">{{ title }}</div>
    <div class="content">
      <el-tooltip v-if="showTooltip" class="box-item" effect="dark" :content="formatThousandValue" placement="top">
        <span class="value">{{ valueCom }}</span>
      </el-tooltip>
      <span v-else class="value">{{ valueCom }}</span>
      <span class="unit">{{ unit }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatLargeNumber, formatThousands } from "@/utils/format";
import { computed } from "vue";

const props = defineProps<{
  title: string;
  value: string;
  unit?: string;
  showTooltip?: boolean;
}>();

const title = computed(() => props.title);
const valueCom = computed(() => formatLargeNumber(props.value));
const formatThousandValue = computed(() => formatThousands(props.value));
const unit = computed(() => props.unit);
</script>

<style lang="scss" scoped>
.ai-card {
  background: #f7f7f7;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  flex: 1 1 calc(50% - 10px);
}

.title {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.content {
  color: var(--text-primary);
  font-weight: 600;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.value {
  font-size: 24px;
}

.unit {
  color: var(--text-primary);
  font-size: 14px;
}

span {
  display: inline-block;
}
</style>
