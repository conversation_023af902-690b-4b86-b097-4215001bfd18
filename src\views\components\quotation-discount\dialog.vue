<template>
  <div>
    <el-dialog
      v-model="modelValue"
      title="批量设置折扣"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="490"
    >
      <QuotationDiscount ref="quotationDiscountRef" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import QuotationDiscount from "./index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IQuotationInquirySetDiscount } from "@/models";
import { quotationInquiryBatchSetDiscount } from "@/api/quotation/quotation-inquiry";
import { ElMessage } from "element-plus";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onSaveSuccess"): void;
}>();

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    taskId?: string;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});
const loading = ref(false);
const quotationDiscountRef = ref<InstanceType<typeof QuotationDiscount>>();
const handleSaveBtn = useLoadingFn(onSave, loading);

/**
 *  保存按钮点击事件
 */
async function onSave() {
  const discount: IQuotationInquirySetDiscount = quotationDiscountRef.value.getdiscountValue();
  discount.taskId = props.taskId;
  await quotationInquiryBatchSetDiscount(discount);
  ElMessage.success("批量设置折扣成功");
  emits("onSaveSuccess");
  closeDialog();
}
function closeDialog() {
  modelValue.value = false;
}
</script>

<style scoped></style>
