<template>
  <el-form ref="formRef" :model="form" :rules="rules" class="cx-form" label-width="7rem">
    <TitleBar title="企业信息" class="mb-3" />
    <el-row :gutter="40" class="mb-3">
      <el-col :span="12">
        <el-form-item label="企业名称" prop="comName">
          <el-input placeholder="请输入企业名称" v-model="form.comName" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="企业简称" prop="name">
          <el-input placeholder="请输入企业简称" v-model="form.name" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="企业地址" prop="address">
          <el-input placeholder="请输入企业地址" v-model="form.address" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            :active-value="0"
            :inactive-value="1"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
          />
        </el-form-item>
      </el-col>
    </el-row>

    <TitleBar title="联系人信息" class="mb-3" />
    <el-row :gutter="40" class="mb-3">
      <el-col :span="12">
        <el-form-item label="联系人姓名" prop="contactName">
          <el-input placeholder="请输入联系人姓名" v-model="form.contactName" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="联系人电话" prop="contactMobile">
          <el-input placeholder="请输入联系人手机" v-model="form.contactMobile" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="联系人邮箱" prop="contactEmail">
          <el-input placeholder="请输入联系人邮箱" v-model="form.contactEmail" />
        </el-form-item>
      </el-col>
      <el-col :span="24" />
      <el-col :span="12">
        <el-form-item label="Token限制" prop="tokenLimitEnabled">
          <el-switch
            v-model="form.tokenLimitEnabled"
            :active-value="true"
            :inactive-value="false"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
            @change="onTokenLimitEnabledChange()"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="token限制数"
          prop="tokenLimitCount"
          :rules="[{ required: form.tokenLimitEnabled, message: '请输入token限制数', trigger: ['change', 'blur'] }]"
        >
          <el-input-number
            class="!w-full"
            v-model="form.tokenLimitCount"
            :controls="false"
            placeholder="请输入token限制数"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="二次校验" prop="quotationSecondConfirm">
          <el-switch
            v-model="form.quotationSecondConfirm"
            :active-value="true"
            :inactive-value="false"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { ITenantForm, IBusinessLicense } from "@/models";
import { useTenantStore, useSystemAuthStore } from "@/store/modules";
import { EMAIL_REGEXP, MOBILE_REGEXP } from "@/consts";
import TitleBar from "@/components/TitleBar";

defineExpose({
  validateForm,
  getValidValue
});

const tenantStore = useTenantStore();
const systemAuthStore = useSystemAuthStore();

const formRef = ref<FormInstance>();

const form = reactive<ITenantForm>({
  id: undefined,
  comName: undefined,
  name: undefined,
  address: undefined,
  status: undefined,
  contactName: undefined,
  contactMobile: undefined,
  contactEmail: undefined,
  tokenLimitEnabled: undefined,
  tokenLimitCount: undefined,
  quotationSecondConfirm: false
});
const rules: FormRules = {
  comName: [{ required: true, message: requiredMessage("企业名称"), trigger: "change" }],
  name: [{ required: true, message: requiredMessage("企业简称"), trigger: "change" }],
  status: [{ required: true, message: requiredMessage("状态"), trigger: "change" }],
  contactMobile: [{ trigger: "change", message: "手机号码格式不正确", pattern: MOBILE_REGEXP }],
  contactEmail: [{ trigger: "change", message: "邮箱格式不正确", pattern: EMAIL_REGEXP }],
  tokenLimitEnabled: [{ required: true, message: requiredMessage("token限制"), trigger: "change" }]
};

const disabled = ref<boolean>();
const license = ref<IBusinessLicense>();

watch(
  () => tenantStore.tenantForm,
  tenantForm => {
    if (tenantForm && Object.keys(tenantForm).length) {
      Object.assign(form, tenantForm);
    }
    disabled.value = !!tenantForm.id;
  }
);

watchEffect(async () => {
  if (!form.id) {
    return;
  }

  const latestLicense: IBusinessLicense = (await systemAuthStore.getTenantAuthInfo(form.id)).data;
  if (!latestLicense.isAuthorization) {
    return;
  }
  license.value = latestLicense;
});
const onTokenLimitEnabledChange = () => {
  form.tokenLimitCount = null;
  if (!form.tokenLimitEnabled) {
    formRef.value.clearValidate(["tokenLimitCount"]);
  }
};

async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}
async function getValidValue(): Promise<ITenantForm> {
  if (!(await validateForm())) {
    return Promise.reject(false);
  }
  return form;
}
</script>

<style scoped></style>
