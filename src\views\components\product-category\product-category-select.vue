<template>
  <el-select
    class="!w-full"
    v-model="modelValue"
    clearable
    filterable
    placeholder="请选择产品分类"
    @change="onSelectCategory($event)"
  >
    <el-option v-for="item in allProductCategory || []" :key="item.id" :label="item.name" :value="item.name" />
  </el-select>
</template>

<script setup lang="ts">
import { queryAllProductCategory } from "@/api/product-category";
import { IProductCategory } from "@/models";
import { computed, onMounted, ref } from "vue";

const emits = defineEmits<{
  (e: "update:modelValue", val?: string): void;
  (e: "onSelectCategory", val: IProductCategory): void;
}>();
const allProductCategory = ref<Array<IProductCategory>>([]);

const props = defineProps<{
  modelValue?: string;
}>();

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: string) {
    emits("update:modelValue", val);
  }
});

onMounted(async () => {
  const { data } = await queryAllProductCategory();
  allProductCategory.value = data;
});

const onSelectCategory = (name: string) => {
  const category = allProductCategory.value.find(item => item.name === name);
  emits("onSelectCategory", category);
};
</script>
