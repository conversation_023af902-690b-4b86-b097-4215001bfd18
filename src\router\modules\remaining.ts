const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "",
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("@/views/register/index.vue"),
    meta: {
      title: "注册",
      showLink: false,
      rank: 103
    }
  },
  {
    path: "/home",
    name: "menu",
    component: () => import("@/views/home/<USER>"),
    meta: {
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      icon: "home",
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  }
] as Array<RouteConfigsTable>;
