import { InternalRuleItem, SyncValidateResult, ValidateOption, Value, Values } from "async-validator";
import { isFunction } from "lodash-unified";
import { resolveUnref } from "@vueuse/core";
import dayjs from "dayjs";
import { MaybeComputedRef } from "@vueuse/shared";

type Validator = (
  rule: InternalRuleItem,
  value: Value,
  callback: (error?: string | Error) => void,
  source: Values,
  options: ValidateOption
) => SyncValidateResult | void;

export class Validators {
  /**
   * Validator that requires value is positive number.
   */
  static positiveNumber: Validator = positiveNumberValidator;

  /**
   * Validator that requires value to be greater than the provided date.
   */
  static minDate(minDate: MaybeComputedRef<Date>): Validator {
    return minDateValidator(minDate);
  }

  /**
   * Validator that requires value to be less than the provided date.
   */
  static maxDate(maxDate: MaybeComputedRef<Date>): Validator {
    return maxDateValidator(maxDate);
  }

  /**
   * Validator that requires value to be greater than or equal to the provided date.
   */
  static minDateWithoutBoundary(minDate: MaybeComputedRef<Date>): Validator {
    return minDateValidator(minDate, false);
  }

  /**
   * Validator that requires value to be less than or equal to the provided date.
   */
  static maxDateWithoutBoundary(maxDate: MaybeComputedRef<Date>): Validator {
    return maxDateValidator(maxDate, false);
  }
}

/**
 * Validator that requires value is positive number.
 */
export function positiveNumberValidator(
  rule: InternalRuleItem,
  value: Value,
  callback: (error?: string | Error) => void
): void {
  if (isEmptyInputValue(value)) {
    return callback();
  }
  if (value > 0) {
    return callback();
  }
  return callback(getRuleMessage(rule));
}

/**
 * Validator that requires value to be greater than or equal to the provided date if `boundary` is true.
 */
export function minDateValidator(minDate: MaybeComputedRef<Date>, boundary = true): Validator {
  return (rule, value, callback) => {
    const minDateValue: Date = getValidDate(resolveUnref(minDate));
    value = getValidDate(value);
    if (isEmptyInputValue(value) || isEmptyInputValue(minDateValue)) {
      return callback();
    }
    if (value > minDateValue) {
      return callback();
    }
    if (boundary && value === minDateValue) {
      return callback();
    }
    return callback(getRuleMessage(rule));
  };
}

/**
 * Validator that requires value to be less than or equal to the provided date if `boundary` is true.
 */
export function maxDateValidator(maxDate: MaybeComputedRef<Date>, boundary = true): Validator {
  return (rule, value, callback) => {
    const maxDateValue: Date | null = getValidDate(resolveUnref(maxDate));
    value = getValidDate(value);
    if (isEmptyInputValue(value) || isEmptyInputValue(maxDateValue)) {
      return callback();
    }
    if (value < maxDateValue) {
      return callback();
    }
    if (boundary && value === maxDateValue) {
      return callback();
    }
    return callback(getRuleMessage(rule));
  };
}

function getRuleMessage(rule: InternalRuleItem): string {
  return isFunction(rule.message) ? rule.message() : rule.message;
}

// don't validate empty values
function isEmptyInputValue(value: unknown): boolean {
  return value == null || ((typeof value === "string" || Array.isArray(value)) && value.length === 0);
}

function getValidDate(value: any): Date | null {
  if (value == null) {
    return null;
  }
  const dayjsObject = dayjs(value);
  return dayjsObject.isValid() ? dayjsObject.toDate() : null;
}
