import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IListResponse,
  IResponse,
  IInquiryOrganization,
  IInquiryOrganizationForm,
  IInquiryOrganizationReq
} from "@/models";

/** 查询询价单位分页  */
export const queryInquiryOrganization = (data?: IInquiryOrganizationReq) => {
  const url: string = withApiGateway("admin-api/business/inquiryOrganization/page");
  return http.post<IInquiryOrganizationReq, IListResponse<IInquiryOrganization>>(url, {
    data
  });
};

/** 查询全部询价单位 */
export const queryInquiryOrganizationList = (data?: IInquiryOrganizationReq) => {
  const url: string = withApiGateway("admin-api/business/inquiryOrganization/getList");
  return http.get<IInquiryOrganizationReq, IResponse<Array<IInquiryOrganization>>>(url, {
    data
  });
};

/** 根据询价单位id 查询详情 */
export const getInquiryOrganizationById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/inquiryOrganization/${id}`);
  return http.get<string, IResponse<IInquiryOrganization>>(url);
};

/** 新增询价单位 */
export const createInquiryOrganization = (data: IInquiryOrganizationForm) => {
  return http.post<IInquiryOrganizationForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/inquiryOrganization"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑询价单位 */
export const updateInquiryOrganization = (data: IInquiryOrganizationForm) => {
  return http.put<IInquiryOrganizationForm, IResponse<boolean>>(
    withApiGateway(`admin-api/business/inquiryOrganization/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除询价单位根据Id */
export const deleteInquiryOrganizationById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/business/inquiryOrganization/${id}`));
};
