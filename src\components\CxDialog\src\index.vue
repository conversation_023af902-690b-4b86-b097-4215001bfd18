<template>
  <el-dialog v-bind="$attrs" v-model="visible" class="dynamic-dialog" @closed="emit('destroy')">
    <component :is="content" ref="contentInstance" />
    <template #footer>
      <slot name="footer" :instance="contentInstance">
        <component :is="actions" />
      </slot>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { type Component, computed, Fragment, h, ref, VNode } from "vue";
import { DialogAction, OnClickCallback } from "./dialog.type";
import { ElButton } from "element-plus";

const props = defineProps<{
  onOk?: OnClickCallback<object>;
  onCancel?: OnClickCallback<object>;
  content: string | VNode | Component | object;
  actions?: Array<DialogAction>;
}>();
const emit = defineEmits<{
  (e: "destroy"): void;
}>();

const visible = ref(true);
const contentInstance = ref();

const actions = computed(() => {
  const actions = props.actions || [
    { title: "取消", onClick: cancel },
    { title: "确定", onClick: ok, type: "primary" }
  ];
  return h(
    Fragment,
    actions?.map(action => {
      const { title, ...elProps } = action;
      return h(ElButton, elProps, () => title);
    })
  );
});

async function cancel() {
  if ((await props.onCancel?.(contentInstance.value)) === false) {
    return;
  }
  visible.value = false;
}

async function ok() {
  if ((await props.onOk?.(contentInstance.value)) === false) {
    return;
  }
  visible.value = false;
}

defineExpose({
  visible,
  contentInstance
});
</script>

<style>
.dynamic-dialog .el-dialog__footer:empty {
  display: none;
}
</style>
