import { IProcessRouteForm, IProcessRoute, IListResponse, IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../../util";

/** 获取工艺路线-根据物资种类编码获取工艺路线 */
export const queryProcessRoute = (categoryCode: string) => {
  return http.get<string, IListResponse<IProcessRoute>>(
    withApiGateway(`admin-api/business/processRoute/getListByCategoryCode/${categoryCode}`)
  );
};
/** 获取工艺路线详情 */
export const queryProcessRouteDeteil = (id: string) => {
  return http.get<string, IListResponse<IProcessRoute>>(
    withApiGateway(`admin-api/business/processRoute/getDetailById/${id}`)
  );
};
/** 新增工艺路线 */
export const createProcessRoute = (data: IProcessRouteForm) => {
  return http.post<IProcessRouteForm, IResponse<string>>(
    withApiGateway("admin-api/business/processRoute/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑工艺路线 */
export const editProcessRoute = (data: IProcessRouteForm) => {
  return http.put<IProcessRouteForm, IResponse<string>>(
    withApiGateway(`admin-api/business/processRoute/edit/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除工艺路线 */
export const deleteProcessRoute = (processRouteId: string) => {
  return http.delete<string, IResponse<string>>(
    withApiGateway(`admin-api/business/processRoute/delete/${processRouteId}`)
  );
};
