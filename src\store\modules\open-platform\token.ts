import { IOpenPlatformToken, IOpenPlatformTokenForm, IOpenPlatformTokenReq, IOpenPlatformTokenScope } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/open-platform";

export const useOpenPlatformTokenStore = defineStore({
  id: "cx-device-open-platform-token",
  state: () => ({
    tokens: [] as Array<IOpenPlatformToken>,
    scopes: [] as Array<IOpenPlatformTokenScope>,
    total: 0 as number,
    token: {} as IOpenPlatformToken,
    loading: false as boolean,
    tokenForm: {} as IOpenPlatformTokenForm
  }),
  actions: {
    async queryTokens(params?: IOpenPlatformTokenReq) {
      this.loading = true;
      const tokenRes = await api.queryTokens(params);
      this.total = tokenRes.data.total;
      this.tokens = tokenRes.data.list;
      this.loading = false;
    },

    async queryTokenScopes() {
      const tokenRes = await api.queryTokenScopes();
      this.scopes = tokenRes.data;
    },

    async createToken(data: IOpenPlatformTokenForm) {
      return api.addToken(data);
    },

    async editToken(data: IOpenPlatformTokenForm) {
      return api.editToken(data);
    },

    async generateToken() {
      return api.generateToken();
    },

    async getTokenDetailById(id: string): Promise<IOpenPlatformToken> {
      const tokenRes = await api.getTokenDetailById(id);
      this.token = tokenRes.data;
      return this.token;
    },

    setTokenForm(data?: Partial<IOpenPlatformTokenForm>) {
      this.tokenForm = data;
    },

    async deleteToken(id: string) {
      return api.deleteToken(id);
    }
  }
});
