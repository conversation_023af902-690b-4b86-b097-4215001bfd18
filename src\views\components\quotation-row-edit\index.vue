<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="型号" prop="modelName" class="flex">
          <div class="flex justify-between w-full">
            <el-input class="flex-1" v-model="form.modelName" clearable placeholder="请输入型号" />
            <el-button @click="onSelectProductDialogVisible()">选择</el-button>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="电压等级" prop="voltageLevel">
          <el-input v-model="form.voltageLevel" clearable placeholder="请输入电压等级" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格" prop="specification">
          <el-input v-model="form.specification" clearable placeholder="请输入规格" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" clearable placeholder="请输入单位" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="数量" prop="quantity">
          <el-input-number class="!w-full" v-model="form.quantity" :controls="false" placeholder="请输入数量" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="折扣1" prop="discount1">
          <el-input-number class="!w-full" v-model="form.discount1" :controls="false" placeholder="请输入折扣1" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="折扣2" prop="discount2">
          <el-input-number class="!w-full" v-model="form.discount2" :controls="false" placeholder="请输入折扣2" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="折扣3" prop="discount3">
          <el-input-number class="!w-full" v-model="form.discount3" :controls="false" placeholder="请输入折扣3" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="折扣4" prop="discount4">
          <el-input-number class="!w-full" v-model="form.discount4" :controls="false" placeholder="请输入折扣4" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="折扣前单价" prop="discountBeforeUnitPrice">
          <el-input-number
            class="!w-full"
            v-model="form.discountBeforeUnitPrice"
            :controls="false"
            :precision="2"
            placeholder="请输入折扣前单价"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <CategoryProductSelectDialog
      v-model="selectProductDialogVisible"
      @onSelectProductInfo="handleSelectProductInfo($event)"
    />
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IProductInfo, IQuotationInquiryDetail } from "@/models";
import CategoryProductSelectDialog from "@/views/components/category-product-select/dialog.vue";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});

const form = reactive<IQuotationInquiryDetail>({});
const formRef = ref<FormInstance>();
const selectProductDialogVisible = ref();
const rules: FormRules = {
  modelName: [{ required: true, trigger: "change", message: "型号名称不能为空" }]
};
const onSelectProductDialogVisible = () => {
  selectProductDialogVisible.value = true;
};

const handleSelectProductInfo = (data: IProductInfo) => {
  const { product, product: { voltage, specification, unit } = {}, price } = data;
  form.modelName = product.model?.modelName;
  form.voltageLevel = voltage;
  form.specification = specification;
  form.unit = unit;
  form.taxUnitPrice = price;
};

async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

function initFormValue(data: IQuotationInquiryDetail) {
  Object.assign(form, data);
}

function getFormValue() {
  return Object.assign({}, form);
}
</script>
