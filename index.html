<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
    />
    <title>程析玲珑AI-数字员工</title>
    <link rel="icon" href="/favicon.ico" />
    <script>
      document.title = "程析玲珑AI-数字员工";
      window.process = {};
    </script>
  </head>

  <body>
    <div id="app">
      <style>
        html,
        body,
        #app {
          width: 100%;
          height: 100%;
          display: flex;
          position: relative;
          justify-content: center;
          align-items: center;
          overflow: hidden;
        }

        .loader,
        .loader:before,
        .loader:after {
          border-radius: 50%;
          width: 2.5em;
          height: 2.5em;
          animation-fill-mode: both;
          animation: loadAnimation 1.8s infinite ease-in-out;
        }

        .loader {
          color: #00b678;
          font-size: 10px;
          margin: 80px auto;
          position: relative;
          text-indent: -9999em;
          transform: translateZ(0);
          animation-delay: -0.16s;
          top: 0;
          transform: translate(-50%, 0);
        }

        .loader:before,
        .loader:after {
          content: "";
          position: absolute;
          top: 0;
        }

        .loader:before {
          left: -3.5em;
          animation-delay: -0.32s;
        }

        .loader:after {
          left: 3.5em;
        }

        @keyframes loadAnimation {
          0%,
          80%,
          100% {
            box-shadow: 0 2.5em 0 -1.3em;
          }

          40% {
            box-shadow: 0 2.5em 0 0;
          }
        }
      </style>
      <div class="loader"></div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
