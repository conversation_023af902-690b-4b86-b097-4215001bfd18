<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="py-3 bg-bg_color">
      <QuotationHeader
        :quotationInquiryExport="state.quotationInquiryExport"
        class="w-full"
        :taskId="state.taskId"
        :fileNames="state.uploadRawFiles?.map(item => item.name)"
        :uploadPercentage="state.uploadPercentage"
        :quotationInquiryProgress="state.quotationInquiryProgress"
        @onStartParsing="handleStartParsing()"
        @onUploadFile="onSelectUploadFile($event)"
        @onStopParsing="handleStopParsing()"
        @onReParseing="handleonReParseing()"
      />
    </div>
    <!-- <div class="risk-tip flex items-center justify-between">
      <div class="flex items-center">
        <div class="tip">当前询价单存在重复询价风险，点击</div>
        <div class="link">查看详情</div>
      </div>
      <div class="close">
        <el-icon><Close /></el-icon>
      </div>
    </div> -->
    <div class="bg-bg_color p-5 pb-1 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <div class="flex justify-between">
        <div class="flex flex-1">
          <el-row :gutter="20">
            <el-col :span="17">
              <ElForm
                :inline="true"
                :model="state.quotationInquiryExport"
                class="p-0 party"
                label-width="70"
                label-position="left"
              >
                <el-row :gutter="20">
                  <el-col :span="8">
                    <ElFormItem label="询价单位" class="inquiry-organization !w-full" prop="inquiryCompanyId">
                      <el-select
                        clearable
                        filterable
                        class="!w-full"
                        placeholder="请选择询价单位"
                        v-model="state.quotationInquiryExport.inquiryCompanyId"
                      >
                        <el-option
                          v-for="item in state.inquiryOrganizationList || []"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        />
                      </el-select>
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="联系人" class="contract w-full" prop="contactPerson">
                      <ElInput
                        clearable
                        placeholder="请输入联系人"
                        v-model="state.quotationInquiryExport.contactPerson"
                      />
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="联系方式" prop="contactInfo" class="w-full">
                      <ElInput
                        clearable
                        placeholder="请输入联系方式"
                        v-model="state.quotationInquiryExport.contactInfo"
                      />
                    </ElFormItem>
                  </el-col>
                  <!-- <el-col :span="5">
                    <ElFormItem label="原始规格型号列" prop="subject" class="show-original-row">
                      <el-switch v-model="state.toggleOriginalRow" @change="onToggleOriginalRow()" />
                    </ElFormItem>
                  </el-col> -->

                  <el-col :span="8">
                    <ElFormItem label="铜价" prop="copperPrice" class="copper-price w-full">
                      <ElInput type="number" v-model="state.quotationInquiryExport.copperPrice" placeholder="请输入">
                        <template #append>元/吨</template>
                      </ElInput>
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="主题" prop="subject" class="w-full">
                      <ElInput clearable placeholder="请输入主题" v-model="state.quotationInquiryExport.subject" />
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="参考" class="rerfence w-full" prop="reference">
                      <ElInput clearable placeholder="请输入参考" v-model="state.quotationInquiryExport.reference" />
                    </ElFormItem>
                  </el-col>
                </el-row>
              </ElForm>
            </el-col>
            <el-col :span="6">
              <ElForm :inline="true" :model="state.quotationInquiryExport" class="p-0">
                <ElFormItem label="备注" class="w-full pr-6 remark" prop="remark">
                  <ElInput
                    :rows="3"
                    resize="none"
                    class="!w-full"
                    clearable
                    placeholder="请输入"
                    type="textarea"
                    v-model="state.quotationInquiryExport.remark"
                  />
                </ElFormItem>
              </ElForm>
            </el-col>
          </el-row>
        </div>
        <div class="action">
          <div>
            <ElButton
              :icon="Discount"
              :disabled="disabledEditDiscount"
              v-auth="PermissionKey.quotation.quotationManagementEdit"
              @click="onBatchQuotationDiscount()"
              >批量折扣</ElButton
            >
            <ElButton
              :icon="CopyDocument"
              v-auth="PermissionKey.quotation.quotationManagementCreate"
              @click="onPasteSpecification()"
              >粘贴型号规格</ElButton
            >
            <ElButton
              :icon="Plus"
              type="primary"
              :disabled="disabledEditDiscount"
              v-auth="PermissionKey.quotation.quotationManagementCreate"
              @click="onAddModelNameSpecificationDialogVisible()"
              >添加型号规格</ElButton
            >
          </div>
          <div class="absolute right-5 mt-2">
            <el-checkbox v-model="state.toggleOriginalRow" label="原始信息" @change="onToggleOriginalRow()" />
            <el-checkbox v-model="state.toggleDiscount" label="折扣" @change="onToggleDiscountRow()" />
          </div>
        </div>
      </div>
      <div class="flex-1 overflow-hidden">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              :header-class="getHeaderClass"
              ref="tableRef"
              class="quotation-grid"
              :columns="columnsRef"
              :data="state.list"
              :width="width"
              :height="height"
              row-key="id"
              fixed
            >
              <template #header-cell="{ column }">
                <span>{{ column.title }}</span>
                <template v-if="column.dataKey === 'selection'">
                  <el-checkbox
                    v-model="state.selectedAll"
                    label=""
                    :disabled="state.list?.length === 0"
                    @change="onToggleSelectedAll($event)"
                  />
                </template>
                <template v-else-if="column.dataKey === 'matchStatus'">
                  <el-popover placement="bottom">
                    <el-checkbox-group v-model="state.filterMatchStatus" @change="onChangeMatchStatus()">
                      <el-checkbox :label="MatchStatusEnum.FULL_MATCH">完全匹配</el-checkbox>
                      <el-checkbox :label="MatchStatusEnum.EQUIVALENT_MATCH">等效匹配</el-checkbox>
                      <el-checkbox :label="MatchStatusEnum.NO_MATCH">未匹配</el-checkbox>
                    </el-checkbox-group>
                    <template #reference>
                      <ElIcon
                        class="cursor-pointer"
                        :class="{ 'filter-match-status': state.filterMatchStatus?.length > 0 }"
                      >
                        <Filter />
                      </ElIcon>
                    </template>
                  </el-popover>
                </template>
              </template>

              <template #cell="{ column, rowData, rowIndex }">
                <template v-if="column.key === 'selection'">
                  <el-checkbox v-model="rowData.selected" label="" @change="onToggleSelectedRow()" />
                </template>
                <template v-else-if="column.key === 'rowIndex'"> {{ rowIndex + 1 }} </template>
                <template v-else-if="column.key === 'modelName'">
                  <el-tooltip
                    class="box-item"
                    :effect="
                      `${rowData.modelName}${rowData.voltageLevel}` ===
                      `${rowData.aiParseModel}${rowData.aiParseVoltage}`
                        ? 'dark'
                        : 'customized'
                    "
                    :show-after="100"
                    :content="`${rowData.aiParseModel}-${rowData.aiParseVoltage}`"
                    placement="top"
                  >
                    <template #content>
                      <div>
                        <template v-if="rowData.aiParseModel">{{ rowData.aiParseModel }}</template>
                        <template v-if="rowData.aiParseModel && rowData.aiParseVoltage">-</template>
                        <template v-if="rowData.aiParseVoltage">{{ rowData.aiParseVoltage }}</template>
                      </div>
                    </template>
                    <div>
                      <template v-if="rowData.modelName">{{ rowData.modelName }}</template>
                      <template v-if="rowData.modelName && rowData.voltageLevel">-</template>
                      <template v-if="rowData.voltageLevel">{{ rowData.voltageLevel }}</template>
                    </div>
                  </el-tooltip>
                </template>
                <template v-else-if="column.key === 'priceMatchRecord'">
                  <el-popover placement="top" :show-after="100" :popper-style="{ width: 'auto' }">
                    <template #reference>
                      <div
                        class="columnRawContent text-ellipsis line-clamp-2 pr-1"
                        :class="{ 'match-no-price': !rowData.isMatchPrice }"
                      >
                        {{ rowData.priceMatchRecord }}
                      </div>
                    </template>
                    <div class="max-w-[500px] leading-5">{{ rowData.priceMatchRecord }}</div>
                  </el-popover>
                </template>
                <template v-else-if="column.key === 'excelRowIndex'">
                  <div>{{ rowData.excelSheet }}-{{ rowData.excelRowIndex }}</div>
                </template>
                <template v-else-if="column.key === 'columnRawContent'">
                  <el-popover placement="top" :show-after="100" :popper-style="{ width: 'auto' }">
                    <template #reference>
                      <div class="columnRawContent text-ellipsis line-clamp-2 pr-1">
                        {{ rowData.columnRawContent }}
                      </div>
                    </template>
                    <div class="max-w-[500px] leading-5">{{ rowData.columnRawContent }}</div>
                  </el-popover>
                </template>
                <template v-else-if="column.key === 'quantity'">
                  <EditNumberCell
                    :data="rowData"
                    field="quantity"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'quantity')"
                  />
                </template>
                <template v-else-if="column.key === 'specification'">
                  <EditStringCell
                    :data="rowData"
                    field="specification"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'specification')"
                  />
                </template>
                <template v-else-if="column.key === 'unit'">
                  <EditStringCell
                    :data="rowData"
                    field="unit"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'unit')"
                  />
                </template>
                <template v-else-if="column.key === 'shortMeterDiscount'">
                  <EditNumberCell
                    :data="rowData"
                    field="shortMeterDiscount"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'shortMeterDiscount')"
                  />
                </template>
                <template v-else-if="column.key === 'discount1'">
                  <EditNumberCell
                    :data="rowData"
                    field="discount1"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discount1')"
                  />
                </template>
                <template v-else-if="column.key === 'discount2'">
                  <EditNumberCell
                    :data="rowData"
                    field="discount2"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discount2')"
                  />
                </template>
                <template v-else-if="column.key === 'discount3'">
                  <EditNumberCell
                    :data="rowData"
                    field="discount3"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discount3')"
                  />
                </template>
                <template v-else-if="column.key === 'discount4'">
                  <EditNumberCell
                    :data="rowData"
                    field="discount4"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discount4')"
                  />
                </template>

                <template v-else-if="column.key === 'discountBeforeUnitPrice'">
                  <EditNumberCell
                    :data="rowData"
                    field="discountBeforeUnitPrice"
                    :precision="2"
                    :disabled="disabledEditDiscount"
                    @onEditSuccess="handleOnEditSuccess(rowData, 'discountBeforeUnitPrice')"
                  />
                </template>
                <template v-else-if="column.key === 'taxTotalAmount'">
                  <div class="text-right">{{ formatThousands(rowData.taxTotalAmount) }}</div>
                </template>

                <template v-else-if="column.key === 'matchStatus'">
                  <el-tooltip :content="MatchStatusEnumMapDesc[rowData.matchStatus]" placement="top">
                    <component v-if="rowData.matchStatus === 1" :is="toRaw(icon_matching)" />
                    <component v-if="rowData.matchStatus === 2" :is="toRaw(icon_quivalent_matching)" />
                    <component v-if="rowData.matchStatus === 3" :is="toRaw(icon_unmatched)" />
                    <span v-if="rowData.matchStatus === MatchStatusEnum.NOT_NEED_MATCH">--</span>
                  </el-tooltip>
                </template>
                <template v-else-if="column.key === 'action'">
                  <div class="action-group">
                    <!-- <div class="action-item flex-1 text-center">
                    <el-icon><EditPen /></el-icon>
                  </div> -->
                    <el-button
                      v-auth="PermissionKey.quotation.quotationManagementEdit"
                      :disabled="disabledEditDiscount"
                      @click="onEditQuotationRowShow(rowData)"
                      link
                      :icon="EditPen"
                    />
                    <el-button
                      v-auth="PermissionKey.quotation.quotationManagementDelete"
                      :disabled="disabledEditDiscount"
                      type="danger"
                      @click="onDeleteQuotationRow(rowData.id)"
                      link
                      :icon="Delete"
                    />
                  </div>
                </template>
                <template v-else>
                  {{ rowData[column.key] }}
                </template>
              </template>
              <template #empty>
                <template v-if="!state.taskId">
                  <el-upload
                    class="w-[520px] h-[210px] quotation-upload"
                    ref="uploadRef"
                    drag
                    :disabled="state.disabledUpload"
                    :auto-upload="false"
                    :limit="state.fileLimit"
                    :multiple="state.multiple"
                    :accept="state.accept"
                    :on-exceed="onExceed"
                    :on-change="onChangeUploadFile"
                    :on-remove="onRemoveFile"
                  >
                    <el-icon class="upload-icon" :size="48"><upload-filled class="text-5xl" /></el-icon>
                    <div>将询价单拖到此处，或 <span class="text-primary">点击上传</span></div>
                  </el-upload>
                  <div class="file" v-if="state.uploadRawFiles?.length">
                    <el-scrollbar max-height="180px" class="w-full">
                      <div
                        class="flex items-center justify-between flex-1 gap-5"
                        v-for="(item, index) in state.uploadRawFiles"
                        :key="index"
                      >
                        <img v-if="item.type.startsWith('image/')" class="w-9 h-9" :src="ImageIcon" />
                        <img v-else class="w-9 h-9" :src="ExcelIcon" />
                        <div class="flex-1">
                          <div class="flex items-center gap-[10px] justify-between mr-[15px]">
                            <span class="file_name line-clamp-2 break-all">{{ item.name }}</span>
                            <el-icon @click="onDeleteUploadFile(index)" class="cursor-pointer" color="#F56C6C"
                              ><CircleCloseFilled
                            /></el-icon>
                          </div>
                          <el-progress class="flex-1" :percentage="state.uploadPercentage" />
                        </div>
                      </div>
                    </el-scrollbar>

                    <div class="flex flex-col">
                      <ElButton
                        :icon="CaretRight"
                        :disabled="!Array.isArray(state.fileIds) || state.fileIds?.length === 0"
                        type="primary"
                        :loading="startParsingLoading"
                        @click="handleStartParsing()"
                        >开始解析</ElButton
                      >
                      <el-checkbox class="!h-6" v-model="state.isFromSGCC" label="国网" />
                    </div>
                  </div>
                </template>
                <template v-else>
                  <CxEmptyData />
                </template>
              </template>
            </el-table-v2>
          </template>
        </el-auto-resizer>
      </div>
      <div class="flex items-center justify-center quotation-amount" v-if="state.list.length">
        <div class="select-row flex items-center text-xs" v-if="selectDeleteRows">
          <div class="py-1 rows">{{ selectDeleteRows }}</div>
          <el-button text type="danger" class="text-xs mx-1" size="small" @click="onDeleteSelectedRows()">
            删除
          </el-button>
        </div>
        <div class="text-xs font-semibold">
          含税合计总价：{{ (state.quotationInquiryProgress?.quotationAmount || 0).toLocaleString() }}
        </div>
      </div>
    </div>
    <QuotationDiscountDialog
      :taskId="state.taskId"
      v-model="state.quotationDiscountDialogVisible"
      @onSaveSuccess="handleBatchSetDiscount()"
    />
    <PasteSpecificationDialog
      v-model="state.pasteSpecificationDialogVisible"
      @onAnalysisSuccess="handleAnalysisSuccess($event)"
    />
    <QuotationRowEditDialog
      v-model="state.quotationRowEditDialogVisible"
      :quotationInquiryRow="state.quotationInquiryRow"
      :is-add="state.isAddModelNameSpecification"
      :taskId="state.taskId"
      @onSaveSuccess="handleuotationRowEditSuccess($event)"
      @onSaveSCancel="handleSaveSCancel()"
    />
    <PurchaseOrderSync />
  </div>
</template>

<script setup lang="ts" name="inquiry-unit">
import { queryInquiryOrganizationList } from "@/api/basic/inquiry-organization";
import { getLatestVersion } from "@/api/product-version";
import {
  batchDeleteTaskItemByIds,
  deleteTaskItemById,
  getParseDetailByTaskItemId,
  getQuotationInquiryCompletedParseList,
  getQuotationInquiryProgress,
  quotationInquiryParseInquiry
} from "@/api/quotation/quotation-inquiry";
import { uploadFile } from "@/api/upload-file";
import ExcelIcon from "@/assets/img/excel-icon.png";
import ImageIcon from "@/assets/img/image_icon.png";
import { MatchStatusEnum, MatchStatusEnumMapDesc, QuotationInquiryParseEnum, TaskParseStatusEnum } from "@/enums";
import {
  IInquiryOrganization,
  IQuotationInquiryDetail,
  IQuotationInquiryExport,
  IQuotationInquiryProgress
} from "@/models";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import PasteSpecificationDialog from "@/views/components/paste-specification/dialog.vue";
import QuotationDiscountDialog from "@/views/components/quotation-discount/dialog.vue";
import {
  CaretRight,
  CircleCloseFilled,
  Delete,
  Discount,
  EditPen,
  Plus,
  UploadFilled,
  Filter,
  CopyDocument
} from "@element-plus/icons-vue";
import { Arrayable, useIntervalFn, useEventListener } from "@vueuse/core";
import { Column, ElButton, ElMessage, genFileId, UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { computed, onMounted, reactive, ref, toRaw, nextTick, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useColumns } from "./columns";
import QuotationHeader from "./quotation-header.vue";
import QuotationRowEditDialog from "@/views/components/quotation-row-edit/dialog.vue";
import { cloneDeep } from "@pureadmin/utils";
import type { CheckboxValueType, TableV2Instance } from "element-plus";
import icon_matching from "@/assets/svg/icon_matching.svg?component";
import icon_quivalent_matching from "@/assets/svg/icon_quivalent_matching.svg?component";
import icon_unmatched from "@/assets/svg/icon_unmatched.svg?component";
import { PermissionKey } from "@/consts";
import PurchaseOrderSync from "@/components/purchase-order-sync/index.vue";
import { usePurchaseOrderSyncStore } from "@/store/modules";
import CxEmptyData from "@/components/CxEmpty";
import EditNumberCell from "@/views/components/quotation/cell/edit-number-cell.vue";
import EditStringCell from "@/views/components/quotation/cell/edit-string-cell.vue";
import { formatThousands } from "@/utils/format";

interface IQuotationInquiryDetailExt extends IQuotationInquiryDetail {
  selected?: boolean;
}

const { columns, getColumnsWidthByRate } = useColumns();
let allQuotationList: Array<IQuotationInquiryDetailExt> = [];
const router = useRouter();
const route = useRoute();
const { pause: stopPollingQuotationInquiry, resume: startPollingQuotationInquiry } = useIntervalFn(
  pollingQuotationInquiry,
  1800,
  {
    immediate: false
  }
);

const supportAllFileTypes = "image/*,.xlsx,.xls,.csv,application/pdf";
const supportImageFileTypes = "image/*";

const supportedFileTypes = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "application/pdf",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
];
const syncStore = usePurchaseOrderSyncStore();
const uploadRef = ref<UploadInstance>();
const tableRef = ref<TableV2Instance>();
const columnsRef = ref<Array<Column>>(columns);
const startParsingLoading = ref();
let parseSchedule = 0;
const referenceValue = 1632;
let versionId: string;
const state = reactive<{
  list: Array<IQuotationInquiryDetailExt>;
  quotationDiscountDialogVisible: boolean;
  pasteSpecificationDialogVisible: boolean;
  quotationRowEditDialogVisible: boolean;
  quotationInquiryExport: IQuotationInquiryExport;
  inquiryOrganizationList: Array<IInquiryOrganization>;
  uploadRawFiles: Array<UploadRawFile | File>;
  taskId?: string;
  uploadPercentage: number;
  quotationInquiryProgress: IQuotationInquiryProgress;
  fileId?: string;
  fileIds?: Array<string>;
  toggleOriginalRow: boolean;
  toggleDiscount: boolean;
  quotationInquiryRow: IQuotationInquiryDetail;
  isAddModelNameSpecification: boolean;
  isFromSGCC: boolean;
  filterMatchStatus: Array<MatchStatusEnum>;
  accept: string;
  fileLimit: number;
  disabledUpload: boolean;
  multiple: boolean;
  selectedAll: boolean;
}>({
  list: [],
  quotationDiscountDialogVisible: false,
  pasteSpecificationDialogVisible: false,
  quotationInquiryExport: {},
  inquiryOrganizationList: [],
  uploadRawFiles: [],
  taskId: "",
  uploadPercentage: 0,
  quotationInquiryProgress: {},
  toggleOriginalRow: true,
  toggleDiscount: false,
  quotationRowEditDialogVisible: false,
  quotationInquiryRow: {},
  isAddModelNameSpecification: false,
  isFromSGCC: false,
  filterMatchStatus: [],
  accept: supportAllFileTypes,
  fileLimit: 1,
  fileIds: [],
  disabledUpload: false,
  multiple: false,
  selectedAll: false
});

const disabledEditDiscount = computed(() => state.quotationInquiryProgress?.parseStatus !== TaskParseStatusEnum.PARSED);
const selectDeleteRows = computed(() => state.list.filter(x => x.selected).length);

const handleStartParsing = useLoadingFn(onStartParsing, startParsingLoading);

onMounted(() => {
  if (route.query?.taskId) {
    state.taskId = route.query.taskId as string;
    pollingQuotationInquiry();
    startPollingQuotationInquiry();
  }

  handleGetLatestVersion();
  handleQueryInquiryOrganization();

  nextTick(() => {
    calculateDynamicWidth();
  });

  window.addEventListener("resize", () => {
    nextTick(() => {
      setTimeout(() => {
        calculateDynamicWidth();
      }, 100);
    });
  });

  useEventListener("paste" as Arrayable<keyof WindowEventMap>, event => {
    // 如果存在TaskId 则不运行粘贴
    if (state.taskId) {
      return;
    }
    const clipboardEvent = event as ClipboardEvent;
    if (clipboardEvent.clipboardData && clipboardEvent.clipboardData?.files?.length > 0) {
      const file = clipboardEvent.clipboardData.files[0];
      // 图片，pdf,excel 粘贴
      const fileType = file.type;
      if (file && (file.type.startsWith("image/") || supportedFileTypes.includes(fileType))) {
        // state.uploadRawFiles = [file];
        onSelectUploadFile(file);
      } else {
        ElMessage.warning("不支持粘贴该文件类型");
      }
    }
  });
});

onUnmounted(() => {
  window.removeEventListener("resize", calculateDynamicWidth);
});

const onToggleSelectedAll = (selected: CheckboxValueType) => {
  state.list = state.list.map(item => {
    item.selected = selected as boolean;
    return item;
  });
};

const onToggleSelectedRow = () => {
  state.selectedAll = state.list.filter(x => x.selected).length === state.list.length;
};

const onDeleteSelectedRows = async () => {
  const selectedRows = state.list.filter(x => x.selected);

  if (selectedRows.length === 0) {
    ElMessage.warning("请选择要删除的行");
    return;
  }

  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  const ids = selectedRows.map(x => x.id);
  await batchDeleteTaskItemByIds(ids);
  state.list = state.list.filter(x => !ids.includes(x.id));
  allQuotationList = allQuotationList.filter(x => !ids.includes(x.id));
  handleQuotationInquiryProgress();
  ElMessage.success("删除成功");
};

const onChangeMatchStatus = () => {
  if (!Array.isArray(state.filterMatchStatus) || state.filterMatchStatus.length === 0) {
    state.list = cloneDeep(allQuotationList);
  } else {
    state.list = cloneDeep(allQuotationList.filter(x => state.filterMatchStatus.includes(x.matchStatus)));
  }
};

function getHeaderClass() {
  let baseClass = `cx-quotation_header ${state.taskId ? "" : "none-quotation"}`;
  if (state.toggleOriginalRow) {
    baseClass = `${baseClass} cx-quotation_header-original-row`;
  }
  return baseClass;
}

const onToggleOriginalRow = () => {
  columnsRef.value = columnsRef.value.map(x => {
    if (["excelRowIndex", "columnRawContent"].includes(x.dataKey as string)) {
      x.hidden = !state.toggleOriginalRow;
    }
    return x;
  });
  calculateDynamicWidth();
};

const onToggleDiscountRow = () => {
  columnsRef.value = columnsRef.value.map(x => {
    if (["discount1", "discount2", "discount3", "discount4", "shortMeterDiscount"].includes(x.dataKey as string)) {
      x.hidden = !state.toggleDiscount;
    }
    return x;
  });
  calculateDynamicWidth();
};

const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  // state.uploadRawFiles = [file];
  uploadRef.value!.handleStart(file);
};

const onChangeUploadFile: UploadProps["onChange"] = uploadFile => {
  onSelectUploadFile(uploadFile.raw);
};

/**  处理上传图片 */
function onSelectUploadFile(file: File) {
  state.disabledUpload = true;
  const imageType = file.type.startsWith("image/");
  if (imageType && state.uploadRawFiles.some(item => !item.type.startsWith("image/"))) {
    state.uploadRawFiles = [];
  }
  state.multiple = imageType;
  if (imageType) {
    state.accept = supportImageFileTypes;
    state.fileLimit = 10;
    state.uploadRawFiles.push(file);
  } else {
    state.accept = supportAllFileTypes;
    state.fileLimit = 1;
    state.uploadRawFiles = [file];
  }
  handleUploadQuotationInquiryFile(state.uploadRawFiles[state.uploadRawFiles.length - 1], imageType);
}

const onRemoveFile: UploadProps["onRemove"] = () => {
  state.uploadRawFiles = [];
  state.fileId = null;
};

const onDeleteUploadFile = async (index: number) => {
  if (!(await useConfirm("删除文件后，请重新上传新的询价单", "确认删除询价单"))) {
    return;
  }

  state.uploadRawFiles.splice(index, 1);
  state.fileIds.splice(index, 1);
  if (!Array.isArray(state.uploadRawFiles) || state.uploadRawFiles?.length === 0) {
    state.accept = supportAllFileTypes;
    state.uploadPercentage = 0;
    state.fileIds = [];
  }
};

const handleBatchSetDiscount = () => {
  handleGetQuotationInquiryParseDetailList();
  handleQuotationInquiryProgress();
};

const onEditQuotationRowShow = (data: IQuotationInquiryDetail) => {
  state.quotationInquiryRow = data;
  state.isAddModelNameSpecification = false;
  state.quotationRowEditDialogVisible = true;
};

const onAddModelNameSpecificationDialogVisible = () => {
  state.quotationInquiryRow = {};
  state.isAddModelNameSpecification = true;
  state.quotationRowEditDialogVisible = true;
};

const onDeleteQuotationRow = async (id: string) => {
  if (!(await useConfirm("确认删除该行报价？", "确认删除"))) {
    return;
  }

  await deleteTaskItemById(id);
  state.list = state.list.filter(x => x.id !== id);
  allQuotationList = allQuotationList.filter(x => x.id !== id);
  handleQuotationInquiryProgress();
  ElMessage.success("删除成功");
};

const handleuotationRowEditSuccess = (data: IQuotationInquiryDetail) => {
  if (state.isAddModelNameSpecification) {
    state.list.push(data);
    allQuotationList = cloneDeep(state.list);
    scrollToRow(state.list.length);
  } else {
    handleGetQuotationInquiryParseDetailList();
  }
  handleQuotationInquiryProgress();
};

const handleSaveSCancel = () => {
  state.quotationInquiryRow = null;
};

const handleOnEditSuccess = async (data: IQuotationInquiryDetail, fieldKey: string) => {
  try {
    await handleGetParseDetailByTaskItemId(data);
    handleQuotationInquiryProgress();
  } catch (error) {
    data[fieldKey] = allQuotationList.find(x => x.id === data.id)[fieldKey];
  }
};

async function onStartParsing() {
  if (!Array.isArray(state.uploadRawFiles) || state.uploadRawFiles.length === 0) {
    ElMessage.warning("请选择报价单");
    return;
  }

  const { data: taskId } = await quotationInquiryParseInquiry({
    // inquiryFileId: state.fileId,
    inquiryFileIdList: state.fileIds,
    versionId,
    isFromSGCC: state.isFromSGCC,
    sourceType: QuotationInquiryParseEnum.WEB
  });
  state.taskId = taskId;
  replaceQueryParams({ taskId });
  state.accept = supportAllFileTypes;
  ElMessage.success("正在解析询价单");
  syncStore.syncAIAnalysis(state.taskId);
  pollingQuotationInquiry();
  startPollingQuotationInquiry();
}

function handleStopParsing() {
  stopPollingQuotationInquiry();
  state.quotationInquiryProgress.parseStatus = TaskParseStatusEnum.PARSING_STOP;
}

function handleonReParseing() {
  startPollingQuotationInquiry();
  state.quotationInquiryProgress.parseStatus = TaskParseStatusEnum.PARSING;
}

//function handleUploadFile(uploadRawFile: UploadRawFile) {
//  state.uploadRawFiles = [uploadRawFile];
//  handleUploadQuotationInquiryFile(uploadRawFile);
// }

async function handleUploadQuotationInquiryFile(uploadRawFile: File, imageType = false): Promise<void> {
  state.fileId = null;
  const formData = new FormData();
  formData.append("file", uploadRawFile);
  const uploadInterval = setInterval(() => {
    if (state.uploadPercentage <= 95) {
      state.uploadPercentage += Math.floor(Math.random() * 5) + 1;
      return;
    }
  }, 300);

  const { data } = await uploadFile(formData).finally(() => (state.disabledUpload = false));

  clearInterval(uploadInterval);
  if (!data?.id) {
    ElMessage.error("上传报价单文件失败，请重新上传");
    // state.fileId = null;
    return;
  }
  if (!imageType) {
    state.fileIds = [data.id];
  } else {
    state.fileIds.push(data.id);
  }
  state.uploadPercentage = 100;
}

const onBatchQuotationDiscount = () => {
  state.quotationDiscountDialogVisible = true;
};

const onPasteSpecification = () => {
  state.pasteSpecificationDialogVisible = true;
};

// 解析文本  成功
const handleAnalysisSuccess = (taskId: string) => {
  state.taskId = taskId;
  replaceQueryParams({ taskId });
  ElMessage.success("正在解析询价单");
  state.list = [];
  allQuotationList = [];
  pollingQuotationInquiry();
  startPollingQuotationInquiry();
};

const handleQueryInquiryOrganization = async () => {
  const { data } = await queryInquiryOrganizationList();
  state.inquiryOrganizationList = data;
};

async function pollingQuotationInquiry() {
  await handleGetQuotationInquiryParseDetailList(true);
  await handleGetQuotationInquiryProgress();
}

async function handleGetQuotationInquiryParseDetailList(scrollToBottom = false) {
  const { data } = await getQuotationInquiryCompletedParseList(state.taskId);
  if (Array.isArray(data) && data.length) {
    state.list = data;
    if (scrollToBottom) {
      scrollToRow(state.list.length);
    }

    allQuotationList = cloneDeep(state.list);
  } else {
    state.list = [];
    allQuotationList = [];
  }
}

async function handleGetQuotationInquiryProgress() {
  const { data } = await getQuotationInquiryProgress(state.taskId);

  if (
    [TaskParseStatusEnum.PARSING_STOP, TaskParseStatusEnum.PARSED].includes(state.quotationInquiryProgress?.parseStatus)
  ) {
    syncStore.sync.percentage = data.parseSchedule;
    stopPollingQuotationInquiry();
    scrollToRow(0);
    return;
  }
  if (data.parseSchedule === 0) {
    parseSchedule = parseSchedule += 4;
    parseSchedule = parseSchedule >= 10 ? 10 : parseSchedule;
    state.quotationInquiryProgress = { ...data, parseSchedule: parseSchedule };
  } else {
    data.parseSchedule = parseFloat((parseSchedule + data.parseSchedule).toFixed(2));
    state.quotationInquiryProgress = data;
  }
  syncStore.sync.percentage = state.quotationInquiryProgress.parseSchedule;
  if (data && data.parseStatus === TaskParseStatusEnum.PARSED) {
    stopPollingQuotationInquiry();
    await handleGetQuotationInquiryParseDetailList(true);
    scrollToRow(0);
    ElMessage.success("解析完成");
  }
}

const handleGetLatestVersion = async () => {
  const { data } = await getLatestVersion();
  if (data?.id) {
    versionId = data.id;
  }
};

function replaceQueryParams(newParams) {
  router.replace({
    query: {
      ...router.currentRoute.value.query,
      ...newParams
    }
  });
}
const handleGetParseDetailByTaskItemId = async (data: IQuotationInquiryDetail) => {
  const { data: updateResult } = await getParseDetailByTaskItemId(data.id);
  const {
    specification,
    unit,
    quantity,
    shortMeterDiscount,
    discount1,
    discount2,
    discount3,
    discount4,
    taxUnitPrice,
    taxTotalAmount,
    discountBeforeUnitPrice
  } = updateResult;
  data.quantity = quantity;
  data.shortMeterDiscount = shortMeterDiscount;
  data.discount1 = discount1;
  data.discount2 = discount2;
  data.discount3 = discount3;
  data.discount4 = discount4;
  data.taxUnitPrice = taxUnitPrice;
  data.discountBeforeUnitPrice = discountBeforeUnitPrice;
  data.taxTotalAmount = taxTotalAmount;
  data.specification = specification;
  data.unit = unit;

  const quotationItem = allQuotationList.find(x => x.id === data.id);
  quotationItem.quantity = quantity;
  quotationItem.shortMeterDiscount = shortMeterDiscount;
  quotationItem.discount1 = discount1;
  quotationItem.discount2 = discount2;
  quotationItem.discount3 = discount3;
  quotationItem.discount4 = discount4;
  quotationItem.taxUnitPrice = taxUnitPrice;
  quotationItem.discountBeforeUnitPrice = discountBeforeUnitPrice;
  quotationItem.taxTotalAmount = taxTotalAmount;
  quotationItem.specification = specification;
  quotationItem.unit = unit;
};

function scrollToRow(rows: number): void {
  nextTick(() => {
    tableRef.value?.scrollToRow(rows, "start");
  });
}

const calculateDynamicWidth = () => {
  const tableWidth = tableRef.value?.$el.offsetWidth || 0;
  const rate = tableWidth / referenceValue;
  const originalColumnsWidth = getColumnsWidthByRate(rate);
  const dynamicWidthFields = ["modelName", "specification", "unit", "quantity"];
  let fixedColumnsWidth =
    originalColumnsWidth["rowIndex"] +
    originalColumnsWidth["selection"] +
    originalColumnsWidth["discountBeforeUnitPrice"] +
    originalColumnsWidth["taxUnitPrice"] +
    originalColumnsWidth["taxTotalAmount"] +
    originalColumnsWidth["priceMatchRecord"] +
    originalColumnsWidth["matchStatus"] +
    originalColumnsWidth["action"];

  if (state.toggleOriginalRow && state.toggleDiscount) {
    columnsRef.value = columnsRef.value.map(col => ({ ...col, width: originalColumnsWidth[col.dataKey as string] }));
    return;
  }

  // 显示原始行
  if (state.toggleOriginalRow) {
    fixedColumnsWidth =
      fixedColumnsWidth + originalColumnsWidth["excelRowIndex"] + originalColumnsWidth["columnRawContent"];
    const averageWidth = (tableWidth - fixedColumnsWidth) / 4;
    columnsRef.value = columnsRef.value.map(col => ({
      ...col,
      width: dynamicWidthFields.includes(col.dataKey as string)
        ? averageWidth
        : originalColumnsWidth[col.dataKey as string]
    }));
    return;
  }

  if (state.toggleDiscount) {
    fixedColumnsWidth =
      fixedColumnsWidth +
      originalColumnsWidth["discount1"] +
      originalColumnsWidth["discount2"] +
      originalColumnsWidth["discount3"] +
      originalColumnsWidth["discount4"];
    const averageWidth = (tableWidth - fixedColumnsWidth) / 4;
    columnsRef.value = columnsRef.value.map(col => ({
      ...col,
      width: dynamicWidthFields.includes(col.dataKey as string)
        ? averageWidth
        : originalColumnsWidth[col.dataKey as string]
    }));
    return;
  }
  const averageWidth = (tableWidth - fixedColumnsWidth) / 4;
  columnsRef.value = columnsRef.value.map(col => ({
    ...col,
    width: dynamicWidthFields.includes(col.dataKey as string) ? averageWidth : col.width
  }));
};

const handleQuotationInquiryProgress = async () => {
  const { data } = await getQuotationInquiryProgress(state.taskId);
  state.quotationInquiryProgress = data;
};
</script>

<style scoped lang="scss">
.party {
  .el-form-item {
    margin-bottom: 0.55rem;
    //  margin-right: 0;

    &.copper-price {
      :deep(.el-input__wrapper) {
        padding-right: 0;
        box-shadow: 0.0625rem 0 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
          0 0 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
          0 -0.0625rem 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
          0 0.0625rem 0 0 var(--el-input-border-color, var(--el-border-color)) inset;
      }

      :deep(.el-input-group__append) {
        padding: 0 10px 0 3px;
        background-color: transparent !important;
      }
    }

    &.inquiry-organization {
      :deep(.el-form-item__label) {
        font-weight: bold !important;
      }
    }

    &.show-original-row {
      width: 100%;

      :deep(.el-form-item__label) {
        white-space: nowrap;
      }

      :deep(.el-form-item__content) {
        justify-content: end;
      }
    }
  }
}

.risk-tip {
  background-color: var(--el-color-danger-light-9);
  padding: 8px 20px;

  .tip,
  .link {
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0;
    color: var(--el-color-danger);
  }

  .link {
    color: var(--el-color-primary);
    margin-left: 4px;
    cursor: pointer;
    border-bottom: 2px solid var(--el-color-primary);
  }

  .close {
    width: 30px;
    text-align: right;

    .el-icon {
      svg {
        color: var(--el-color-danger);
      }
    }
  }
}

.quotation-grid {
  :deep(.el-table-v2__empty) {
    margin-top: 20px;
    height: calc(100% - 70px) !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f5;
  }
}

.quotation-upload {
  :deep(.el-upload) {
    height: 100%;

    .el-upload-dragger {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }

  :deep(.el-upload-list) {
    display: none;
  }
}

.upload-icon {
  color: var(--el-color-primary);
}

.file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e4e7ed;
  width: 520px;
  margin-top: 10px;
  padding: 10px 20px 10px 20px;
  background-color: #ffffff;
  gap: 30px;

  .file_name {
    font-size: 16px;
    font-weight: 500;
    color: #303339;
  }
}

.action-group {
  width: 100%;

  .action-item {
    flex: 1;
    text-align: center;
    cursor: pointer;

    svg {
      color: #606266;
    }
  }
}

:deep(.cx-quotation_header) {
  .el-table-v2__header-cell {
    background-color: #fafbfc;
    color: #303339;
    font-size: 14px;
    font-weight: 500;
  }
}

:deep(.cx-quotation_header-original-row) {
  .el-table-v2__header-cell[data-key="excelSheet"],
  .el-table-v2__header-cell[data-key="excelRowIndex"],
  .el-table-v2__header-cell[data-key="columnRawContent"] {
    background-color: var(--el-color-warning-light-9);
  }
}

:deep(.none-quotation) {
  opacity: 35%;
}

.discount-price {
  :deep(.el-input__suffix) {
    svg {
      height: 22px;
    }
  }
}

.filter-match-status {
  color: var(--el-color-primary);
}

.quotation-amount {
  margin-top: 4px;
  // border: 1px solid #dde0e6;
  padding: 4px 0;
  border-radius: 3px;

  .select-row {
    .rows {
      background-color: var(--el-color-primary);
      color: #ffffff;
      padding: 2px 10px;
      border-radius: 4px;
    }
  }
}

.match-no-price {
  color: var(--el-color-warning);
}
</style>

<style>
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}
</style>
