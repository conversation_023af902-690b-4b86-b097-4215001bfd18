import { AccountRegisterSourceEnum } from "@/enums";

export interface IAccountRegister {
  /**
   * 所在部门
   */
  department?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 所属企业
   */
  enterprise?: string;
  /**
   * 手机号
   */
  phone: string;
  /**
   * 岗位
   */
  position?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 用户名
   */
  username: string;
  /**
   * openId
   */
  wechatMpOpenId: string;

  wechatUnionId?: string;

  /**  MA: 微信小程序 MP: 微信公众号 */
  registerSource?: AccountRegisterSourceEnum;
}
