<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" />

    <el-dialog
      v-model="drawerVisible"
      title="审核"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <AccountReviewForm ref="formRef" :id="id" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import AccountReviewForm from "./form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { updateBatchAccountReview } from "@/api/platform/account-review";

const loading = ref(false);
const props = defineProps<{
  mode: "edit" | "add";
  id?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const formRef = ref<InstanceType<typeof AccountReviewForm>>();
const drawerVisible = ref(false);
const handleSaveBtn = useLoadingFn(onSave, loading);

/**
 *  保存按钮点击事件
 */
async function onSave() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }

  const formVal = formRef.value.getFormValue();
  formVal.registerReviewIdList = [props.id];
  await updateBatchAccountReview(formVal);
  closeDialog();
  emits("postSaveSuccess");
  ElMessage({ message: "审核成功", type: "success" });
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  drawerVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  drawerVisible.value = false;
}
</script>

<style scoped></style>
