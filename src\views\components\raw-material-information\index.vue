<template>
  <div class="overflow-hidden w-full flex flex-col flex-1 h-[70vh]">
    <div class="bg-bg_color flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="原材料类型：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.type" placeholder="请输入原材料类型" />
        </ElFormItem>
        <ElFormItem label="原材料名称：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.rawName" placeholder="请输入原材料名称" />
        </ElFormItem>
        <ElFormItem label="原材料型号：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.rawModel" placeholder="请输入原材料型号" />
        </ElFormItem>
        <ElFormItem label="原材料规格：">
          <ElInput class="!w-[200px]" clearable v-model="state.params.rawSpec" placeholder="请输入原材料规格" />
        </ElFormItem>

        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </div>

    <div class="bg-bg_color px-4 my-5 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        highlight-current-row
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
        @row-click="onRowClick($event)"
      >
        <template #select="{ row, index }">
          <el-checkbox v-model="row.selected" label="" @change="onToggleSelectedRow($event, index)" />
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="raw-material-information">
import { onMounted, ref, reactive } from "vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryRawMaterialInformation } from "@/api/quote-basic-data/raw-material-information";
import { IRawMaterialInformation, IRawMaterialInformationReq } from "@/models";
import { CheckboxValueType, ElButton } from "element-plus";

interface IRawMaterialInformationExt extends IRawMaterialInformation {
  selected?: boolean;
}

const emits = defineEmits<{
  (e: "onSelect", data: IRawMaterialInformation): void;
}>();

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IRawMaterialInformationExt>;
  params: IRawMaterialInformationReq;
}>({
  list: [
    {
      id: "1",
      type: "PVC",
      rawModel: "11",
      rawName: "001",
      rawSpec: "1*1"
    },
    {
      id: "2",
      type: "PVC2",
      rawModel: "1333",
      rawName: "003",
      rawSpec: "1*3"
    }
  ],
  params: {}
});

onMounted(() => {
  requestList();
});

const onToggleSelectedRow = (value: CheckboxValueType, editIndex: number) => {
  state.list = state.list.map((item, index) => ({
    ...item,
    selected: index === editIndex ? (value as boolean) : false
  }));
  onSelect();
};

const onRowClick = (data: IRawMaterialInformationExt) => {
  state.list = state.list.map(item => ({ ...item, selected: item.id === data.id ? !item.selected : false }));
  onSelect();
};

const onSelect = () => {
  emits(
    "onSelect",
    state.list.find(item => item.selected)
  );
};

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryRawMaterialInformation(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);
</script>
