import { defineComponent, h } from "vue";
import { ElRadio, ElRadioGroup } from "element-plus";
import { useI18n } from "vue-i18n";

export default defineComponent({
  name: "EnumRadioGroup",
  props: {
    enum: {
      required: true
    },
    enumName: {
      type: String,
      required: true
    }
  },
  render() {
    let options: Array<{ label: string; value: string | number }> = [];
    if (this.enum) {
      const { t } = useI18n();
      options = Object.keys(this.enum)
        .filter(key => isNaN(Number(key)))
        .map(key => {
          const label = this.enumName ? t(`enum.${this.enumName}.${key}`) : key;
          const value = this.enum[key];
          return { label, value };
        });
    }
    const children = options.map(option => h(ElRadio, { label: option.value }, () => option.label));
    return h(ElRadioGroup, () => children);
  }
});
