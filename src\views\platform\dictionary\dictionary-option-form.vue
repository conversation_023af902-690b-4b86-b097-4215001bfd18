<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="right"
    label-width="7rem"
    require-asterisk-position="left"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="编码" prop="code">
          <el-input placeholder="请输入编码" v-model="form.code" clearable :disabled="disabled" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="字典值名称" prop="name">
          <el-input placeholder="请输入字典值名称" v-model="form.name" clearable :disabled="disabled" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="排序" prop="sort" class="w-full">
          <ElInputNumber
            placeholder="请输入排序"
            controls-position="right"
            class="!w-full"
            :min="0"
            v-model="form.sort"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-switch
            clearable
            v-model="form.status"
            :active-value="0"
            :inactive-value="1"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remarks">
          <el-input
            clearable
            v-model="form.remarks"
            type="textarea"
            placeholder="请输入备注"
            :rows="2"
            :maxlength="200"
            :show-word-limit="true"
            resize="none"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IDictionaryOptionForm } from "@/models";

defineExpose({
  validate,
  getValidValue
});

const formRef = ref<FormInstance>();
const disabled = ref<boolean>();
const form = reactive<IDictionaryOptionForm>({
  id: undefined,
  name: undefined,
  code: undefined,
  remarks: undefined,
  status: undefined,
  subClassCodeList: undefined,
  sort: undefined,
  parentCode: undefined
});
const rules = reactive<FormRules>({
  subClassCodeList: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
  code: [{ required: true, message: requiredMessage("编码"), trigger: "change" }],
  name: [{ required: true, message: requiredMessage("字典值名称"), trigger: "change" }],
  sort: [{ required: true, message: requiredMessage("排序"), trigger: "change" }],
  status: [{ required: true, message: requiredMessage("状态"), trigger: "change" }]
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IDictionaryOptionForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}
</script>

<style scoped></style>
