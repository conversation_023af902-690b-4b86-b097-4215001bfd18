/**
 * 禁用日期
 * @returns
 */
export const disabledNowAfterDate = (time: Date) => {
  return time.getTime() > Date.now();
};

/**
 * 禁用日期
 */
export const disabledTimeHours = (day: number) => {
  const nowDay = new Date().getDay();
  if (day === nowDay) {
    const hour = new Date().getHours();
    return makeRange(hour + 1, 24);
  } else {
    return [];
  }
};

function makeRange(start: number, end: number) {
  const result: number[] = [];
  for (let i = start; i <= end; i++) {
    result.push(i);
  }
  return result;
}
