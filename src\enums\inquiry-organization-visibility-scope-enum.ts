export enum InquiryOrganizationVisibilityScopeEnum {
  /** 所有人可见 */
  All = 0,

  /** 指定部门及子部门可见 */
  Department = 1
}

export const InquiryOrganizationVisibilityScopeEnumMapDesc: Record<InquiryOrganizationVisibilityScopeEnum, string> = {
  [InquiryOrganizationVisibilityScopeEnum.All]: "所有人可见",
  [InquiryOrganizationVisibilityScopeEnum.Department]: "指定部门及子部门可见"
};

export const InquiryOrganizationVisibilityScopeEnumMapColor: Record<InquiryOrganizationVisibilityScopeEnum, string> = {
  [InquiryOrganizationVisibilityScopeEnum.All]: "primary",
  [InquiryOrganizationVisibilityScopeEnum.Department]: "success"
};
