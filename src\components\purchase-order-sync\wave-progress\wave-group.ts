import { Wave } from "./wave";

export class WaveGroup {
  private waves: Array<Wave> = [];
  private readonly waveCount = 2;
  private readonly pointCount = 6;

  constructor() {
    for (let i = 0; i < this.waveCount; i++) {
      this.waves[i] = new Wave(i, this.pointCount);
    }
  }
  resize(stageWidth: number, stageHeight: number) {
    this.waves.forEach(wave => wave.resize(stageWidth, stageHeight));
  }

  setOffsetY(offsetY: number) {
    this.waves.forEach(wave => wave.setOffsetY(offsetY));
  }

  setColor(color: string) {
    this.waves.forEach(wave => wave.setColor(color));
  }

  draw(ctx: CanvasRenderingContext2D, complete?: boolean) {
    this.waves.forEach(wave => wave.draw(ctx, complete));
  }
}
