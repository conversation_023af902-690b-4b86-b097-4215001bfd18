import { getQuotationInquiryProgress } from "@/api/quotation/quotation-inquiry";
import { fullDateFormat } from "@/consts";
import { PurchaseOrderSyncStatus, TaskParseStatusEnum } from "@/enums";
import { formatDate } from "@/utils/format";
import { AxiosError } from "axios";
import { defineStore } from "pinia";
import { ref } from "vue";

export const usePurchaseOrderSyncStore = defineStore("cx-purchase-order-sync", () => {
  const dialogVisible = ref(false);
  const background = ref(false);
  const result = ref<any>({});
  const steps = ref<Array<any>>([]);
  const activeStep = ref<any>();
  const status = ref<PurchaseOrderSyncStatus>();
  const percentage = ref(0);
  const tips = ref("");

  const _abortSignal = ref(false);
  const _abortMessage = ref("");
  const _tips = ["解析文件", "AI识别", "单价匹配", "数据写入"];
  let _stepIndex: number;
  let _intervalHandle: ReturnType<typeof setInterval>;
  let taskId: string;
  const api = {
    getPurchaseOrderSyncResult: () =>
      new Promise(resolve => {
        const data = {
          modifyTime: new Date().toString(),
          dataRowCount: 100,
          upgradeStatus: true,
          todayCount: 20
        };
        setTimeout(() => resolve({ data }), 2000);
      }),
    // syncAIAnalysis: () =>
    //   new Promise(resolve => {
    //     const data = {
    //       modifyTime: new Date().toString(),
    //       dataRowCount: 100,
    //       upgradeStatus: true,
    //       todayCount: 20
    //     };
    //     setTimeout(() => resolve({ data }), 2000);
    //   }),
    checkPurchaseOrderSync: () =>
      new Promise(resolve => {
        setTimeout(() => resolve({ data: 100, test: "222" }), 2000);
      }),
    syncAIAnalysis: () => {
      const poll = async () => {
        const { data } = await getQuotationInquiryProgress(taskId);
        percentage.value = data.parseSchedule;
        if (data.parseStatus === TaskParseStatusEnum.PARSED) {
          return Promise.resolve({
            modifyTime: new Date().toString(),
            dataRowCount: data.totalInquiryParseCount,
            upgradeStatus: true,
            todayCount: data.parsedInquiryCount
          });
        }
        await new Promise(resolve => setTimeout(resolve, 1800));
        return poll();
      };
      return poll();
    }
  };

  async function refreshSyncResult() {
    const response = await api.getPurchaseOrderSyncResult();
    const data = (response as { data: any }).data;
    _setResult(data);
  }

  function syncAIAnalysis(id: string) {
    taskId = id;
    if (status.value === PurchaseOrderSyncStatus.RUNNING) {
      return;
    }
    _initializeSync();
    _startSync();
  }

  function reSyncPurchaseOrder() {
    if (status.value === PurchaseOrderSyncStatus.RUNNING) {
      return;
    }
    _initializeSync();
    // wait for progress animation
    setTimeout(() => _startSync(), 600);
  }

  function _startSync() {
    status.value = PurchaseOrderSyncStatus.RUNNING;
    _stepIndex = 0;
    _execStep();
  }

  async function _execStep() {
    if (_stepIndex >= steps.value.length) {
      _complete();
      return;
    }
    tips.value = _tips[_stepIndex];
    activeStep.value = steps.value[_stepIndex];
    activeStep.value.status = PurchaseOrderSyncStatus.RUNNING;
    const { duration, action } = activeStep.value;
    // _interval(duration, start, end);
    const promises = [_wait(duration)];
    if (action) {
      promises.push(
        action?.().catch(e => {
          _fail(e);
          return Promise.reject(e);
        })
      );
    }
    const [, syncResult] = await Promise.all(promises);
    //  percentage.value = end;
    if (syncResult === PurchaseOrderSyncStatus.BACKGROUND_PULL) {
      activeStep.value.status = PurchaseOrderSyncStatus.BACKGROUND_PULL;
    } else {
      activeStep.value.status = PurchaseOrderSyncStatus.SUCCESS;
    }
    _abortSignal.value ? _abort() : _nextStep();
  }

  function _nextStep() {
    _stepIndex++;
    _execStep();
  }

  // function _interval(duration: number, start: number, end: number) {
  //   _clearInterval();
  //   //  const time = 0;

  //   // _intervalHandle = setInterval(() => {
  //   //   time += 100;
  //   //   percentage.value = Math.floor((1 - Math.exp((-1 * time) / duration)) * (end - start) + start);
  //   // }, 100);
  // }

  async function _abort() {
    tips.value = _abortMessage.value;
    if (activeStep.value.status === PurchaseOrderSyncStatus.BACKGROUND_PULL) {
      status.value = PurchaseOrderSyncStatus.BACKGROUND_PULL;
    } else {
      status.value = PurchaseOrderSyncStatus.SUCCESS;
    }
    _clearInterval();
    await refreshSyncResult();
  }

  async function _complete() {
    status.value = PurchaseOrderSyncStatus.SUCCESS;
    // percentage.value = 100;
    _clearInterval();
    await refreshSyncResult();
    tips.value = `解析完成，数据写入成功`;
  }

  function _fail(e: AxiosError) {
    console.log(e);
    status.value = PurchaseOrderSyncStatus.FAIL;
    activeStep.value.status = PurchaseOrderSyncStatus.FAIL;
    tips.value = e.message;
    _clearInterval();
    refreshSyncResult();
  }

  function _wait(period: number) {
    return new Promise(resolve => setTimeout(() => resolve(""), period));
  }

  function _clearInterval() {
    if (_intervalHandle) {
      clearInterval(_intervalHandle);
      _intervalHandle = null;
    }
  }

  function _setResult(data: any) {
    const { modifyTime, dataRowCount, upgradeStatus, todayCount } = data;
    result.value = {
      dataRowCount,
      upgradeStatus,
      todayCount,
      modifyTime: formatDate(modifyTime, fullDateFormat)
    };
  }

  function _syncPurchaseOrder() {
    // return api.syncAIAnalysis().then(
    //   res => {
    //     if (res.code === 500) {
    //       _abortSignal.value = true;
    //       _abortMessage.value = res.msg;
    //       throw new Error();
    //     }
    //     if (!res.data.dataRowCount) {
    //       _abortSignal.value = true;
    //       _abortMessage.value = "未拉取到新的采购订单";
    //     }
    //     return res;
    //   },
    //   e => {
    //     if (e.code === 500) {
    //       _abortSignal.value = true;
    //       _abortMessage.value = e.message;
    //       return PurchaseOrderSyncStatus.BACKGROUND_PULL;
    //     }
    //     if (e.request.status === 504) {
    //       _abortSignal.value = true;
    //       _abortMessage.value = "EIP接口请求较慢或更新数量较多，本次拉取将在后台自动运行";
    //       return PurchaseOrderSyncStatus.BACKGROUND_PULL;
    //     }
    //   }
    // );
  }

  function _initializeSync() {
    dialogVisible.value = true;
    background.value = false;
    steps.value = [
      { name: "解析文件", percent: 10, duration: 800, start: 0, end: 10 },
      { name: "AI识别", percent: 70, duration: 2000, start: 10, end: 90 },
      // { name: "传输订单数据", percent: 58, duration: 2000, start: 29, end: 84, action: _syncPurchaseOrder },
      { name: "单价匹配", percent: 12, duration: 10, start: 90, end: 95 },
      { name: "数据写入", percent: 8, duration: 10, start: 95, end: 100 }
    ];
    percentage.value = 0;
    status.value = PurchaseOrderSyncStatus.RUNNING;
    tips.value = _tips[0];
    _abortSignal.value = false;
    _abortMessage.value = "";
  }

  return {
    result,
    background,
    dialogVisible,
    sync: {
      steps,
      status,
      percentage,
      tips
    },
    refreshSyncResult,
    syncAIAnalysis,
    reSyncPurchaseOrder
  };
});
