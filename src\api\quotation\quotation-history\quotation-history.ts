import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse, IQuotationHistory, IQuotationHistoryForm, IQuotationHistoryReq } from "@/models";

/** 报价历史列表查询  */
export const queryQuotationHistory = (data: IQuotationHistoryReq) => {
  const url: string = withApiGateway("admin-api/business/quotationInquiry/getHistoryPage");
  return http.post<IQuotationHistoryReq, IListResponse<IQuotationHistory>>(url, {
    data
  });
};

/** 根据报价历史id 查询详情 */
export const getQuotationHistoryById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/quotation-history/quotation-history-detail-by-id/${id}`);
  return http.get<string, IResponse<IQuotationHistory>>(url);
};

/** 新增报价历史 */
export const createQuotationHistory = (data: IQuotationHistoryForm) => {
  return http.post<IQuotationHistoryForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/quotation-history/quotation-history"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑报价历史 */
export const updateQuotationHistory = (data: IQuotationHistoryForm) => {
  return http.put<IQuotationHistoryForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/quotation-history/quotation-history"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除报价历史根据Id */
export const deleteQuotationHistoryById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(
    withApiGateway(`admin-api/business/quotation-history/quotation-history-by-id/${id}`)
  );
};
