<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
  {{#if (or needSearch needCreateOrEdit)}}
    <div class="bg-bg_color pr-6 pt-5 flex {{#if needSearch}} justify-between {{else}} justify-end {{/if}}">
     {{#if needSearch}}
      <ElForm :inline="true" :model="state.params" class="flex-1">
         {{#each searchFields}}
           {{#if (equal this.type "string") }}
             <ElFormItem label="{{this.label}}：">
               <ElInput class="!w-[200px]" clearable v-model="state.params.{{this.prop}}" placeholder="{{this.placeholder}}" />
            </ElFormItem>
           {{/if}}
            {{#if (equal this.type "date") }}
             <ElFormItem label="创建时间：">
              <el-date-picker
              v-model="state.params.{{this.prop}}"
              type="daterange"
              range-separator="～"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
             />
            </ElFormItem>
           {{/if}}
        {{/each}}
    
        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      {{/if}}
       {{#if needCreateOrEdit}}
        <AddEdit{{properCase name}}Dialog mode="add" @post-save-success="onQuery()">
         <template #trigger="{ openDialog }">
          <el-button  class="mb-5"  :icon="Plus" type="primary" @click="openDialog">新增</el-button>
         </template>
       </AddEdit{{properCase name}}Dialog>
       {{/if}}
    </div>
  {{/if}}

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
      {{#if (or needCreateOrEdit needDelete)}}
        <template #operation="data">
         <div>
          {{#if needCreateOrEdit}}
            <AddEdit{{properCase name}}Dialog mode="edit" :id="data.row.id" @post-save-success="onQuery()">
              <template #trigger="{ openDialog }">
               <el-button link type="primary" @click="openDialog">编辑</el-button>
             </template>
          </AddEdit{{properCase name}}Dialog>
          {{/if}}
          {{#if needDelete}}
          <ElButton link type="danger" @click="onDelete(data.row.id)">
            删除
          </ElButton>
          {{/if}}
        </div>
      </template>
      {{/if}}
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>

</template>

<script setup lang="ts" name="{{name}}">
import { onMounted, ref,reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { query{{properCase name}},delete{{properCase name}}ById } from "@/api/{{dashCase name}}";
import { I{{properCase name }} } from "@/models";
import { ElButton,ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
{{#if needCreateOrEdit }} import AddEdit{{properCase name}}Dialog from "./add-edit-{{dashCase name}}/dialog.vue"; {{/if}}

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
 const state =reactive<{
     list: Array<I{{properCase name }}>,
     params: {[key:string]:string}
 }>({
     list:[],
     params:{}
 })

onMounted(() => { 
  requestList();
});

{{#if needSearch}}
const onQuery=()=>{
    requestList();
}

const onResetQuery=()=>{
  state.params={}
  requestList();
}
{{/if}}


const onPageCurrentChange=()=>{
    requestList();
}

const onPageSizeChange=()=>{
   pagination.currentPage = 1;
   requestList();
}

{{#if needDelete}}
const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await delete{{properCase name}}ById(id);
  ElMessage.success("删除成功");
  requestList();
};
{{/if}}

const requestList = useLoadingFn(async () => {
  let params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

   {{#if needSearch}}
    if (state.params && Object.keys(state.params).length) {
     params = { ...params, ...state.params };
    }
   {{/if}}

  const { data } = await  query{{properCase name}}(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);

</script>
