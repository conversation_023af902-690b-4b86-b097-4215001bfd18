import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IListResponse,
  IProductInfoReq,
  IEquivalent,
  IEquivalentReq,
  IEquivalentForm,
  IResponse,
  IEquivalentImport
} from "@/models";

/** 查询等效命名分页  */
export const queryEquivalent = (data?: IEquivalentReq) => {
  const url: string = withApiGateway("admin-api/business/equivalentMapping/queryProductEquivalentPage");
  return http.post<IProductInfoReq, IListResponse<IEquivalent>>(url, { data });
};

/** 查询等效命名  */
export const queryEquivalentDetailById = (id?: string) => {
  const url: string = withApiGateway(`admin-api/business/equivalentMapping/${id}`);
  return http.get<IProductInfoReq, IResponse<IEquivalent>>(url);
};

/** 查询等效命名分页  */
export const queryProductInfo = (data: IEquivalentReq) => {
  const url: string = withApiGateway("admin-api/business/equivalentMapping/queryProductEquivalentPage");
  return http.post<IProductInfoReq, IListResponse<IEquivalent>>(url, { data });
};

/** 新增等效命名  */
export const addEquivalent = (data: IEquivalentForm) => {
  const url: string = withApiGateway("admin-api/business/equivalentMapping");
  return http.post<IProductInfoReq, IResponse<boolean>>(url, { data });
};

/** 编辑等效命名  */
export const updateEquivalent = (data: IEquivalentForm) => {
  const url: string = withApiGateway(`admin-api/business/equivalentMapping/${data.id}`);
  return http.put<IProductInfoReq, IResponse<boolean>>(url, { data });
};

/** 删除等效命名  */
export const deleteEquivalent = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/equivalentMapping/${id}`);
  return http.delete<IProductInfoReq, IResponse<boolean>>(url);
};

/** 导入 */
export const importProductEquivalent = (data: IEquivalentImport) => {
  const url: string = withApiGateway("admin-api/business/equivalentMapping/importProductEquivalent");
  return http.post<IEquivalentImport, IResponse<boolean>>(url, { data });
};
