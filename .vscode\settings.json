{"editor.formatOnType": true, "editor.formatOnSave": true, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.tabSize": 2, "editor.formatOnPaste": true, "editor.guides.bracketPairs": "active", "files.autoSave": "off", "git.confirmSync": false, "workbench.startupEditor": "newUntitledFile", "editor.suggestSelection": "first", "editor.acceptSuggestionOnCommitCharacter": false, "css.lint.propertyIgnoredDueToDisplay": "ignore", "editor.quickSuggestions": {"other": true, "comments": true, "strings": true}, "files.associations": {"editor.snippetSuggestions": "top"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "i18n-ally.localesPaths": "locales", "i18n-ally.keystyle": "nested", "i18n-ally.sortKeys": true, "i18n-ally.namespace": true, "i18n-ally.enabledParsers": ["yaml", "js"], "i18n-ally.sourceLanguage": "en", "i18n-ally.displayLanguage": "zh-CN", "i18n-ally.enabledFrameworks": ["vue"], "iconify.excludes": ["el"], "cSpell.words": ["pnpm"], "vue.codeActions.enabled": true, "scss.lint.unknownAtRules": "ignore", "css.lint.unknownAtRules": "ignore"}