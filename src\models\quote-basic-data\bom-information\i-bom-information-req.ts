import { ProductUnitEnum } from "@/enums/quote-basic-data";
import { IPagingReq } from "@/models";

export interface IBomInformationReq extends IPagingReq {
  id?: string;
  /**
   * 产品分类
   */
  productCategory?: string;
  /**
   * 物料编号
   */
  productCode?: string;
  /**
   * 产品名称
   */
  productName?: string;
  /**
   * 型号
   */
  model?: string;
  /**
   * 规格
   */
  specification?: string;
  /**
   * 电压等级
   */
  voltage?: string;
  /**
   * 数据来源
   */
  source?: string;

  /**
   * 计量单位
   */
  productUnit?: ProductUnitEnum;
}
