<template>
  <el-popover :visible="modelValue" placement="bottom" :width="300" trigger="click">
    <div class="flex flex-col gap-2">
      <div class="flex flex-col gap-1">
        <span>型号</span>
        <el-input v-model="state.modelName" placeholder="请输入型号" clearable @keydown.enter="onConfirmSearch()">
          <template #prepend>
            <el-button :icon="Search" />
          </template>
        </el-input>
      </div>
      <div class="flex flex-col gap-1" v-if="props.voltageLevelColumn?.voltageLevelMergeModelName">
        <span>电压</span>
        <el-input v-model="state.voltageLevel" placeholder="请输入电压" clearable @keydown.enter="onConfirmSearch()">
          <template #prepend>
            <el-button :icon="Search" />
          </template>
        </el-input>
      </div>
      <div class="flex flex-col gap-1" v-if="props.specificationColumn?.specificationMergeModelName">
        <span>规格</span>
        <el-input v-model="state.specification" placeholder="请输入规格" clearable @keydown.enter="onConfirmSearch()">
          <template #prepend>
            <el-button :icon="Search" />
          </template>
        </el-input>
      </div>
    </div>
    <div class="flex items-center justify-between mt-5">
      <el-button type="primary" @click="onClear()">清除</el-button>
      <div>
        <el-button @click="onCancel()">取消</el-button>
        <el-button type="primary" @click="onConfirmSearch()">确定</el-button>
      </div>
    </div>
    <template #reference>
      <div />
    </template>
  </el-popover>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { Column } from "element-plus";
import { computed, reactive } from "vue";

const emits = defineEmits<{
  (
    e: "onSearch",
    params: {
      modelName?: string;
      voltageLevel?: string;
      specification?: string;
    }
  ): void;
  (e: "update:modelValue", value: boolean): void;
}>();

const props = withDefaults(
  defineProps<{
    voltageLevelColumn?: Column;
    specificationColumn?: Column;
    disabled?: boolean;
    modelValue?: boolean;
  }>(),
  {
    disabled: false,
    modelValue: false
  }
);

const state = reactive<{
  modelName?: string;
  preModelName?: string;
  voltageLevel?: string;
  preVoltageLevel?: string;
  specification?: string;
  preSpecification?: string;
  visible?: boolean;
}>({
  modelName: "",
  preModelName: "",
  voltageLevel: "",
  preVoltageLevel: "",
  specification: "",
  preSpecification: ""
});

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

// const showFilter = computed(
//   () => state.visible || state.modelName?.trim() || state.voltageLevel?.trim() || state.specification?.trim()
// );

// const showFilterHighlight = computed(
//   () => state.modelName?.trim() || state.voltageLevel?.trim() || state.specification?.trim()
// );

// const onShowFilter = () => {
//   state.visible = true;
// };

const onClear = () => {
  state.modelName = "";
  state.voltageLevel = "";
  state.specification = "";
  emits("onSearch", {
    modelName: state.modelName,
    voltageLevel: state.voltageLevel,
    specification: state.specification
  });
  onClose();
};

const onCancel = () => {
  state.modelName = state.preModelName;
  state.voltageLevel = state.preVoltageLevel;
  state.specification = state.preSpecification;
  onClose();
};
const onConfirmSearch = () => {
  state.preModelName = state.modelName;
  state.preVoltageLevel = state.voltageLevel;
  state.preSpecification = state.specification;

  emits("onSearch", {
    modelName: state.modelName,
    voltageLevel: state.voltageLevel,
    specification: state.specification
  });
  onClose();
};

const onClose = () => {
  modelValue.value = false;
};
</script>

<style scoped lang="scss">
.show-filter {
  visibility: visible !important;

  &.show-filter-highlight {
    color: var(--el-color-primary);
  }
}
</style>
