<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入新密码"
            maxlength="20"
            show-password
            autocomplete="new-password"
          />
        </el-form-item>
        <el-form-item>
          <div class="text-sm text-secondary mt-2" v-if="form.password">
            <div>
              密码强度:
              <span v-if="passwordStrength === 'high'" class="text-primary"> 高</span>
              <span v-if="passwordStrength === 'medium'" class="text-yellow-500"> 中</span>
              <span v-if="passwordStrength === 'low'" class="text-danger"> 低</span>
            </div>
            <div>1）8-20个字符组成</div>
            <div>2）密码包含大小写字母、数字、特殊字符</div>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            clearable
            placeholder="请输入确认密码"
            maxlength="20"
            show-password
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { getPasswordSafety } from "@/utils/get-password-safety";

/**
 * 修改密码表单
 */

interface FormType {
  password: string;
  confirmPassword: string;
}

const form = reactive<FormType>({
  password: "",
  confirmPassword: ""
});
const formRef = ref<FormInstance>();
const passwordStrength = computed(() => {
  return getPasswordSafety(form.password);
});

const rules: FormRules = {
  password: [
    { required: true, message: "请输入新密码", trigger: "change" },
    {
      trigger: ["blur", "change"],
      pattern: /^(?:(?=.*[0-9])(?=.*[a-zA-Z])|(?=.*[0-9])(?=.*[\W_])|(?=.*[a-zA-Z])(?=.*[\W_])).+$/,
      message: "密码需包含数字、字母、特殊符号中的任意两种字符"
    }
  ],
  confirmPassword: [
    { required: true, message: "请输入确认密码", trigger: ["blur", "change"] },
    { trigger: ["blur", "change"], validator: validatorConfirmPassword }
  ]
};

function validatorConfirmPassword(rule: any, value: string, callback: Function) {
  if (value !== form.password) {
    callback("新密码和确认密码不一致");
    return;
  }

  callback();
}

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

defineExpose({
  validateForm,
  getFormValue
});
</script>

<style scoped lang="scss"></style>
