import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IResponse,
  IInquiryRisk,
  IInquiryRiskDetail,
  IInquiryRiskReq,
  IListResponse,
  IInquiryRiskHandle,
  IInquiryRiskSummary
} from "@/models";

/** 查询当前用户下的询价风险记录(分页)  */
export const getInquiryRiskPage = (data?: IInquiryRiskReq) => {
  const url: string = withApiGateway("/admin-api/business/inquiry/risk/getPage");
  return http.post<IInquiryRiskReq, IListResponse<IInquiryRisk>>(url, { data });
};

/** 查询询价风险明细列表  */
export const queryInquiryRiskDetailList = (data: IInquiryRiskReq) => {
  const url: string = withApiGateway(`admin-api/business/inquiry/risk/getRiskItemPage`);
  return http.post<IInquiryRiskReq, IListResponse<IInquiryRiskDetail>>(url, { data });
};

/** 处理询价风险  */
export const handleInquiryRisk = (data: IInquiryRiskHandle) => {
  const url: string = withApiGateway(`admin-api/business/inquiry/risk/handleInquiryRisk/${data.id}`);
  return http.post<IInquiryRiskHandle, IResponse<boolean>>(url);
};

/** 处理询价风险-批量  */
export const handleBatchInquiryRisk = (data: IInquiryRiskHandle) => {
  const url: string = withApiGateway("admin-api/business/inquiry/risk/handleInquiryRisk");
  return http.post<IInquiryRiskHandle, IResponse<boolean>>(url, { data });
};

/** 查询询价风险详情  */
export const getInquiryRiskDetailById = (id: string) => {
  const url: string = withApiGateway(`/admin-api/business/inquiry/risk/getById/${id}`);
  return http.get<void, IResponse<IInquiryRisk>>(url);
};

export const getCurrentUserRiskSummary = () => {
  const url: string = withApiGateway(`/admin-api/business/inquiry/risk/getCurrentUserRiskSummary`);
  return http.get<void, IResponse<IInquiryRiskSummary>>(url);
};
