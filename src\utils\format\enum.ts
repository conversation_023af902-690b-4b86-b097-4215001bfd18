import { i18n } from "@/plugins/i18n";

export const formatEnum = (value: string | number, enumeration: Record<string, string | number>, enumName: string) => {
  // Bob 如果 枚举为电压等级 voltageClassesEnum 则返回原始值
  // 表达里面电压等级 原先为枚举下拉选择，现在为兼容sdcc2.0改为输入，展示地方不需要格式化，
  // 考虑到后面如果改成电压等级枚举下拉，存数字，则需要进行格式化
  if (enumName === "voltageClassesEnum") {
    return value;
  }

  if (value == null) {
    return undefined;
  }
  const property: string | undefined = Object.entries(enumeration).find(([_, v]) => v === value)?.[0];
  if (!property) {
    return undefined;
  }
  const key = `enum.${enumName}.${property}`;
  return i18n.global.t.call(i18n.global.locale, key) || key;
};
