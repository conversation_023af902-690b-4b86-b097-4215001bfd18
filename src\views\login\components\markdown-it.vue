<template>
  <div class="markdown-content" ref="contentRef" v-html="renderedContent" />
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import MarkdownIt from "markdown-it";

const props = defineProps({
  filePath: {
    type: String,
    required: true
  }
});

const renderedContent = ref("");
const contentRef = ref<HTMLElement | null>(null);
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true
});

const loadMarkdown = async () => {
  try {
    const response = await fetch(props.filePath);
    if (!response.ok) {
      throw new Error(`加载失败: ${response.statusText}`);
    }
    const text = await response.text();
    renderedContent.value = md.render(text);
    setTimeout(() => {
      if (contentRef.value) {
        contentRef.value.scrollTop = 0;
      }
    }, 0);
  } catch (error) {
    console.error("加载Markdown文件时出错:", error);
    renderedContent.value = `<p class="error-text">文档加载失败</p>`;
  }
};

watch(() => props.filePath, loadMarkdown, { immediate: true });

onMounted(() => {
  loadMarkdown();
});
</script>

<style scoped>
.markdown-content {
  padding: 20px;
  line-height: 1.6;
  color: #333;
}

.error-text {
  color: #f56c6c;
  font-weight: bold;
}
</style>
