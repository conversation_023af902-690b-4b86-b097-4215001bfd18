<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 pt-5 flex justify-between">
      <ElForm :inline="true" :model="state.params" class="flex-1">
        <ElFormItem label="询价单位" prop="inquiryOrgId">
          <el-select clearable placeholder="请选择询价单位" filterable v-model="state.params.inquiryOrgId">
            <el-option
              v-for="item in state.inquiryOrganizationList || []"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </ElFormItem>
        <ElFormItem label="询价开始时间" prop="inquiryStartTime">
          <el-date-picker
            v-model="state.params.inquiryStartTime"
            type="date"
            placeholder="请选择询价开始时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </ElFormItem>

        <ElFormItem label="询价结束时间" prop="inquiryStartTime">
          <el-date-picker
            v-model="state.params.inquiryEndTime"
            type="date"
            placeholder="请选择询价结束时间"
            value-format="YYYY-MM-DD 23:59:59"
          />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="onQuery()">搜索</ElButton>
          <ElButton @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #operatorName="{ row }">
          <CXEmployee :name="row.operatorName" />
        </template>

        <template #parseTokenUsage="{ row }">
          <ThousandSeparator :value="row.parseTokenUsage" />
        </template>

        <template #quotationAmount="{ row }">
          <ThousandSeparator :value="row.quotationAmount" />
        </template>

        <template #inquiryAttachmentName="{ row }">
          <template v-if="row.inquiryType === InquiryTypeEnum.file">
            <el-popover placement="top" :show-after="100" :popper-style="{ width: 'auto' }">
              <div class="flex flex-col gap-3">
                <CxDownload
                  v-for="item in row.inquiryAttachmentList"
                  :key="item.id"
                  :name="item.inquiryAttachmentName"
                  :id="item.inquiryAttachmentId"
                />
              </div>
              <template #reference>
                <CxDownload
                  :name="row.inquiryAttachmentList?.[0]?.inquiryAttachmentName"
                  :id="row.inquiryAttachmentList?.[0]?.inquiryAttachmentId"
                />
              </template>
            </el-popover>
          </template>
          <span v-else>{{ row.inquiryTextContent }}</span>
        </template>
        <template #quotationAttachmentId="{ row }">
          <CxDownload :name="row.quotationAttachmentName" :id="row.quotationAttachmentId" />
        </template>

        <template #parseStatus="{ row }">
          <CxTag v-if="row.parseStatus" :type="TaskParseStatusEnumMapColor[row.parseStatus]">{{
            TaskParseStatusEnumMapDesc[row.parseStatus]
          }}</CxTag>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="quotation-history">
import { onMounted, ref, reactive } from "vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryQuotationHistory } from "@/api/quotation/quotation-history";
import { IInquiryOrganization, IQuotationHistory, IQuotationHistoryReq } from "@/models";
import { ElButton } from "element-plus";
import CXEmployee from "@/components/cx-employee/index.vue";
import CxDownload from "@/components/cx-download/index.vue";
import { TaskParseStatusEnumMapColor, TaskParseStatusEnumMapDesc, InquiryTypeEnum } from "@/enums";
import CxTag from "@/components/CxTag/index.vue";
import { queryInquiryOrganizationList } from "@/api/basic/inquiry-organization";
import ThousandSeparator from "@/components/thousand-separator/index.vue";

const { pagination } = useTableConfig();
const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IQuotationHistory>;
  params: IQuotationHistoryReq;
  inquiryOrganizationList: Array<IInquiryOrganization>;
}>({
  list: [],
  params: {},
  inquiryOrganizationList: []
});

onMounted(() => {
  requestList();
  handleQueryInquiryOrganization();
});

const onQuery = () => {
  requestList();
};

const onResetQuery = () => {
  state.params = {};
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params: IQuotationHistoryReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  //   if (state.params.inquiryStartTime) {
  // params.inquiryStartTime=
  //   }

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryQuotationHistory(params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);

const handleQueryInquiryOrganization = async () => {
  const { data } = await queryInquiryOrganizationList();
  state.inquiryOrganizationList = data;
};
</script>
