import { IEmployee, IEmployeeForm, IEmployeeReq, IListResponse, IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { StatusEnum } from "@/enums";

export const queryEmployee = (params: IEmployeeReq) => {
  return http.get<IEmployeeReq, IListResponse<IEmployee>>(withApiGateway("admin-api/system/user/page"), { params });
};

export const getEmployeeDetail = (id: string) => {
  return http.get<string, IResponse<IEmployee>>(withApiGateway("admin-api/system/user/get"), { params: { id } });
};

export const addEmployee = (data: IEmployeeForm) => {
  return http.post<IEmployeeForm, IResponse<string>>(
    withApiGateway("admin-api/system/user/create"),
    { data },
    { showErrorInDialog: true }
  );
};

export const editEmployee = (data: IEmployeeForm) => {
  return http.put(withApiGateway("admin-api/system/user/update"), { data }, { showErrorInDialog: true });
};

export const resetEmployeePassword = (id: string, password: string) => {
  return http.put(withApiGateway("admin-api/system/user/update-password"), { data: { id, password } });
};

export const deleteEmployee = (id: string) => {
  return http.delete(withApiGateway("admin-api/system/user/delete"), { params: { id } });
};

export const editEmployeeStatus = (id: string, status: StatusEnum) => {
  return http.put(withApiGateway("admin-api/system/user/update-status"), { data: { id, status } });
};
