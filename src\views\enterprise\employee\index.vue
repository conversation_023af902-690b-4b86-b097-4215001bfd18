<template>
  <div class="bg-bg_color pt-[8px] px-6 flex justify-between">
    <ElForm :inline="true" :model="state.params" class="ml-6 flex-1">
      <ElFormItem label="综合搜索：" class="w-1/4">
        <ElInput
          class="w-full"
          clearable
          v-model="state.params.keyWords"
          placeholder="请输入用户名/手机号"
          @clear="onConfirmQuery"
        />
      </ElFormItem>

      <ElFormItem label="状态：" class="w-1/5">
        <ElSelect v-model="state.params.status" placeholder="请选择状态" class="w-full" clearable filterable>
          <ElOption v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem>
        <ElButton type="primary" @click="onConfirmQuery()">搜索</ElButton>
        <ElButton @click="onResetQuery()">重置</ElButton>
      </ElFormItem>
    </ElForm>
    <ElButton
      v-auth="PermissionKey.enterprise.enterpriseEmployeeCreate"
      type="primary"
      :icon="Plus"
      @click="onAddEmployeeModalVis()"
      >新增员工</ElButton
    >
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="employeeStore.employees"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="employeeStore.loading"
      @page-size-change="queryEmployee"
      @page-current-change="queryEmployee"
    >
      <template #dataPermissionType="{ row }">
        <CxTag :type="DataPermissionTypeEnumMapColor[row.dataPermissionType]">{{
          DataPermissionTypeEnumMapDesc[row.dataPermissionType]
        }}</CxTag>
      </template>
      <template #wechatUnionId="{ row }">
        <CxTag v-if="row.wechatUnionId" type="success">已绑定</CxTag>
        <CxTag v-else type="warning">未绑定</CxTag>
      </template>
      <template #operation="data">
        <div>
          <ElButton
            v-auth="PermissionKey.enterprise.enterpriseEmployeeEdit"
            type="primary"
            link
            @click="onEditEmployeeModalVis(data.row)"
          >
            编辑
          </ElButton>
          <template v-if="data.row.wechatUnionId">
            <ElButton
              v-auth="PermissionKey.enterprise.enterpriseEmployeeEdit"
              type="warning"
              link
              @click="onWeChatUnbind(data.row.id)"
            >
              解绑
            </ElButton>
          </template>

          <template v-else>
            <ElButton
              v-auth="PermissionKey.enterprise.enterpriseEmployeeEdit"
              type="primary"
              link
              @click="onWeChatbind(data.row)"
            >
              绑定
            </ElButton>
          </template>
          <reset-password-dialog v-auth="PermissionKey.enterprise.enterpriseEmployeeResetPassword" :id="data.row.id">
            <template #trigger="{ openDialog }">
              <ElButton link type="primary" @click="openDialog"> 重置密码 </ElButton>
            </template>
          </reset-password-dialog>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>

    <el-dialog
      :title="getEmployeeFormModalTitle()"
      destroy-on-close
      class="default"
      align-center
      v-model="state.employeeFormModalVis"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="onCloseEmployeeFormModal()"
    >
      <EmployeeForm :isAdd="state.isAddEmployee" ref="employeeFormRef" />
      <template #footer>
        <el-button @click="onCancelEmployeeFormModal()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSaveEmployee()">保存</el-button>
      </template>
    </el-dialog>
    <WeChatQCcodeDialog
      v-model="state.wechatQRCodeVisible"
      :employeeId="state.employeeId"
      :employeeName="state.employeeName"
      @onBindCancel="handleBindCancel()"
    />
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { reactive, ref, watch, onMounted } from "vue";
import { IEmployee, IEmployeeForm, IEmployeeReq, IOption } from "@/models";
import { ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import EmployeeForm from "./employee-form.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { usePageStoreHook } from "@/store/modules/page";
import { useEmployeeStore } from "@/store/modules/enterprise/employee";
import { StatusEnum, DataPermissionTypeEnumMapColor, DataPermissionTypeEnumMapDesc } from "@/enums";
import { PermissionKey } from "@/consts";
import ResetPasswordDialog from "./reset-password-dialog/index.vue";
import WeChatQCcodeDialog from "./wechat-qrcode/dialog.vue";
import CxTag from "@/components/CxTag/index.vue";
import { useConfirm } from "@/utils/useConfirm";
import { weChatLoginUnbindById } from "@/api/user-wechat";

usePageStoreHook().setTitle("员工管理");

const stateOptions: Array<IOption> = [
  { label: "全部", value: -1 },
  { label: "启用", value: 0 },
  { label: "禁用", value: 1 }
];
const { columns } = useColumns();
const { pagination } = useTableConfig();
const employeeStore = useEmployeeStore();
pagination.pageSize = 20;

const state = reactive<{
  employeeFormModalVis: boolean;
  isAddEmployee: boolean;
  params: IEmployeeReq;
  employeeId?: string;
  employeeName?: string;
  wechatQRCodeVisible: boolean;
}>({
  employeeFormModalVis: false,
  isAddEmployee: false,
  params: {},
  wechatQRCodeVisible: false
});

watch(
  () => employeeStore.total,
  () => {
    pagination.total = employeeStore.total;
  },
  {
    immediate: true
  }
);

onMounted(() => {
  employeeStore.$reset();
});

const saveLoading = ref<boolean>(false);
const employeeFormRef = ref<InstanceType<typeof EmployeeForm>>();

queryEmployee();

const getEmployeeFormModalTitle = () => (state.isAddEmployee ? "新增员工" : "编辑员工");
const handleSaveEmployee = useLoadingFn(onAddEmployee, saveLoading);

const onAddEmployeeModalVis = () => {
  state.isAddEmployee = true;
  state.employeeFormModalVis = true;
  employeeStore.setEmployeeForm({ status: StatusEnum.ENABLE });
};

async function onAddEmployee() {
  const formValue: IEmployeeForm | false = await employeeFormRef.value.getValidValue().catch(() => false);

  if (!formValue) {
    return;
  }

  if (!formValue.email) {
    delete formValue.email;
  }

  if (!formValue.id) {
    const { data } = await employeeStore.addEmployee(formValue);
    state.wechatQRCodeVisible = true;
    state.employeeId = data;
    return;
  } else {
    await employeeStore.editEmployee(formValue);
    state.employeeFormModalVis = false;
    ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
    pagination.currentPage = 1;
    queryEmployee();
  }
}

const onEditEmployeeModalVis = async (employee: IEmployee) => {
  state.employeeFormModalVis = true;
  state.isAddEmployee = false;
  const employeeInfo = await employeeStore.getEmployeeDetailById(employee.id);
  employeeStore.setEmployeeForm({ ...employeeInfo });
};

const onWeChatUnbind = async (id: string) => {
  if (!(await useConfirm("解绑成功后，可进行再次绑定", "确认解绑"))) {
    return;
  }
  await weChatLoginUnbindById(id);
  ElMessage.success("解绑成功");
  queryEmployee();
};

const onWeChatbind = async (data: IEmployee) => {
  state.wechatQRCodeVisible = true;
  state.employeeId = data.id;
  state.employeeName = data.nickname;
};

const onResetQuery = () => {
  state.params = {};
  pagination.currentPage = 1;
  queryEmployee();
};

const onConfirmQuery = () => {
  pagination.currentPage = 1;
  queryEmployee();
};

const onCancelEmployeeFormModal = () => {
  state.employeeFormModalVis = false;
  queryEmployee();
};

function queryEmployee() {
  employeeStore.queryEmployee({
    ...state.params,
    status: state.params?.status < 0 ? undefined : state.params.status,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  });
}

const onCloseEmployeeFormModal = () => {
  employeeStore.setAddEmployeeDetail();
};

const handleBindCancel = () => {
  state.employeeFormModalVis = false;
  pagination.currentPage = 1;
  queryEmployee();
};
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
