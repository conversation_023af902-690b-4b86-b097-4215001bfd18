import { defineComponent, h } from "vue";
import { ElOption, ElSelect } from "element-plus";
import { useI18n } from "vue-i18n";

// todo: add select props ide tips
export default defineComponent({
  name: "EnumSelect",
  props: {
    enum: {
      required: true
    },
    enumName: {
      type: String,
      required: true
    }
  },
  render() {
    let options: Array<{ label: string; value: string | number }> = [];
    if (this.enum) {
      const { t } = useI18n();
      options = Object.keys(this.enum)
        .filter(key => isNaN(Number(key)))
        .map(key => {
          const label = this.enumName ? t(`enum.${this.enumName}.${key}`) : key;
          const value = this.enum[key];
          return { label, value };
        });
    }
    const children = options.map(option =>
      h(ElOption, { label: option.label, value: option.value, key: option.value })
    );
    return h(ElSelect, { filterable: true }, () => children);
  }
});
