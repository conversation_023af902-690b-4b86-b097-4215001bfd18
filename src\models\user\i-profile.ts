import { IMenu } from "../menu";
import { ITenant } from "../platform";

export interface IProfile {
  username: string;
  nickname: string;
  remark: string;
  deptId: number;
  postIds: Array<number>;
  email: string;
  mobile: string;
  sex: number;
  avatar: string;
  id: string;
  status: number;
  loginIp: string;
  loginDate: string;
  createTime: string;
  roles: Array<IRole>;
  dept: IDept;
  posts: Array<IPost>;
  socialUsers: Array<ISocialUsers>;
  tenantInfo: ITenant;
  avatarInfo: IAvatarInfo;
  menuIds: Array<IMenu>;

  /** true： 超级管理员 */
  isSuperAdmin: boolean;
}

export interface IRole {
  id: string;
  name: string;
}

export interface IDept {
  id: number;
  name: string;
}

export interface IPost {
  id: number;
  name: string;
}

export interface ISocialUsers {
  type: number;
  openid: string;
}

export interface IAvatarInfo {
  id?: string;
  name?: string;
  url: string;
}

export interface IPasswordSafety {
  /** 租户是否启用了密码检测功能 */
  tenantEnabled: boolean;
  /** 密码强度 */
  passwordStrength: string;
  /** 是否需要提示修改密码 */
  alertChangePassword: boolean;
  /** 是否需要强制修改密码 */
  mustChangePassword: boolean;
}
