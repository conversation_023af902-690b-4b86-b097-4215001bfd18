<script setup lang="ts">
import mixNav from "./sidebar/mixNav.vue";
import { useNav } from "@/layout/hooks/useNav";
import Breadcrumb from "./sidebar/breadCrumb.vue";
import topCollapse from "./sidebar/topCollapse.vue";
import { useTranslationLang } from "../hooks/useTranslationLang";
// import Notice from "./notice/index.vue";
// import globalization from "@/assets/svg/globalization.svg?component";
import Setting from "@iconify-icons/ri/settings-3-line";
// import Check from "@iconify-icons/ep/check";
import Tenant from "./tenant/tenant.vue";
import { Download } from "@element-plus/icons-vue";
import Profile from "./profile/profile.vue";
import { useSystemAuthStore, useVersionStore } from "@/store/modules";
import { computedAsync } from "@vueuse/core";
import { WarningFilled } from "@element-plus/icons-vue";
import { onUnmounted, ref, watchEffect } from "vue";
import { emitter } from "@/utils/mitt";
import { useTimeoutFn } from "@vueuse/core";
import OperationManualVideo from "../components/operation-manual-video/index.vue";

// const { layout, device, onPanel, pureApp, toggleSideBar, getDropdownItemStyle, getDropdownItemClass } = useNav();

// const { t, locale, translationCh, translationEn } = useTranslationLang();
const systemAuthStore = useSystemAuthStore();
const versionStore = useVersionStore();
const { layout, device, onPanel, pureApp, toggleSideBar } = useNav();
const { t } = useTranslationLang();
const isEnterpriseCmp = computedAsync(() => systemAuthStore.getIsEnterprise);
const productLatestVersion = ref();
const visible = ref(false);

watchEffect(async () => {
  await versionStore.getLatestVersion();
  productLatestVersion.value = versionStore.productVersion;
});

emitter.on("exportRecordAddTip", () => {
  visible.value = true;
  useTimeoutFn(() => {
    visible.value = false;
  }, 3000);
});

onUnmounted(() => {
  emitter.off("exportRecordAddTip");
});
</script>

<template>
  <div class="navbar bg-[#fff] dark:shadow-[#0d0d0d]">
    <topCollapse
      v-if="device === 'mobile'"
      class="hamburger-container"
      :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar"
    />

    <Breadcrumb v-if="layout !== 'mix' && device !== 'mobile'" class="breadcrumb-container" />

    <mixNav v-if="layout === 'mix'" />

    <div v-if="layout === 'vertical'" class="vertical-header-right">
      <div class="inquiry-prompt" v-if="productLatestVersion?.id">
        <div class="content">
          <el-icon color="#e6a23c"><WarningFilled /></el-icon>
          <div class="current">当前产品单价版本：</div>
          <div class="version">{{ productLatestVersion.versionNumber }}</div>
          <div class="notice">{{ productLatestVersion.versionName }}</div>
          <!-- <div class="close">
            <el-icon color="#909399"><Close /></el-icon>
          </div> -->
        </div>
      </div>

      <!-- 租户-->
      <Tenant />
      <router-link to="/quotation-export-record" class="!text-inherit" title="导出记录">
        <el-icon :size="16" class="set-icon navbar-bg-hover">
          <Download />
        </el-icon>
      </router-link>
      <OperationManualVideo />
      <!-- 国际化 -->
      <!-- <el-dropdown id="header-translation" trigger="click">
        <globalization class="navbar-bg-hover w-[40px] h-[48px] p-[11px] cursor-pointer outline-none" />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'zh')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'zh')]"
              @click="translationCh"
            >
              <IconifyIconOffline class="check-zh" v-show="locale === 'zh'" :icon="Check" />
              简体中文
            </el-dropdown-item>
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'en')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'en')]"
              @click="translationEn"
            >
              <span class="check-en" v-show="locale === 'en'">
                <IconifyIconOffline :icon="Check" />
              </span>
              English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown> -->
      <!-- <Notice id="header-notice" /> -->
      <!-- 个性化配置 -- 演示不显示 -->
      <span v-if="isEnterpriseCmp" class="set-icon navbar-bg-hover" :title="t('buttons.hssystemSet')" @click="onPanel">
        <IconifyIconOffline :icon="Setting" />
      </span>
      <!-- 用户信息 -->
      <Profile />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  width: 100%;
  height: 48px;
  overflow: hidden;

  .hamburger-container {
    line-height: 48px;
    height: 100%;
    float: left;
    cursor: pointer;
  }

  .vertical-header-right {
    display: flex;
    min-width: 280px;
    height: 48px;
    align-items: center;
    color: #000000d9;
    justify-content: flex-end;
  }

  .breadcrumb-container {
    float: left;
    margin-left: 24px;

    ::v-deep(.el-breadcrumb__inner a) {
      color: var(--el-text-color-secondary);

      &:hover {
        color: var(--el-color-primary);
      }
    }

    ::v-deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner a) {
      color: var(--el-text-color-primary);
    }
  }
}

.translation {
  ::v-deep(.el-dropdown-menu__item) {
    padding: 5px 40px;
  }

  .check-zh {
    position: absolute;
    left: 20px;
  }

  .check-en {
    position: absolute;
    left: 20px;
  }
}

.inquiry-prompt {
  flex: 1;
  margin: 0 64px;

  .content {
    justify-content: center;
    padding: 4px 16px;
    border-radius: 2px;
    background: #f4f4f5;
    display: flex;
    align-items: center;
    line-height: 22px;
    font-size: 14px;
    font-weight: normal;
    letter-spacing: 0;
    position: relative;
  }

  .current {
    color: #606266;
    margin-left: 13px;
  }

  .version {
    font-size: 18px;
    font-weight: bold;
    line-height: 26px;
    color: var(--el-color-primary);
    margin: 0 12px;
  }

  .notice {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: #303133;
  }

  .close {
    width: 40px;
    text-align: right;
    position: absolute;
    right: 20px;
    margin-top: 3px;
    cursor: pointer;

    &:hover {
      svg {
        color: #f56c6c;
      }
    }
  }
}
</style>
