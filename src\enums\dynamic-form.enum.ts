/** 控件枚举类型 */
export enum EControlType {
  TextControl = "text",
  NumberTextControl = "number",
  SelectControl = "select",
  DateControl = "date",
  FileControl = "file",
  RadioControl = "radio",
  WaveRoseControl = "wave-form"
}

export const IDENTIIFY_CONTROL_TYPE_DESCIPT: Record<EControlType, string> = {
  [EControlType.TextControl]: "文本框",
  [EControlType.NumberTextControl]: "数字型文本框",
  [EControlType.SelectControl]: "下拉框",
  [EControlType.DateControl]: "日期框",
  [EControlType.FileControl]: "文件",
  [EControlType.RadioControl]: "单选框",
  [EControlType.WaveRoseControl]: "波形图"
};

export enum WaveRoseAxisEnum {
  XAxis = 1,
  YAxis = 2
}
