import { getConfig } from "@/config";
import { useEpThemeStoreHook } from "@/store/modules/epTheme";
import { darken, lighten, toggleTheme } from "@pureadmin/theme/dist/browser-utils";
import { useGlobal } from "@pureadmin/utils";
import { ref } from "vue";
import { themeColorsType } from "../types";
import { useLayout } from "./useLayout";

export function useDataThemeChange() {
  const { layoutTheme, layout } = useLayout();
  const themeColors = ref<Array<themeColorsType>>([
    /* 深色侧边（默认） */
    { color: "#00b678", themeColor: "default" },
    /* 科技蓝 */
    { color: "#3662EC", themeColor: "blue" },
    /* 普鲁士蓝 */
    { color: "#0747A6", themeColor: "prussian" },
    /* 灰蓝 */
    { color: "#4FA4D1", themeColor: "greyblue" },
    /* 竹绿 */
    { color: "#00B259", themeColor: "bamboo" },
    /* 新叶绿 */
    { color: "#67C23A", themeColor: "leaf" },
    /* 蔚蓝 */
    { color: "#409EFF", themeColor: "azure" },
    /* 青色 */
    { color: "#07B9B9", themeColor: "cyan" },
    /* 橙色 */
    { color: "#FF8F1F", themeColor: "orange" },
    /* 金黄 */
    { color: "#FFC300", themeColor: "gold" },
    /* 紫色 */
    { color: "#8A38F5", themeColor: "purple" },
    /* 赤红 */
    { color: "#FA5151", themeColor: "red" },
    /* 亮白色 */
    { color: "#ffffff", themeColor: "light" }
  ]);

  const { $storage } = useGlobal<GlobalPropertiesApi>();
  const dataTheme = ref<boolean>($storage?.layout?.darkMode);
  const body = document.documentElement as HTMLElement;

  /** 设置导航主题色 */
  function setLayoutThemeColor(theme = getConfig().Theme ?? "default") {
    layoutTheme.value.theme = theme;
    toggleTheme({
      scopeName: `layout-theme-${theme}`
    });
    $storage.layout = {
      layout: layout.value,
      theme,
      darkMode: dataTheme.value,
      sidebarStatus: $storage.layout?.sidebarStatus,
      epThemeColor: $storage.layout?.epThemeColor
    };

    if (theme === "default" || theme === "light") {
      setEpThemeColor(getConfig().EpThemeColor);
    } else {
      const colors = themeColors.value.find(v => v.themeColor === theme);
      setEpThemeColor(colors.color);
    }
  }

  function setPropertyPrimary(mode: string, i: number, color: string) {
    document.documentElement.style.setProperty(
      `--el-color-primary-${mode}-${i}`,
      dataTheme.value ? darken(color, i / 10) : lighten(color, i / 10)
    );
  }

  /** 设置 `element-plus` 主题色 */
  const setEpThemeColor = (color: string) => {
    useEpThemeStoreHook().setEpThemeColor(color);
    document.documentElement.style.setProperty("--el-color-primary", color);
    for (let i = 1; i <= 2; i++) {
      setPropertyPrimary("dark", i, color);
    }
    for (let i = 1; i <= 9; i++) {
      setPropertyPrimary("light", i, color);
    }
  };

  /** 日间、夜间主题切换 */
  function dataThemeChange() {
    /* 如果当前是light夜间主题，默认切换到default主题 */
    if (useEpThemeStoreHook().epTheme === "light" && dataTheme.value) {
      setLayoutThemeColor("default");
    } else {
      setLayoutThemeColor(useEpThemeStoreHook().epTheme);
    }

    if (dataTheme.value) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }

  return {
    body,
    dataTheme,
    layoutTheme,
    themeColors,
    dataThemeChange,
    setEpThemeColor,
    setLayoutThemeColor
  };
}
