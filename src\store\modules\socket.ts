import { defineStore } from "pinia";
import { io, Socket } from "socket.io-client";
import { reactive, watch, watchEffect } from "vue";
import { SocketEventEnum } from "@/enums";
import { useUserStore } from "@/store/modules/user";
import { SocketHandler, SocketQuery } from "@/models";

export const useSocketStore = defineStore("cx-socket", () => {
  let socket: Socket;
  let initialized = false;
  const query = reactive<SocketQuery>({} as SocketQuery);
  const eventHandlers = new Map<SocketEventEnum, Set<SocketHandler>>();

  function watchQueryAndReconnect() {
    watch(query, () => {
      if (!query.userId || !query.tenantId) {
        if (socket) {
          socket.disconnect();
          socket = null;
        }
        return;
      }
      socket ? reconnect(query) : createSocket(query);
    });
  }

  function watchProfileAndUpdateQuery() {
    watchEffect(async () => {
      const profile = useUserStore().profile;
      query.userId = profile?.id;
      query.tenantId = profile?.tenantInfo?.id;
    });
  }

  function createSocket(query: SocketQuery) {
    socket = io({ query });
    listenSupportEvents();
  }

  function reconnect(query: SocketQuery) {
    socket.io.opts.query = query;
    socket.disconnect().connect();
  }

  function listenSupportEvents() {
    Object.values(SocketEventEnum).forEach(eventName =>
      socket.on(eventName, event => eventHandlers.get(eventName)?.forEach(handler => handler?.(event)))
    );
  }

  function addEventHandler(event: SocketEventEnum, handler: SocketHandler) {
    if (eventHandlers.has(event)) {
      eventHandlers.get(event).add(handler);
    } else {
      eventHandlers.set(event, new Set([handler]));
    }
  }

  function removeEventHandler(event: SocketEventEnum, handler: SocketHandler) {
    if (!eventHandlers.has(event)) {
      return;
    }
    const handlers = eventHandlers.get(event);
    handlers.delete(handler);
    if (handlers.size === 0) {
      eventHandlers.delete(event);
    }
  }

  function setupSocket() {
    if (initialized) {
      console.warn("Socket has already initialized!");
      return;
    }
    initialized = true;
    watchQueryAndReconnect();
    watchProfileAndUpdateQuery();
  }

  function on(event: SocketEventEnum, handler: SocketHandler) {
    addEventHandler(event, handler);
    return () => removeEventHandler(event, handler);
  }

  return {
    setupSocket,
    on
  };
});

export function setupSocket() {
  useSocketStore().setupSocket();
}
