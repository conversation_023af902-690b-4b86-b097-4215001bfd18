import { http } from "@/utils/http";
import { withApiGateway } from "./util";
import { IBusinessLicense, IBusinessLicenseTenant, ILicenseVerify, IResponse } from "@/models";

/** 查询授权信息 */
export const getBusinessLicenseInfo = () => {
  return http.get<void, IResponse<IBusinessLicense>>(withApiGateway("admin-api/business/license/info-display"));
};

/** 验证证书 */
export const businessLicenseVerify = (license: ILicenseVerify): Promise<IResponse<boolean>> => {
  return http.post(withApiGateway("admin-api/business/license-management/verify"), { data: license });
};

/** 查询授权信息 没有格式化功能点 */
export const getBusinessLicenseAuth = () => {
  return http.get<void, IResponse<IBusinessLicense>>(withApiGateway("admin-api/business/license/info"));
};

/**  查询以及授权的租户列表 */
export const queryAuthTenantList = () => {
  return http.get<void, IResponse<Array<IBusinessLicense>>>(
    withApiGateway("admin-api/business/license-management/tenant-list")
  );
};

/**  解析授权文件，查看授权信息 */
export const decodeLicenseDisplayInfo = (license: string): Promise<IResponse<IBusinessLicense>> => {
  return http.post(withApiGateway("admin-api/business/license-management/view-license"), { data: { license } });
};

/** 查询未绑定的租户列表 */
export const queryUnAuthTenant = () => {
  return http.get<void, IResponse<Array<IBusinessLicenseTenant>>>(
    withApiGateway("admin-api/business/license-management/tenant-select-list")
  );
};

/** 获取单个租户授权信息 */
export const getTenantAuthInfo = (id: string) => {
  return http.get<void, IResponse<IBusinessLicense>>(
    withApiGateway(`admin-api/business/license-management/tenant-info?tenantId=${id}`)
  );
};

/** 获取单个租户授权信息[详细信息，包含物质，品类等] */
export const getTenantAuthDisplay = (id: string) => {
  return http.get<void, IResponse<IBusinessLicense>>(
    withApiGateway(`admin-api/business/license-management/tenant-display?tenantId=${id}`)
  );
};
