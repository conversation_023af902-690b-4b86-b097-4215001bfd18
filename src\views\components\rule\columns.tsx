import { ColumnWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "优先级",
      prop: "priority",
      slot: "priority",
      width: ColumnWidth.Char4,
      align: "center"
    },
    {
      label: "状态",
      prop: "enabled",
      slot: "enabled",
      width: ColumnWidth.Char4
    },
    {
      label: "规则名称",
      prop: "ruleName",
      width: ColumnWidth.Char30
    },
    {
      label: "匹配字段",
      prop: "matchFieldStrategy",
      slot: "matchFieldStrategy",
      width: ColumnWidth.Char6
    },
    {
      label: "最终展示型号",
      prop: "displayStrategy",
      slot: "displayStrategy",
      width: ColumnWidth.Char7
    },
    {
      label: "最终展示电压",
      prop: "displayStrategyVoltage",
      slot: "displayStrategyVoltage",
      width: ColumnWidth.Char7
    },
    {
      label: "创建时间",
      prop: "createdAt",
      formatter: dateFormatter()
    },
    {
      label: "创建人",
      prop: "creator"
    },
    {
      label: "更新时间",
      prop: "modifiedAt",
      formatter: dateFormatter()
    },
    {
      label: "更新人",
      prop: "updaterName"
    },
    {
      label: "备注",
      prop: "remark"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char6
    }
  ];
  return { columns };
}
