<template>
  <div class="px-5">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col>
          <el-form-item label="部门名称" prop="name">
            <el-input placeholder="请输入部门名称" v-model="form.name" clearable />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="负责人" prop="leaderUserId">
            <el-select
              placeholder="请选择负责人"
              class="w-full"
              v-model="form.leaderUserId"
              clearable
              filterable
              :loading="remoteSearchloading"
              remote
              :remote-method="remoteSearchEmployee"
            >
              <el-option v-for="item in employeesRef" :key="item.id" :label="item.nickname" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="所属部门" prop="parentId">
            <el-tree-select
              clearable
              :check-strictly="true"
              class="w-full"
              placeholder="请选择所属部门"
              v-model="form.parentId"
              :data="departmentStore.departmentTree"
              :render-after-expand="false"
            />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="状态" prop="contactName">
            <el-switch
              clearable
              v-model="form.status"
              :active-value="0"
              :inactive-value="1"
              active-text="启用"
              inactive-text="禁用"
              inline-prompt
            />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="部门简介" prop="introduction">
            <el-input
              clearable
              v-model="form.introduction"
              type="textarea"
              placeholder="请输入部门简介"
              :rows="2"
              :maxlength="200"
              :show-word-limit="true"
              resize="none"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IDepartmentForm, IEmployee } from "@/models";
import { useDepartmentStore, useEmployeeStore } from "@/store/modules";

defineExpose({
  validate,
  getValidValue
});

const employeeStore = useEmployeeStore();
const departmentStore = useDepartmentStore();
const remoteSearchloading = ref(false);

const formRef = ref<FormInstance>();
const form = reactive<IDepartmentForm>({
  id: undefined,
  name: undefined,
  parentId: undefined,
  leaderUserId: undefined,
  status: undefined,
  introduction: undefined
});
const rules = reactive<FormRules>({
  name: [{ required: true, message: requiredMessage("部门名称"), trigger: "change" }]
});

const employeesRef = ref<Array<IEmployee>>([]);

watchEffect(() => {
  if (departmentStore.departmentForm && Object.keys(departmentStore.departmentForm).length) {
    Object.assign(form, departmentStore.departmentForm);
  }
});

onMounted(async () => {
  handleSearchEmployee();
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IDepartmentForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}

const remoteSearchEmployee = (query: string) => {
  if (query) {
    remoteSearchloading.value = true;
    setTimeout(() => {
      remoteSearchloading.value = false;
      handleSearchEmployee(query);
    }, 200);
  } else {
    employeesRef.value = [];
  }
};

const handleSearchEmployee = async (query?: string) => {
  const employeeRes = await employeeStore.searchEmployee({ pageSize: 100, keyWords: query });
  employeesRef.value = employeeRes.data?.list;
};
</script>

<style scoped></style>
