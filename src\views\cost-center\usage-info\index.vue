<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- <div class="bg-bg_color px-5 pt-5 flex justify-between pb-4 items-center">
      <div>
        <div class="text-base mb-3">每日统计数据可能存在10分钟左右延迟</div>
        <div class="flex items-center gap-8">
          <div>
            <span class="text-base text-gray-500">充值余额：</span>
            <span>￥698.78 </span>
          </div>
          <div>
            <span class="text-base text-gray-500">token余额：</span>
            <span>1208,823,999 </span>
          </div>
        </div>
      </div>
      <UploadPaymentVoucherDialog mode="add">
        <template #trigger="{ openDialog }">
          <el-button :icon="Plus" type="primary" @click="openDialog">上传充值记录</el-button>
        </template>
      </UploadPaymentVoucherDialog>
    </div> -->

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-1 overflow-hidden gap-6">
      <div class="flex-1 flex flex-col gap-12">
        <div class="h-[30%]" v-loading="queryCompanyUsageTokenLoading">
          <div class="flex items-center justify-between mb-4">
            <span class="text-[16px] font-semibold">企业token用量</span>
            <div class="flex items-center gap-2">
              <el-date-picker
                type="month"
                v-model="state.companyUsageTokenExportReq.month"
                placeholder="请选择"
                value-format="YYYY-MM"
                :clearable="false"
                @change="onQueryCompanyUsageToken()"
              />
              <el-button type="primary" :loading="exportCompanyUsageTokenLoading" @click="onExportCompanyUsageToken()"
                >导出</el-button
              >
            </div>
          </div>
          <v-chart class="chart" :option="chartOption" autoresize />
        </div>
        <div class="flex-1 flex flex-col overflow-hidden">
          <div class="flex items-center justify-between mb-4">
            <div>
              <span class="text-[16px] font-semibold mr-4">员工token用量</span>
              <el-input
                v-model="state.employeeUsageTokenExportReq.userName"
                style="width: 240px"
                clearable
                placeholder="请输入员工姓名搜索"
              />
              <el-button type="primary" class="ml-4" @click="onSearchEmployeeUsageToken()">搜索</el-button>
              <el-button class="ml-4" @click="onSearcResethEmployeeUsageToken()">重置</el-button>
            </div>
            <div class="flex items-center gap-2">
              <el-date-picker
                v-model="state.employeeUsageTokenExportReq.month"
                type="month"
                :clearable="false"
                value-format="YYYY-MM"
                placeholder="请选择"
                @change="onSearchEmployeeUsageToken()"
              />
              <el-button type="primary" :loading="exportEmployeeUsageTokenLoading" @click="onExportEmployeeUsageToken()"
                >导出</el-button
              >
            </div>
          </div>
          <PureTable
            class="flex-1 overflow-hidden pagination"
            row-key="id"
            size="small"
            :data="state.employeeUsageToken"
            :columns="columns"
            :loading="queryEmployeeUsageTokenLoading"
            showOverflowTooltip
          >
            <template #tokenCount="{ row }">
              {{ formatThousands(row.tokenCount || 0) }}
            </template>

            <template #empty>
              <CxEmpty />
            </template>
          </PureTable>
        </div>
      </div>
      <!-- <div class="w-[25%]">
        <el-card class="h-full">
          <template #header>
            <div>余额告警设置</div>
          </template>
          <div class="text-base text-gray-500 mb-6">
            开启余额预警功能后，当账户余额低于指定值后，我们会通过微信服务号向指定人员发送通知
          </div>

          <el-form ref="formRef" :model="form" label-position="top">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="预警开关" prop="enable">
                  <el-switch
                    v-model="form.enable"
                    :active-value="true"
                    :inactive-value="false"
                    inline-prompt
                    active-text="开"
                    inactive-text="关"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="预警阈值" prop="threshold">
                  <el-input-number
                    class="!w-full"
                    v-model="form.threshold"
                    clearable
                    :min="0"
                    :controls="false"
                    placeholder="请请输入预警阈值"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="预警信息接收账号" prop="accountIds">
                  <el-select class="!w-full" v-model="form.accountIds" placeholder="请选择预警信息接收账号">
                    <el-option v-for="item in []" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="text-right mt-5">
              <el-button>取消</el-button>
              <el-button type="primary">保存</el-button>
            </div>
          </el-form>
        </el-card>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts" name="invoice">
// import { Plus } from "@element-plus/icons-vue";
// import UploadPaymentVoucherDialog from "@/views/cost-center/components/upload-payment-voucher/dialog.vue";
import { onMounted, reactive, ref } from "vue";
import CxEmpty from "@/components/CxEmpty";
import { BarChart, LineChart } from "echarts/charts";
import { GridComponent, LegendComponent, TooltipComponent } from "echarts/components";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import VChart from "vue-echarts";
import {
  exportCompanyDailyUsageTokenExport,
  exportEmployeeDailyUsageTokenExport,
  queryTokenUsageForCompanyDaily,
  queryTokenUsageForEmployeeDaily
} from "@/api/cost-center/token-usage";
import { IEmployeeUsageToken, IFile, ITokenUsage, ITokenUsageReq } from "@/models";
import dayjs from "dayjs";
import { formatThousands } from "@/utils/format";
import { downloadByData } from "@pureadmin/utils";
import { downLoadFile } from "@/api/upload-file";
import { ElMessage } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";

// interface ITokenBalanceWarningSet {
//   enable?: boolean;
//   threshold?: number;
//   accountIds?: Array<string>;
// }
// 注册必要的组件
use([CanvasRenderer, BarChart, LineChart, GridComponent, TooltipComponent, LegendComponent]);

// const form = reactive<ITokenBalanceWarningSet>({});
let convertUsageTokenUnit = "";
const queryEmployeeUsageTokenLoading = ref();
const queryCompanyUsageTokenLoading = ref();
const exportCompanyUsageTokenLoading = ref();
const exportEmployeeUsageTokenLoading = ref();
const state = reactive<{
  employeeUsageToken: Array<IEmployeeUsageToken>;
  employeeUsageTokenExportReq?: ITokenUsageReq;
  tokenUsage: Array<ITokenUsage>;
  companyUsageTokenExportReq?: ITokenUsageReq;
}>({
  employeeUsageToken: [],
  employeeUsageTokenExportReq: {},
  companyUsageTokenExportReq: {},
  tokenUsage: []
});

const columns: TableColumnList = [
  {
    label: "员工",
    prop: "name"
  },
  {
    label: "手机号码",
    prop: "mobile"
  },
  {
    label: "部门",
    prop: "dept"
  },
  {
    label: "累计token用量",
    prop: "tokenCount",
    slot: "tokenCount"
  }
];

const chartOption = ref({
  backgroundColor: "#fff",
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross",
      crossStyle: {
        color: "#999"
      }
    }
  },
  grid: {
    left: "3%",
    right: "3%",
    bottom: "15%",
    top: "5%",
    containLabel: true
  },
  legend: {
    data: ["token使用量"],
    itemGap: 30,
    bottom: 0,
    textStyle: {
      fontSize: 12,
      color: "#606266"
    }
  },
  xAxis: {
    type: "category",
    data: [],
    axisPointer: {
      type: "shadow"
    },
    axisLine: {
      lineStyle: {
        color: "#606266"
      }
    },
    axisLabel: {
      color: "#909399",
      fontSize: 12
    },
    axisTick: {
      show: false
    }
  },
  yAxis: [
    {
      type: "value",
      name: "",
      min: 0,
      position: "left",
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: "#909399",
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: "#EBEEF5"
        }
      }
    },
    {
      type: "value",
      name: "",
      position: "left",
      axisLabel: {
        formatter: function (value) {
          return value + convertUsageTokenUnit;
        },
        color: "#909399",
        fontSize: 12
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    }
  ],
  series: [
    {
      name: "token使用量",
      type: "line",
      yAxisIndex: 1,
      data: [],
      symbol: "circle",
      symbolSize: 8,
      itemStyle: {
        color: "#FAECD8",
        borderColor: "#FF9F43"
      },
      lineStyle: {
        width: 2,
        color: "#E6A23C"
      }
    }
  ]
});

onMounted(() => {
  const currentMonth = dayjs().format("YYYY-MM");
  state.employeeUsageTokenExportReq = {
    month: currentMonth
  };

  state.companyUsageTokenExportReq = {
    month: currentMonth
  };

  handleQueryTokenUsageForEmployeeDaily();
  handleQueryTokenUsageForCompanyDaily();
});

const onSearcResethEmployeeUsageToken = () => {
  state.employeeUsageTokenExportReq.userName = null;
  handleQueryTokenUsageForEmployeeDaily();
};

const onSearchEmployeeUsageToken = () => {
  handleQueryTokenUsageForEmployeeDaily();
};

const onExportEmployeeUsageToken = useLoadingFn(async () => {
  const { data } = await exportEmployeeDailyUsageTokenExport(state.employeeUsageTokenExportReq);
  downloadUsageTokenFile(data);
}, exportEmployeeUsageTokenLoading);

const onExportCompanyUsageToken = useLoadingFn(async () => {
  const { data } = await exportCompanyDailyUsageTokenExport(state.companyUsageTokenExportReq);
  downloadUsageTokenFile(data);
}, exportCompanyUsageTokenLoading);

const handleQueryTokenUsageForEmployeeDaily = useLoadingFn(async () => {
  const { data } = await queryTokenUsageForEmployeeDaily(state.employeeUsageTokenExportReq);
  state.employeeUsageToken = data;
}, queryEmployeeUsageTokenLoading);

const onQueryCompanyUsageToken = () => {
  handleQueryTokenUsageForCompanyDaily();
};

const handleQueryTokenUsageForCompanyDaily = useLoadingFn(async () => {
  const { data } = await queryTokenUsageForCompanyDaily(state.companyUsageTokenExportReq);
  chartOption.value.xAxis.data = data.map(item => dayjs(item.date).format("MM-DD"));
  const { unit, data: list } = usageTokenUnit(data);
  convertUsageTokenUnit = unit;
  chartOption.value.series[0].data = list.map(item => item.tokenCount);
}, queryCompanyUsageTokenLoading);

const usageTokenUnit = (list: Array<ITokenUsage>): { unit: string; data: Array<ITokenUsage> } => {
  const maxAmount = list.reduce((max, item) => Math.max(max, item.tokenCount), -Infinity);

  let _mode = "万";
  if (Math.abs(maxAmount) >= *********) {
    _mode = "亿";
  } else if (Math.abs(maxAmount) >= 10000000) {
    _mode = "千万";
  }

  switch (_mode) {
    case "万":
      list = list.map(item => ({
        ...item,
        tokenCount: Math.round((item.tokenCount / 10000 + Number.EPSILON) * 100) / 100
      }));
      break;
    case "千万":
      list = list.map(item => ({
        ...item,
        tokenCount: Math.round((item.tokenCount / 10000000 + Number.EPSILON) * 100) / 100
      }));
      break;
    case "亿":
      list = list.map(item => ({
        ...item,
        tokenCount: Math.round((item.tokenCount / ********* + Number.EPSILON) * 100) / 100
      }));
      break;
    default:
      break;
  }
  return {
    unit: _mode,
    data: list
  };
};

const downloadUsageTokenFile = async (data: IFile) => {
  const blob = await downLoadFile(data.id);
  downloadByData(blob, data.name, blob.type);
  ElMessage.success("导出成功");
};
</script>
