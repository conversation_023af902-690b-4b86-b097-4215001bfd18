<template>
  <div>
    <el-dialog
      v-model="modelValue"
      :title="isAdd ? '新增型号规格' : '编辑报价单明细'"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="40%"
      @closed="closeDialog()"
    >
      <QuotationRowForm ref="quotationRowFormRef" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { addQuotationInquiryItem, quotationInquiryEditItem } from "@/api/quotation/quotation-inquiry";
import { ElMessage } from "element-plus";
import QuotationRowForm from "./index.vue";
import { IQuotationInquiryDetail } from "@/models";
import { nextTick } from "process";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onSaveSuccess", data?: IQuotationInquiryDetail): void;
  (e: "onSaveSCancel"): void;
}>();

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    isAdd?: boolean;
    taskId?: string;
    quotationInquiryRow: IQuotationInquiryDetail;
  }>(),
  {
    isAdd: false
  }
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

const loading = ref(false);
const quotationRowFormRef = ref<InstanceType<typeof QuotationRowForm>>();
const handleSaveBtn = useLoadingFn(onSave, loading);

watch(
  () => props.quotationInquiryRow,
  quotationInquiryRow => {
    if (!quotationInquiryRow || Object.keys(quotationInquiryRow).length === 0) {
      return;
    }
    nextTick(() => {
      quotationRowFormRef.value?.initFormValue(quotationInquiryRow);
    });
  }
);

/**
 *  保存按钮点击事件
 */
async function onSave() {
  const validResult = await quotationRowFormRef.value.validateForm();
  if (!validResult) {
    return false;
  }

  const value = quotationRowFormRef.value.getFormValue();

  let msg: string;
  let quotationInquiryDetail: IQuotationInquiryDetail;
  if (!props.isAdd) {
    await quotationInquiryEditItem(value);
    msg = "修改成功";
  } else {
    value.taskId = props.taskId;
    const { data } = await addQuotationInquiryItem(value);
    quotationInquiryDetail = data;
    msg = "新增成功";
  }

  ElMessage.success(msg);
  emits("onSaveSuccess", quotationInquiryDetail);
  closeDialog();
}
function closeDialog() {
  modelValue.value = false;
  emits("onSaveSCancel");
}
</script>

<style scoped></style>
