import { IResponse, IRoleForm } from "@/models";
import { IRole } from "@/models/user";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { StatusEnum } from "@/enums";

export const queryRoleList = () => {
  return http.get<void, IResponse<Array<IRole>>>(withApiGateway("admin-api/system/role/list-all-simple"));
};

export const addRole = (data: IRoleForm) => {
  return http.post<IRoleForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/role/create"),
    { data },
    { showErrorInDialog: true }
  );
};

export const editRole = (data: IRoleForm) => {
  return http.put<IRoleForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/role/update"),
    { data },
    { showErrorInDialog: true }
  );
};

export const editRoleStatus = (id: string, status: StatusEnum): Promise<IResponse<boolean>> => {
  return http.put(withApiGateway("admin-api/system/role/update-status"), { data: { id, status } });
};

export const bindEmployeeToRole = (roleId: string, userIds: Array<string>): Promise<IResponse<boolean>> => {
  return http.put(withApiGateway("admin-api/system/role/bind-user-to-role"), { data: { roleId, userIds } });
};

export const getRoleInfoById = (roleId: string) => {
  return http.get<string, IResponse<IRole>>(withApiGateway("admin-api/system/role/get"), { params: { id: roleId } });
};

export const removeEmployeeRole = (roleId: string, userId: string) => {
  return http.delete(withApiGateway("admin-api/system/role/remove-binding"), { params: { roleId, userId } });
};

export const deleteRole = (id: string) => {
  return http.delete(withApiGateway("admin-api/system/role/delete"), { params: { id } });
};
