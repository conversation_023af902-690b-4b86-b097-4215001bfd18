// 模拟后端动态生成路由
import { MockMethod } from "vite-plugin-mock";

const quotationManagementRouter: DynamicRouteConfigsTable = {
  path: "/quotation",
  meta: {
    title: "报价管理",
    icon: "list",
    rank: 1
  },
  children: [
    {
      path: "/quotation/management",
      name: "quotation-management",
      component: "quotation/quotation-management/index",
      meta: {
        title: "报价管理"
      }
    },
    {
      path: "/quotation/risk",
      name: "quotation-risk",
      component: "quotation/risk-record/index",
      meta: {
        title: "风险记录",
        showParent: true,
        menuKey: "quotation-risk-detail"
      },
      children: [
        {
          path: "/quotation/risk/detail/:id",
          name: "quotation-risk-detail",
          component: "quotation/risk-record/risk-record-detail/index",
          meta: {
            title: "风险记录详情",
            menuKey: "quotation-risk-detail",
            showLink: false
          },
          children: []
        },

        {
          path: "/quotation/inquiry-risk",
          name: "quotation-inquiry-risk",
          component: "quotation/inquiry-risk/index",
          meta: {
            menuKey: "quotation-risk-detail",
            title: "询价风险配置",
            showLink: false
          },
          children: []
        }
      ]
    },
    {
      path: "/quotation/short-meter",
      component: "quotation/short-meter-rule/index",
      name: "quotation-short-meter",
      meta: {
        title: "短米规则"
      }
    },
    {
      path: "/quotation/history",
      name: "quotation-history",
      component: "quotation/quotation-history/index",
      meta: {
        title: "报价历史",
        menuKey: "quotation-history"
      },
      children: [
        {
          path: "/quotation/history/detail/:id",
          name: "quotation-history-detail",
          component: "quotation/quotation-history/detail/index",
          meta: {
            title: "报价历史-详情",
            menuKey: "quotation-history",
            showLink: false
          },
          children: []
        }
      ]
    }
  ]
};

const basicDataRouter: DynamicRouteConfigsTable = {
  path: "/basic",
  meta: {
    title: "基础数据",
    icon: "shield",
    rank: 1
  },
  children: [
    {
      path: "/basic/product",
      name: "basic-product",
      component: "basic/product-info/index",
      meta: {
        title: "产品信息"
      },
      children: []
    },
    {
      path: "/basic/inquiry",
      component: "basic/inquiry-organization/index",
      name: "inquiry-unit",
      meta: {
        title: "询价单位"
      },
      children: []
    },
    {
      path: "/basic/equivalent",
      name: "equivalent",
      component: "basic/equivalent/index",
      meta: {
        title: "等效命名"
      },
      children: []
    },
    {
      path: "/basic/quotation",
      name: "quotation-template",
      component: "basic/quotation-template/index",
      meta: {
        title: "报价模板"
      },
      children: []
    }
  ]
};

const enterpriseRouter: DynamicRouteConfigsTable = {
  path: "/enterprise",
  meta: {
    title: "企业管理",
    icon: "building",
    rank: 10
  },
  children: [
    {
      path: "/enterprise-info",
      name: "enterprise-info",
      component: "enterprise/enterprise-info/index",
      meta: {
        title: "企业信息"
      }
    },
    {
      path: "/remittance-info",
      name: "remittance-info",
      component: "enterprise/remittance-info/index",
      meta: {
        title: "汇款信息"
      }
    },
    {
      path: "/employee",
      name: "employee",
      component: "enterprise/employee/index",
      meta: {
        title: "员工管理"
      }
    },
    {
      path: "/department",
      name: "department",
      component: "enterprise/department/index",
      meta: {
        title: "部门管理"
      }
    },
    {
      path: "/role",
      component: "enterprise/role/index",
      name: "role",
      meta: {
        title: "角色权限"
      }
    }
  ]
};

/** 系统内置表单 */
const tenantRouter: DynamicRouteConfigsTable = {
  path: "/platform",
  meta: {
    title: "平台管理",
    icon: "platform",
    rank: 10
  },
  children: [
    // {
    //   path: "/auth",
    //   name: "auth",
    //   component: "platform/system-auth/index",
    //   meta: {
    //     title: "系统授权",
    //     rank: 10
    //   }
    // },
    {
      path: "/tenant",
      name: "tenant",
      component: "platform/tenant/index",
      meta: {
        title: "租户管理",
        menuKey: "tenant",
        rank: 10
      },
      children: [
        {
          path: "/platform/token/:id",
          name: "密钥",
          component: "open-platform/token/index",
          meta: {
            showLink: false,
            title: "OpenAPI凭证管理",
            menuKey: "tenant"
          }
        }
      ]
    },
    {
      path: "/account",
      name: "account",
      component: "platform/account/index",
      meta: {
        title: "账号管理"
      }
    },
    {
      path: "/operational-statistics",
      name: "operational-statistics",
      component: "platform/operational-statistics/index",
      meta: {
        title: "运营统计"
      }
    },
    {
      path: "/account/review",
      name: "account-review",
      component: "platform/account-review/index",
      meta: {
        title: "账号审核"
      }
    },
    {
      path: "/basic/rule",
      name: "basic-quotation-rule",
      component: "basic/rule-engine/index",
      meta: {
        title: "租户规则库"
      },
      children: []
    },
    {
      path: "/platform/rule",
      name: "platform-quotation-rule",
      component: "platform/rule-engine/index",
      meta: {
        title: "平台规则库"
      },
      children: []
    }
  ]
};

// 个人中心
const accountRouter: DynamicRouteConfigsTable = {
  path: "/account",
  meta: {
    title: "账号",
    icon: "admin",
    rank: 10,
    showLink: false
  },
  children: [
    {
      path: "/account-profile",
      name: "个人中心",
      component: "profile/index",
      meta: {
        showParent: false,
        showLink: false,
        title: "个人中心"
      }
    }
  ]
};

// 数据看板
const dashboardRouter: DynamicRouteConfigsTable = {
  path: "/welcome",
  meta: {
    title: "数据看板",
    icon: "lineChart",
    rank: 10,
    showLink: false
  },
  children: [
    {
      path: "/dashboard",
      name: "数据看板",
      component: "welcome/dashboard/dashboard",
      meta: {
        showParent: false,
        showLink: false,
        title: "数据看板"
      }
    }
  ]
};

// 日志监控
const log: DynamicRouteConfigsTable = {
  path: "/logging",
  meta: {
    title: "日志监控",
    icon: "calendar",
    rank: 10
  },
  children: [
    {
      path: "/login-logging",
      name: "loginLogging",
      component: "logging/login/index",
      meta: {
        title: "登录日志"
      }
    }
  ]
};
// 导出记录
const reportExportRecord: DynamicRouteConfigsTable = {
  path: "/report-export",
  meta: {
    title: "导出记录",
    showLink: false,
    rank: 11
  },
  children: [
    {
      path: "/quotation-export-record",
      name: "quotation-export-record",
      component: "report-center/quotation-export-record/index",
      meta: {
        title: "报价单导出记录",
        showLink: false
      }
    }
  ]
};

// 费用中心
const costCenter: DynamicRouteConfigsTable = {
  path: "/cost",
  meta: {
    title: "费用中心",
    icon: "calendar",
    rank: 10
  },
  children: [
    {
      path: "/cost/invoice",
      name: "cost-invoice",
      component: "cost-center/invoice/index",
      meta: {
        title: "账单管理"
      }
    },
    {
      path: "/cost/token",
      name: "cost-usage-info",
      component: "cost-center/usage-info/index",
      meta: {
        title: "用量信息"
      }
    }
  ]
};

// 报价基础数据管理
const quoteBasicDataRouter: DynamicRouteConfigsTable = {
  path: "/quote-basic-data",
  meta: {
    title: "报价基础数据",
    icon: "calendar",
    rank: 10
  },
  children: [
    {
      path: "/basic/bom",
      name: "bom-information",
      component: "quote-basic-data/bom-information/index",
      meta: {
        title: "BOM信息"
      }
    },
    {
      path: "/basic/material",
      name: "raw-material-information",
      component: "quote-basic-data/raw-material-information/index",
      meta: {
        title: "原材料信息",
        menuKey: "raw-material-information"
      },
      children: [
        {
          path: "/basic/material/:id",
          name: "cost-invoice",
          component: "quote-basic-data/raw-material-information/detail/index",
          meta: {
            title: "原材料成本详情",
            showLink: false,
            menuKey: "raw-material-information"
          }
        }
      ]
    }
    // {
    //   path: "/basic/material-cost",
    //   name: "raw-material-cost",
    //   component: "quote-basic-data/raw-material-cost/index",
    //   meta: {
    //     title: "原材料成本维护",
    //     menuKey: "raw-material-cost"
    //   },
    //   children: [
    //     {
    //       path: "/basic/material-cost/detail/:id",
    //       name: "cost-invoice",
    //       component: "quote-basic-data/raw-material-cost/detail/index",
    //       meta: {
    //         title: "原材料成本详情",
    //         showLink: false,
    //         menuKey: "raw-material-cost"
    //       }
    //     }
    //   ]
    // }
  ]
};

export default [
  {
    url: "/getAsyncRoutes",
    method: "get",
    response: () => {
      return {
        success: true,
        data: [
          log,
          costCenter,
          quotationManagementRouter,
          enterpriseRouter,
          basicDataRouter,
          tenantRouter,
          accountRouter,
          dashboardRouter,
          reportExportRecord,
          quoteBasicDataRouter
        ]
      };
    }
  }
] as MockMethod[];
