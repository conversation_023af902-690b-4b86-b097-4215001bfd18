import { RechargeTypeEnum, VerificationStatusEnum } from "@/enums";
import { IBase } from "@/models";

/** 费用中心 */
export interface IInvoice extends IBase {
  /**
   * 订单编号
   */
  orderNo?: string;
  /**
   * 状态
   */
  status?: VerificationStatusEnum;
  /**
   * 类型
   */
  type?: RechargeTypeEnum;
  /**
   * 充值金额
   */
  rechargeAmount?: number;
  /**
   * 充值token
   */
  rechargeToken?: number;
  /**
   * 实际到账token
   */
  actualToken?: number;
  /**
   * 充值时间
   */
  rechargeTime?: string;
  /**
   * 提交人
   */
  submitter?: string;
  /**
   * 备注
   */
  remark?: string;
}
