<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" />
    <el-dialog
      v-model="drawerVisible"
      :title="isEditMode ? '编辑' : '新增'"
      width="65%"
      class="!mt-[6vh]"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog()"
    >
      <RuleDefinitionForm v-loading="queryLoading" class="h-[70vh]" ref="formRef" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button v-if="!props.id" type="warning" @click="handleSaveContinueAddBtn" :loading="saveContinueAddLoading"
            >保存、并继续新增</el-button
          >
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import IRuleForm from "./form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import RuleDefinitionForm from "./index.vue";
import { createRuleDefinition, updateRuleDefinition, getRuleDefinitionById } from "@/api";
import { RuleScopeEnum } from "@/enums";
const loading = ref(false);

defineExpose({
  openDialog
});

const props = defineProps<{
  mode: "edit" | "add";
  ruleScope: RuleScopeEnum;
  id?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);
let saveContinueAddTag = false;
const formRef = ref<InstanceType<typeof IRuleForm>>();
const saveContinueAddLoading = ref(false);
const queryLoading = ref(false);
const drawerVisible = ref(false);
/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");
/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");
const handleSaveBtn = useLoadingFn(onSave, loading);
const handleSaveContinueAddBtn = useLoadingFn(handleSaveContinueAddLoading, saveContinueAddLoading);
const handleGetRuleDefinitionById = useLoadingFn(getRuleDefinitionById, queryLoading);

// 订阅弹窗开启状态，请求数据
watch(drawerVisible, async visible => {
  if (!visible || !isEditMode.value) {
    return;
  }
  const { data } = await handleGetRuleDefinitionById(props.id, props.ruleScope);
  formRef.value.initFormValue(data);
});

async function handleSaveContinueAddLoading() {
  const result = await handleSave();
  if (!result) {
    return;
  }
  saveContinueAddTag = true;
  formRef.value.emptyFormValue();
  ElMessage({ message: isAddMode.value ? "新增成功" : "编辑成功", type: "success" });
}

/**
 *  保存按钮点击事件
 */
async function onSave() {
  const result = await handleSave();
  if (!result) {
    return;
  }

  closeDialog();
  emits("postSaveSuccess");
  ElMessage({ message: isAddMode.value ? "新增成功" : "编辑成功", type: "success" });
}

async function handleSave() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return false;
  }

  const formVal = formRef.value.getFormValue();
  if (isAddMode.value) {
    await createRuleDefinition(formVal, props.ruleScope);
  } else {
    await updateRuleDefinition(formVal, props.ruleScope);
  }
  return true;
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  drawerVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  if (saveContinueAddTag) {
    emits("postSaveSuccess");
  }
  saveContinueAddTag = false;
  drawerVisible.value = false;
}
</script>
