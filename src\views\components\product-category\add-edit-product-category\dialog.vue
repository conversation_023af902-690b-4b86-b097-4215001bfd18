<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" />

    <el-dialog
      v-model="drawerVisible"
      title="新增"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <CategoryForm ref="formRef" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import CategoryForm from "./form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getProductCategoryById, createProductCategory, updateProductCategory } from "@/api/product-category";

const loading = ref(false);
const props = defineProps<{
  mode: "edit" | "add";
  id?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const formRef = ref<InstanceType<typeof CategoryForm>>();
const drawerVisible = ref(false);
/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");
/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");
const handleSaveBtn = useLoadingFn(onSave, loading);
// 订阅弹窗开启状态，请求数据
watch(drawerVisible, async visible => {
  if (!visible || !isEditMode.value) {
    return;
  }
  const { data } = await getProductCategoryById(props.id);
  formRef.value.initFormValue(data);
});

/**
 *  保存按钮点击事件
 */
async function onSave() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }

  const formVal = formRef.value.getFormValue();
  if (isAddMode.value) {
    await createProductCategory(formVal);
  } else {
    await updateProductCategory(formVal);
  }

  closeDialog();
  emits("postSaveSuccess");
  ElMessage({ message: isAddMode.value ? "新增成功" : "编辑成功", type: "success" });
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  drawerVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  drawerVisible.value = false;
}
</script>

<style scoped></style>
