<template>
  <div class="login-container">
    <div class="bg-cude absolute" />
    <header>
      <div class="absolute z-10 left-10 top-10">
        <div class="login-logo-group">
          <component class="logo" :is="toRaw(logo)" />
        </div>
      </div>
      <div class="flex-c absolute space-x-2 right-10 top-10">
        <div class="flex-c text-base space-x-2">
          <FontIcon icon="icon-telephone" class="text-primary-color" />
          <span class="text-regular">全国服务电话</span>
          <a class="text-regular" :href="`tel:${tel}`">{{ tel }}</a>
        </div>
      </div>
    </header>
    <main class="quick-container absolute z-10 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
      <h1 class="title text-center">AI报价软件</h1>
      <p class="subtitle text-center">让AI赋能企业数字化转型，智能识别线缆参数，快速生成精准报价</p>
      <div class="quick-access-grid">
        <QuickAccess
          v-for="(entry, index) in entries"
          :key="entry.title"
          :title="entry.title"
          :path="entry.path"
          :icon="entry.icon"
          :titleColor="entry.titleColor"
          :bgColor="entry.bgColor"
          :noPermission="entry.noPermission"
          :class="{ 'grid-item-wide': index === 0 }"
        />
      </div>
    </main>
    <footer class="left-0 right-0 text-center text-xs text-gray-400">
      <div class="text-secondary text-sm">
        Copyright © 2017-{{ currentYear }} {{ t("login.company") }}. All Rights Reserved
      </div>
      <div class="flex-c gap-5 mt-2">
        <div class="flex">
          <div style="width: 16px; margin-right: 4px">
            <img style="object-fit: contain" :src="beian" alt="beian" />
          </div>
          <a href="https://beian.miit.gov.cn" target="_blank">沪ICP备18008973号-1</a>
        </div>
        <div class="flex">
          <div style="width: 16px; margin-right: 4px">
            <img style="object-fit: contain" :src="beian" alt="beian" />
          </div>
          <a href="http://www.beian.gov.cn" target="_blank">沪公网安备31011002002604号</a>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import IconCompany from "@/assets/img/quick/icon-company.png";
import IconData from "@/assets/img/quick/icon-data.png";
import IconHistory from "@/assets/img/quick/icon-history.png";
import IconKanban from "@/assets/img/quick/icon-kanban.png";
import IconOffer from "@/assets/img/quick/icon-offer.png";
import IconOpenCenter from "@/assets/img/quick/icon-opencenter.png";
import IconRisk from "@/assets/img/quick/icon-risk.png";
import beian from "@/assets/img/beian.png";
import logo from "@/assets/svg/login_logo.svg";
import { onMounted, ref, toRaw } from "vue";
import { useI18n } from "vue-i18n";
import QuickAccess from "./quick-access.vue";
import { PermissionKey } from "@/consts";
import { useUserStoreHook } from "@/store/modules/user";

const tel = "************";

const { t } = useI18n();

const currentYear = new Date().getFullYear();

// 快捷入口数据
const entries = ref([
  {
    title: "报价管理",
    path: "/quotation/management",
    icon: IconOffer,
    titleColor: "#ffffff",
    bgColor: "linear-gradient(180deg, #13B2B2 0%, #90E4D6 100%)",
    permissions: [PermissionKey.quotation.quotation, PermissionKey.quotation.quotationManagement],
    noPermission: true
  },
  {
    title: "报价历史",
    path: "/quotation/history",
    icon: IconHistory,
    titleColor: "#2E5F69",
    bgColor: "linear-gradient(158deg, #CEEEEA 19%, #ECF6F0 48%)",
    permissions: [PermissionKey.quotation.quotation, PermissionKey.quotation.quotationHistory],
    noPermission: true
  },
  {
    title: "询价风险",
    path: "/quotation/risk",
    icon: IconRisk,
    titleColor: "#2E5F69",
    bgColor: "linear-gradient(158deg, #CEE6EE 19%, #ECF6F6 48%)",
    permissions: [
      PermissionKey.quotation.quotation,
      PermissionKey.quotation.quotationRisk,
      PermissionKey.quotation.quotationRiskConfig
    ],
    noPermission: true
  },
  {
    title: "基础数据管理",
    path: "/basic/product",
    icon: IconData,
    titleColor: "#724F6A",
    bgColor: "linear-gradient(158deg, #EEE1FF 19%, #F8F5FA 48%)",
    permissions: [
      PermissionKey.meta.metaPproduct,
      PermissionKey.meta.metaCustomer,
      PermissionKey.meta.metaNameMap,
      PermissionKey.meta.metaQuotationTemp
    ],
    noPermission: true
  },
  {
    title: "数据看板",
    path: "/dashboard",
    icon: IconKanban,
    titleColor: "#2E5F69",
    bgColor: "linear-gradient(158deg, #CEEED8 19%, #ECF6EE 48%)",
    permissions: [PermissionKey.workbench.workbenchView, PermissionKey.workbench.dashboardView],
    noPermission: true
  },
  {
    title: "企业管理",
    path: "/enterprise-info",
    icon: IconCompany,
    titleColor: "#2E5F69",
    bgColor: "linear-gradient(158deg, #D0E5F4 19%, #ECF6F6 48%)",
    permissions: [
      PermissionKey.enterprise.enterprise,
      PermissionKey.enterprise.enterpriseEmployee,
      PermissionKey.enterprise.enterpriseDepartment,
      PermissionKey.enterprise.enterpriseRole
    ],
    noPermission: true
  },
  {
    title: "开放中心",
    path: "/auth",
    icon: IconOpenCenter,
    titleColor: "#695B2E",
    bgColor: "linear-gradient(158deg, #EDECD4 19%, #F2F6EC 48%)",
    permissions: [],
    noPermission: true
  }
]);

onMounted(() => {
  const allAuth = useUserStoreHook().getAuth;
  entries.value.forEach(item => {
    item.noPermission = !item.permissions?.some(permission => allAuth?.includes(permission));
  });
});
</script>

<style scoped lang="scss">
.login-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)),
    url("../../assets/img/home_bg_xl.png") no-repeat center bottom;
  background-attachment: fixed;
  background-size: cover;
}

.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.bg-cude {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 20%;
  background: linear-gradient(180deg, #deecf1 0%, rgba(222, 236, 241, 0) 100%);
}

.text-primary-color {
  color: #00b678;
}

.quick-container {
  .title {
    color: #3d3d3d;
    font-size: 48px;
    font-weight: 600;
  }

  .subtitle {
    color: #3d3d3d;
    font-size: 20px;
    margin-top: 8px;
  }
}

.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 60px auto;
  padding: 0 100px;
}

.grid-item-wide {
  grid-column: span 2;
}

.quick-access {
  width: 100%;
  height: 220px;
  margin: 0;
}

main {
  flex: 1;
  width: 100vw;
}

footer {
  position: absolute;
  width: 100vw;
  bottom: 20px;
  text-align: center;
  padding: 15px;
}

@media screen and (max-width: 1024px) {
  .login-container {
    min-width: 1024px;
    overflow: auto;
  }
}
</style>
