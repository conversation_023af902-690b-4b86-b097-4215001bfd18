export enum ErrorType {
  SUCCESS,
  INFO,
  WARNING,
  ERROR
}

export type ElErrorType = "info" | "success" | "warning" | "error";

export function getElErrorType(type: ErrorType): ElErrorType {
  switch (type) {
    case ErrorType.SUCCESS:
      return "success";
    case ErrorType.INFO:
      return "info";
    case ErrorType.WARNING:
      return "warning";
    case ErrorType.ERROR:
      return "error";
  }
}

export function getErrorTitle(type: ErrorType): string {
  switch (type) {
    case ErrorType.SUCCESS:
      return "成功";
    case ErrorType.INFO:
      return "信息";
    case ErrorType.WARNING:
      return "提示";
    case ErrorType.ERROR:
      return "错误";
  }
}
