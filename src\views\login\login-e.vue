<template>
  <div class="login-container relative select-none">
    <div class="main">
      <div class="header">
        <img :src="LoginLogo" alt="bg" />
      </div>
      <div class="login-content">
        <el-form ref="formRef" :model="form" :rules="loginRules" size="large" class="login-form" @submit.prevent>
          <el-form-item prop="username">
            <el-input
              clearable
              :placeholder="t('login.username')"
              :prefix-icon="useRenderIcon(user)"
              v-model="form.username"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              class="pwd"
              clearable
              show-password
              :placeholder="t('login.password')"
              :prefix-icon="useRenderIcon(lock)"
              v-model="form.password"
            />
          </el-form-item>
          <div class="login-btn">
            <el-button
              class="w-full"
              color="#4FA4D1"
              size="large"
              native-type="submit"
              :loading="loading"
              @click="onLogin(formRef)"
              >{{ t("login.login") }}</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="footer">Copyright © 2017-{{ currentYear }} {{ t("login.company") }}. All Rights Reserved</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import LoginLogo from "@/assets/img/login_logo-e.png";
import { useI18n } from "vue-i18n";
import { loginRules } from "./utils/rule";
import { user, lock } from "./utils/static";
import type { FormInstance } from "element-plus";
import { useLoginHook } from "./login-hook";
import { useLayout } from "@/layout/hooks/useLayout";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const formRef = ref<FormInstance>();
const { login, loading, form } = useLoginHook();
const currentYear = new Date().getFullYear();

const { initStorage } = useLayout();
initStorage();
const { t } = useI18n();

const onLogin = async (formEl: FormInstance | undefined) => {
  loading.value = true;
  login(formEl);
};
</script>

<style scoped lang="scss">
.el-input {
  --el-input-focus-border: #4fa4d1;
  --el-input-focus-border-color: #4fa4d1;
}

.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #4fa4d1;

  .main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 480px;
    border-radius: 6px;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 57%, #dcedf6 100%);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.2);
  }

  .header {
    padding: 80px 0;
  }

  .login-content {
    width: 100%;
    padding: 0 80px;
    margin-bottom: 10px;

    .login-form {
      width: 100%;
    }

    .pwd {
      font-family: "auto";
    }

    .login-btn {
      margin-top: 40px;
      text-align: right;

      .el-button {
        color: #ffffff;
      }
    }
  }

  .footer {
    @apply w-full text-center text-secondary text-sm py-11;
  }
}
</style>
