<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <TitleBar title="员工信息" class="mb-3" />
    <el-row :gutter="40" class="mb-4">
      <el-col :span="12">
        <el-form-item label="用户名" prop="username">
          <el-input placeholder="请输入用户名" v-model="form.username" :disabled="disabled" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="手机号码" prop="mobile">
          <el-input placeholder="请输入手机号码" v-model="form.mobile" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="姓名" prop="nickname">
          <el-input placeholder="请输入姓名" v-model="form.nickname" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="邮箱" prop="email">
          <el-input placeholder="请输入邮箱" v-model="form.email" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="账号类型" prop="dataPermissionType">
          <el-radio-group v-model="form.dataPermissionType">
            <el-radio :label="item.value" v-for="(item, index) in DataPermissionTypeEnumOption" :key="index">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            :active-value="0"
            :inactive-value="1"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
          />
        </el-form-item>
      </el-col>
    </el-row>

    <TitleBar title="其他信息" class="mb-3" />
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="所属部门" prop="deptIds">
          <el-tree-select
            clearable
            multiple
            :check-strictly="true"
            class="w-full"
            placeholder="请选择所属部门"
            v-model="form.deptIds"
            :data="departmentStore.departmentTree"
            :render-after-expand="false"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="系统角色"
          prop="roleIds"
          :rules="{
            required: roleRequired,
            message: '系统角色不能为空',
            trigger: 'change'
          }"
        >
          <el-select placeholder="请选择角色" class="w-full" v-model="form.roleIds" clearable filterable multiple>
            <el-option v-for="item in roleStore.roles || []" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <template v-if="!props.isAdd">
      <TitleBar title="微信绑定" class="mb-3" />
      <el-row :gutter="40">
        <el-col :span="12">
          <div class="flex items-center gap-4 cursor-pointer" @click="onWeChatQRCodeShow()">
            <img :src="WeChatIcon" class="w-8 h-8" />
            <div v-if="form.wechatUnionId">已绑定</div>
            <div v-else>绑定</div>
          </div>
        </el-col>
      </el-row>
    </template>

    <!-- <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="2"
            :maxlength="200"
            :show-word-limit="true"
            resize="none"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row> -->
    <WeChatQCcodeDialog v-model="wechatQRCodeVisible" :employeeId="form.id" :wechatUnionId="form.wechatUnionId" />
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IEmployeeForm } from "@/models";
import { useDepartmentStore, useEmployeeStore, useRoleStore, useSystemAuthStore } from "@/store/modules";
import { EMAIL_REGEXP, MOBILE_REGEXP, USER_NAME_REGEXP } from "@/consts";
import TitleBar from "@/components/TitleBar";
import { DataPermissionTypeEnum, LicenseEnum, DataPermissionTypeEnumOption } from "@/enums";
import WeChatIcon from "@/assets/img/wechat.png";
import WeChatQCcodeDialog from "./wechat-qrcode/dialog.vue";

const props = defineProps<{
  isAdd: boolean;
}>();

defineExpose({
  validate,
  getValidValue
});

const employeeStore = useEmployeeStore();
const roleStore = useRoleStore();
const departmentStore = useDepartmentStore();
const systemAuthStore = useSystemAuthStore();
const roleRequired = ref<boolean>();
const formRef = ref<FormInstance>();
const wechatQRCodeVisible = ref<boolean>(false);
const form = reactive<IEmployeeForm>({
  id: undefined,
  username: undefined,
  mobile: undefined,
  nickname: undefined,
  sex: undefined,
  email: undefined,
  status: undefined,
  password: undefined,
  remark: undefined,
  roleIds: [],
  deptIds: undefined,
  dataPermissionType: DataPermissionTypeEnum.DEFAULT
});
const rules: FormRules = {
  username: props.isAdd
    ? [
        { required: true, trigger: "change", message: "用户名不能为空" },
        {
          required: true,
          message: "3到20字符,可包含 字母 数字 @ . _",
          trigger: "change",
          pattern: USER_NAME_REGEXP
        }
      ]
    : [{ required: true, trigger: "change", message: "用户名不能为空" }],
  mobile: [
    { required: true, trigger: "change", message: "手机号码不能为空" },
    { trigger: "change", message: "手机号码格式不正确", pattern: MOBILE_REGEXP }
  ],
  nickname: [{ required: true, message: requiredMessage("姓名"), trigger: "change" }],
  email: [{ trigger: "change", message: "邮箱格式不正确", pattern: EMAIL_REGEXP }]
};

roleStore.queryRoleList();
departmentStore.queryDepartmentTree();

const disabled = ref<boolean>();
watchEffect(() => {
  if (employeeStore.employeeForm && Object.keys(employeeStore.employeeForm).length) {
    Object.assign(form, employeeStore.employeeForm);
  }
  disabled.value = !!employeeStore.employeeForm?.id;
});

watchEffect(() => {
  roleRequired.value = systemAuthStore.businessLicenseAuth?.authorization?.edition === LicenseEnum.Lite;
});

const onWeChatQRCodeShow = () => {
  if (form.wechatUnionId) {
    return;
  }
  wechatQRCodeVisible.value = true;
};

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IEmployeeForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}
</script>

<style scoped></style>
