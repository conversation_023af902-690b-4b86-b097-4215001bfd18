<template>
  <div class="flex flex-col p-5 h-full">
    <Headline title="询价单数量TOP" />
    <div class="flex flex-col flex-1 overflow-hidden">
      <el-scrollbar>
        <div class="ranking-list">
          <div v-for="(item, index) in rankingData" :key="index" class="ranking-item">
            <div class="ranking-info">
              <div class="ranking-number" :class="getNumberClass(index)">
                <template v-if="index < 3">
                  <img :src="getRankingImage(index)" alt="ranking" class="ranking-medal" />
                </template>
                <template v-else>
                  {{ index + 1 }}
                </template>
              </div>
              <div class="ranking-name">{{ item.inquiryOrganizationName }}</div>
            </div>
            <div class="ranking-count font-numeric">{{ item.inquiryCount }} 单</div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getInquiryOrganizationList } from "@/api/dashboard";
import BronzeMedal from "@/assets/img/dashboard/bronze-medal.png"; // 铜牌
import GoldMedal from "@/assets/img/dashboard/gold-medal.png"; // 金牌
import SilverMedal from "@/assets/img/dashboard/silver-medal.png"; // 银牌
import { IDateRange } from "@/models";
import { ref, watch } from "vue";
import Headline from "./components/headline.vue";

defineExpose({ handleGetInquiryOrganizationList });

const rankingData = ref([]);
const props = withDefaults(
  defineProps<{
    dateRange: IDateRange;
  }>(),
  {}
);

watch(
  () => props.dateRange,
  dateRange => {
    if (dateRange) {
      handleGetInquiryOrganizationList(dateRange);
    }
  },
  { immediate: true }
);

// 获取排名图片
const getRankingImage = (index: number) => {
  const rankImages = [GoldMedal, SilverMedal, BronzeMedal];
  return rankImages[index];
};

// 获取排名样式类
const getNumberClass = (index: number) => {
  if (index < 3) return "rank-medal";
  return "rank-normal";
};

async function handleGetInquiryOrganizationList(data: IDateRange) {
  const { data: list } = await getInquiryOrganizationList(data);
  rankingData.value = list;
}
</script>

<style lang="scss" scoped>
.ranking-list {
  padding: 0 10px;
  width: 100%;
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 6px 0;
}

.ranking-info {
  display: flex;
  align-items: center;
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-right: 20px;
  color: #ffffff;
}

.ranking-medal {
  width: 24px;
  height: 24px;
}

.rank-medal {
  display: flex;
  align-items: center;
  justify-content: center;
}

.rank-normal {
  color: var(--el-text-color-primary);
}

.ranking-name {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.ranking-count {
  font-size: 14px;
  color: var(--el-text-color-primary);
  width: 80px;
  text-align: right;
}
</style>
