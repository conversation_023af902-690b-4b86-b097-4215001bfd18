<template>
  <div class="card-container">
    <div class="flex-1">
      <DataCard
        title="当前在线人数"
        :iconSrc="userIcon"
        :bgColor="userBGColor"
        :cudeSrc="userCude"
        :illustration-src="userIllustration"
      >
        <template #content>
          <div class="user-content">
            <div class="value-wrapper flex items-baseline">
              <span class="value">{{ currentOnlineCount || "0" }}</span>
              <span class="unit">峰值：</span>
              <span class="sub-value">{{ totalNumber }}</span>
            </div>
            <div class="w-1/2 h-[50px]" ref="chartContainer" />
            <div class="description">近7日在线人数趋势</div>
          </div>
        </template>
      </DataCard>
    </div>
    <div class="fix-width">
      <DataCard
        title="今日询价风险"
        :iconSrc="riskIcon"
        :bgColor="riskBGColor"
        :cudeSrc="riskCude"
        :illustration-src="riskIllustration"
      >
        <template #content>
          <div class="risk-content">
            <span class="risk-num">{{ todayInquiryRisk || "0" }}</span
            >/<span>{{ totalInquiryRisk }}</span>
          </div>
        </template>
      </DataCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getOnlineData } from "@/api/dashboard";
import riskCude from "@/assets/img/dashboard/risk-cude.png";
import riskIcon from "@/assets/img/dashboard/risk-icon.png";
import riskIllustration from "@/assets/img/dashboard/risk-illustration.png";
import userCude from "@/assets/img/dashboard/user-cude.png";
import userIcon from "@/assets/img/dashboard/user-icon.png";
import userIllustration from "@/assets/img/dashboard/user-illustration.png";
import { IDataStatistics } from "@/models";
import { formatDecimal } from "@/utils/format";
import { useECharts, UtilsEChartsOption } from "@pureadmin/utils";
import { ref, watch } from "vue";
import DataCard from "./components/data-card.vue";

const userBGColor = "linear-gradient(180deg, rgba(226, 249, 241, 0.7) 0%, rgba(226, 249, 241, 0) 56%), #FFFFFF;";
const riskBGColor = "linear-gradient(180deg, rgba(250, 234, 234, 0.52) 0%, rgba(250, 234, 234, 0) 61%), #FFFFFF;";

const chartContainer = ref<HTMLDivElement>();

const { setOptions } = useECharts(chartContainer);
const todayInquiryRisk = ref();
const totalInquiryRisk = ref();
const currentOnlineCount = ref();
const totalNumber = ref();

const props = withDefaults(
  defineProps<{
    data: IDataStatistics;
  }>(),
  {}
);

const options = ref<UtilsEChartsOption>({
  grid: {
    left: 1,
    right: 1,
    bottom: 1,
    top: 1
  },
  tooltip: {
    trigger: "axis",
    formatter: params => `${formatDecimal(params[0].data)}`
  },
  xAxis: {
    type: "category",
    show: false,
    boundaryGap: false
  },
  yAxis: {
    type: "value",
    show: false
  },
  series: [
    {
      type: "line",
      smooth: true,
      symbol: "none",
      sampling: "lttb",
      areaStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(0, 194, 146, 0.3)" // 渐变起始颜色
            },
            {
              offset: 1,
              color: "rgba(0, 194, 146, 0)" // 渐变结束颜色
            }
          ],
          global: false
        }
      },
      itemStyle: {
        color: "#00C292"
      },
      data: []
    }
  ]
});

watch(chartContainer, async () => {
  if (chartContainer.value) {
    const { data } = await getOnlineData();
    options.value.series[0].data = data;
    setOptions(options.value as UtilsEChartsOption);
  }
});

watch(
  () => props.data,
  data => {
    if (data) {
      todayInquiryRisk.value = data.todayInquiryRisk;
      totalInquiryRisk.value = data.totalInquiryRisk;
      currentOnlineCount.value = data.onlineNumber;
      totalNumber.value = data.totalNumber;
    }
  },
  {
    immediate: true
  }
);
</script>

<style lang="scss" scoped>
.card-container {
  display: flex;
  height: 100%;
  gap: 20px;
}

.fix-width {
  width: 40%;
}

.user-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 60px;

  .value-wrapper {
    span {
      display: inline-block;
    }

    .value {
      margin-right: 16px;
      font-size: 40px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .unit {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }

    .sub-value {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
  }

  .description {
    color: var(--el-text-color-placeholder);
    font-size: 12px;
  }
}

.risk-content {
  margin-top: 60px;
  display: flex;
  align-items: baseline;
  gap: 8px;
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);

  .risk-num {
    font-size: 48px;
    color: var(--el-color-error);
  }
}
</style>
