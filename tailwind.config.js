/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: "class",
  corePlugins: {
    preflight: false
  },
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        bg_color: "var(--el-bg-color)",
        neutral: "var(--color-neutral)",
        amber: "var(--color-amber)",
        primary: "var(--el-color-primary)",
        primary_light_9: "var(--el-color-primary-light-9)",
        primary_light_5: "var(--el-color-primary-light-5)",
        text_color_primary: "var(--el-text-color-primary)",
        text_color_regular: "var(--el-text-color-regular)",
        text_color_secondary: "var(--el-text-color-secondary)",
        text_color_disabled: "var(--el-text-color-disabled)"
      },
      screens: {
        sm: "1280px",
        md: "1440px",
        lg: "1600px",
        xl: "1920px"
      },
      fontSize: {
        sm: ["12px", "20px"],
        base: ["14px", "22px"],
        middle: ["16px", "24px"],
        lg: ["18px", "26px"],
        xl: ["20px", "28px"],
        xxl: ["24px", "32px"],
        "4xl": "2rem",
        "7xl": ["4.75rem", "4.75rem"]
      },
      textColor: {
        primary: "var(--el-color-primary)",
        primaryText: "var(--el-text-color-primary)",
        regular: "var(--el-text-color-regular)",
        secondary: "var(--el-text-color-secondary)",
        wechat: "#28C445",
        danger: "var(--el-color-danger)",
        disabled: "var(--el-text-color-disabled)"
      }
    }
  }
};
