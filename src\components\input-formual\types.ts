/**
 * 公式输入组件
 */
export interface InputFormualModelVal {
  leftValue: string;
  leftSymbol: number;
  midValue: string;
  rightSymbol: number;
  rightValue: string;
}

/**
 * 公式比较符号枚举
 */
export enum ComparisonSymbolEnum {
  /** 小于 */
  LessThanValue = 0,
  /** 小于等于 */
  LessThanOrEqualValue = 1
}

/**
 * 公式比较符号映射
 */
export const ComparisonSymbolMap = {
  [ComparisonSymbolEnum.LessThanValue]: "<",
  [ComparisonSymbolEnum.LessThanOrEqualValue]: "≤"
};
