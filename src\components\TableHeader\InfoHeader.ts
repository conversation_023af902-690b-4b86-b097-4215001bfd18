import { Fragment, h } from "vue";
import { ElIcon, ElTooltip } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";

export function infoHeader(content: string) {
  return data => {
    const tooltip = h(ElTooltip, { content }, () =>
      h(ElIcon, { class: "cursor-pointer ml-2 pt-[2px] !inline-block" }, () => h(InfoFilled))
    );
    return h(Fragment, [h("span", data.column.label), tooltip]);
  };
}
