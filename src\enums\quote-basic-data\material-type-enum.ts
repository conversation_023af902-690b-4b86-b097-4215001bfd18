export enum MaterialTypeEnum {
  /**
   * 主材
   */
  MAIN = 0,

  /**
   * 辅材
   */
  AUXILIARY = 1
}

/**
 * 材料类型枚举描述映射
 */
export const MaterialTypeEnumDesc: Record<MaterialTypeEnum, string> = {
  [MaterialTypeEnum.MAIN]: "主材",
  [MaterialTypeEnum.AUXILIARY]: "辅材"
};

/**
 * 材料类型枚举颜色映射
 */
export const MaterialTypeEnumColor: Record<MaterialTypeEnum, string> = {
  [MaterialTypeEnum.MAIN]: "primary", // 主色
  [MaterialTypeEnum.AUXILIARY]: "info" // 次要色
};
