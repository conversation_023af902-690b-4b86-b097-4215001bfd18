import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IListResponse,
  IResponse,
  IAccountReview,
  IAccountReviewForm,
  IAccountReviewReq,
  IAccountRegister,
  IAnalysisWeChatOpenId,
  IWeChatInfo
} from "@/models";

/** 查询账号审核分页  */
export const queryAccountReview = (data: IAccountReviewReq) => {
  const url: string = withApiGateway("admin-api/system/registerReview/page");
  return http.post<IAccountReviewReq, IListResponse<IAccountReview>>(url, {
    data
  });
};

/** 根据账号审核id 查询详情 */
export const getAccountReviewById = (id: string) => {
  const url: string = withApiGateway(`admin-api/system/registerReview/get?id=${id}`);
  return http.get<string, IResponse<IAccountReview>>(url);
};

/** 编辑账号审核 */
export const updateBatchAccountReview = (data: IAccountReviewForm) => {
  return http.put<IAccountReviewForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/registerReview/updateStatus"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除账号审核根据Id */
export const deleteAccountReviewById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/system/registerReview/delete?id=${id}`));
};

export const createRegister = (data: IAccountRegister): Promise<IResponse<boolean>> => {
  return http.post(withApiGateway("admin-api/system/wechat/createRegisterReview"), { data });
};

export const getWeChatOpenId = (params: IAnalysisWeChatOpenId): Promise<IResponse<IWeChatInfo>> => {
  return http.get(withApiGateway("admin-api/system/wechat/mb/tokens"), { params });
};
