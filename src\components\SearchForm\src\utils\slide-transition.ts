import type { TransitionProps } from "vue";
import { defineComponent, h, Transition } from "vue";

const props: TransitionProps = {
  onBeforeEnter(el: HTMLElement) {
    el.style.height = "0";
  },
  onEnter(el: HTMLElement) {
    el.style.height = `${el.scrollHeight}px`;
  },
  onAfterEnter(el: HTMLElement) {
    el.style.height = "";
  },
  onBeforeLeave(el: HTMLElement) {
    el.style.height = `${el.scrollHeight}px`;
  },
  onLeave(el: HTMLElement) {
    el.style.height = "0";
  }
};

export default defineComponent({
  name: "SlideTransition",
  props: {
    collapse: {
      type: Boolean
    }
  },
  render() {
    const children = this.collapse
      ? null
      : {
          default: () =>
            h(
              "div",
              { class: "transition-[height] ease-in-out overflow-hidden will-change-[height]" },
              { default: () => [this.$slots.default()] }
            )
        };
    return h(Transition, props, children);
  }
});
