import { ImportProductVersionStatusEnum } from "@/enums";
import { IBase } from "@/models";

export interface IProductVersion extends IBase {
  /** 版本名称 */
  versionName?: string;

  /** 版本号 */
  versionNumber?: string;

  /** 导入附件ID  */
  importAttachmentId?: string;

  /** 附件名称 */
  importAttachmentName?: string;

  /** 附件地址 */
  importAttachmentUrl?: string;

  /** 状态 */
  status?: ImportProductVersionStatusEnum;
}
