/** 金额格式化 */
export const formatNumber = (value: number, type = "money"): string => {
  const amountNum = Number(value);

  if (isNaN(amountNum)) {
    return value.toString();
  }

  if (type !== "money") {
    return amountNum.toFixed(2);
  }

  if (Math.abs(amountNum) >= 100000000) {
    return (amountNum / 100000000).toFixed(2) + " 亿";
  }
  if (Math.abs(amountNum) >= 10000000) {
    return (amountNum / 10000000).toFixed(2) + " 千万";
  }
  // if (Math.abs(amountNum) > 1000000) {
  //   return (amountNum / 1000000).toFixed(2) + '百万';
  // }
  if (Math.abs(amountNum) >= 10000) {
    return (amountNum / 10000).toFixed(2) + " 万";
  }

  return amountNum.toFixed(2) + " 元";
};
export const formatThousands = (value: string | number) => {
  if (typeof value === "string" || typeof value === "number") {
    return `${value}`?.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return null;
};

export const formatLargeNumber = (numStr: string, keepDecimalZero = false) => {
  // 1. 检查输入是否为有效数字字符串
  if (typeof numStr !== "string" || !/^-?\d*\.?\d+$/.test(numStr)) {
    return numStr; // 非数字字符串原样返回
  }

  const num = parseFloat(numStr);
  if (isNaN(num)) return numStr; // 无效数字处理

  // 2. 按亿/万单位格式化
  if (Math.abs(num) >= 100000000) {
    const value = num / 100000000;
    return formatWithUnit(value, "亿", keepDecimalZero);
  }
  if (Math.abs(num) >= 10000) {
    const value = num / 10000;
    return formatWithUnit(value, "万", keepDecimalZero);
  }
  return numStr;
};
function formatWithUnit(value: number, unit: string, keepDecimalZero: boolean) {
  let formattedValue = value.toFixed(2);
  if (!keepDecimalZero) {
    formattedValue = formattedValue.replace(/\.?0+$/, ""); // 去除末尾的0
  }
  return formattedValue + unit;
}
