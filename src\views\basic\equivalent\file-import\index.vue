<template>
  <div class="inline-block">
    <el-dialog
      v-model="modelValue"
      title="导入"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="40%"
    >
      <el-upload
        class="w-full"
        ref="uploadRef"
        drag
        :auto-upload="false"
        accept=".xlsx,.xls,.csv"
        :limit="1"
        :on-exceed="onExceed"
        :on-change="onChangeUploadFile"
      >
        <el-icon class="upload-icon" :size="30"><upload-filled /></el-icon>
        <div>拖动上传 or <span class="text-primary">点击上传</span></div>
        <template #tip>
          <div class="el-upload__tip">.xlsx,.xls,.csv</div>
        </template>
      </el-upload>
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleImportBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { uploadFile } from "@/api/upload-file";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage, genFileId, UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { computed, ref } from "vue";
import { importProductEquivalent } from "@/api/basic/equivalent";

const emits = defineEmits<{
  (e: "update:modelValue", val?: boolean): void;
  (e: "onImportSuccess"): void;
}>();

let uploadRawFile: UploadRawFile = null;
const uploadRef = ref<UploadInstance>();
const loading = ref(false);

const handleImportBtn = useLoadingFn(onImport, loading);

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    versionId?: string;
  }>(),
  {}
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRawFile = file;
  uploadRef.value!.handleStart(file);
};

const onChangeUploadFile: UploadProps["onChange"] = uploadFile => {
  uploadRawFile = uploadFile.raw;
};

async function onImport(): Promise<void> {
  if (!uploadRawFile) {
    ElMessage.warning("请选择文件");
    return;
  }

  const formData = new FormData();
  formData.append("file", uploadRawFile);
  const { data } = await uploadFile(formData);
  await importProductEquivalent({ importFileId: data.id });
  ElMessage.success("导入成功");
  emits("onImportSuccess");
  closeDialog();
}

function closeDialog() {
  modelValue.value = false;
}
</script>

<style scoped lang="scss">
.upload-icon {
  color: var(--el-color-primary);
}
</style>
