import { isNumber } from "@vueuse/core";

const DECIMAL_SEP = ".";
const MINUS_SIGN = "-";

/**
 * Formats a number as text, with minimumIntegerDigits, minimumFractionDigits, maximumFractionDigits
 *
 * @param value The number to format
 * @param digitsInfo Decimal representation options, specified by a string in the following format:
 * `{minimumIntegerDigits}.{minimumFractionDigits}-{maximumFractionDigits}`
 * @param locales The locale(s) to use
 * @example
 *
 * formatDecimal(12345.12345, "1.1-2")
 * // => 12,345.12
 *
 * formatDecimal(12345.12543, "0.-2")
 * // => 12,345.13
 *
 * formatDecimal(12345.12345, "6.1-2")
 * // => 012,345.12
 */
export function formatDecimal(value: number, digitsInfo?: string, locales: Intl.LocalesArgument = "en-US") {
  if (!isNumber(value)) {
    return value;
  }
  const options: Intl.NumberFormatOptions = {
    style: "decimal",
    ...parseNumberFormat(digitsInfo)
  };
  return value.toLocaleString(locales, options);
}

function parseNumberFormat(format: string) {
  const p = {
    minimumIntegerDigits: undefined,
    minimumFractionDigits: undefined,
    maximumFractionDigits: 20
  };
  if (format) {
    const parts = format.split(DECIMAL_SEP);
    p.minimumIntegerDigits = Math.max(toNumber(parts[0], 1), 1);
    if (parts[1]) {
      const fractionParts = parts[1].split(MINUS_SIGN);
      p.minimumFractionDigits = toNumber(fractionParts[0]);
      p.maximumFractionDigits = toNumber(fractionParts[1], 20);
    }
  }
  return p;
}

function toNumber(value: string, defaultValue: number = undefined) {
  if (!value) {
    return defaultValue;
  }
  return Number(value);
}
