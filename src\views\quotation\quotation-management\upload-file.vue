<template>
  <div class="max-w-[35%]">
    <el-popover placement="bottom-end" width="370" v-if="props.files?.length > 1">
      <template #reference>
        <div class="flex px-5 items-center">
          <img class="w-8 h-8" :src="ExcelIcon" />
          <div class="file flex items-center flex-1 break-words overflow-hidden">
            <div class="file_name cursor-pointer flex-1 break-words overflow-hidden">
              {{ props.files?.[0].inquiryAttachmentName }}
            </div>
            <el-icon class="more mt-1"><More /></el-icon>
          </div>
        </div>
      </template>
      <div class="w-full">
        <div class="file_name" v-for="(file, index) in props.files || []" :key="index">
          {{ file.inquiryAttachmentName }}
        </div>
      </div>
    </el-popover>
    <div v-else class="flex px-5 items-center cursor-pointer">
      <FileViewAndDownload
        :file-name="props.files?.[0]?.inquiryAttachmentName"
        :file-id="props.files?.[0]?.inquiryAttachmentId"
        :file-url="props.files?.[0]?.inquiryAttachmentUrl"
      >
        <div class="flex items-center">
          <img class="w-8 h-8" :src="ExcelIcon" />
          <div class="file_name flex-1 break-words overflow-hidden">{{ props.files?.[0]?.inquiryAttachmentName }}</div>
        </div>
      </FileViewAndDownload>
    </div>
  </div>
</template>

<script setup lang="ts">
import ExcelIcon from "@/assets/img/excel-icon.png";
import { InquiryAttachment } from "@/models/quotation";
import { More } from "@element-plus/icons-vue";
import FileViewAndDownload from "@/views/components/file-view-download/index.vue";

const props = withDefaults(
  defineProps<{
    fileNames?: Array<string>;
    files?: Array<InquiryAttachment>;
  }>(),
  {}
);
</script>

<style scoped lang="scss">
.file {
  margin: 0 22px 0 13px;

  .more {
    transform: rotate(90deg);
  }
}

.file_name {
  font-size: 18px;
  font-weight: 500;
  color: #303339;
}
</style>
