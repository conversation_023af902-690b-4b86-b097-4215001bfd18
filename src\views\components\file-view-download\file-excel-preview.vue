<template>
  <el-dialog
    v-model="modelValue"
    :title="props.name || '预览'"
    :append-to-body="true"
    align-center
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="60%"
  >
    <div class="h-[70vh]" v-if="props.src" v-loading="loading">
      <VueOfficeExcel :src="props.src" :options="options" @rendered="onRendered()" />
    </div>
    <template #footer>
      <span>
        <el-button @click="closeDialog">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, defineAsyncComponent } from "vue";
import "@vue-office/excel/lib/index.css";

const VueOfficeExcel = defineAsyncComponent(() => import("@vue-office/excel"));

const emits = defineEmits<{
  (e: "update:modelValue", value: boolean);
}>();
const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    src?: string;
    name?: string;
  }>(),
  {}
);

const options = ref<any>({ xls: true });
const loading = ref(true);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});
const onRendered = () => {
  loading.value = false;
};

const closeDialog = () => {
  modelValue.value = false;
};
</script>
