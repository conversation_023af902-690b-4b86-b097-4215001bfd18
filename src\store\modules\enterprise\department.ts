import { IDepartment, IDepartmentForm, IDepartmentReq, IDepartmentTree } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/enterprise";

export const useDepartmentStore = defineStore({
  id: "cx-enterprise-department",
  state: () => ({
    departments: [] as Array<IDepartment>,
    departmentTree: [] as Array<IDepartmentTree>,
    total: 0 as number,
    loading: false as boolean,
    departmentForm: {} as IDepartmentForm
  }),
  actions: {
    async queryDepartment(params?: IDepartmentReq) {
      this.loading = true;
      const res = await api.queryDepartment(params);
      this.departments = res.data.list;
      this.total = res.data.total;
      this.loading = false;
    },

    async queryDepartmentTree() {
      const res = await api.queryDepartmentTree();
      this.departmentTree = res.data;
    },

    async addDepartment(data: IDepartmentForm) {
      return api.addDepartment(data);
    },

    async editDepartment(data: IDepartmentForm) {
      return api.editDepartment(data);
    },

    async deleteDepartment(id: string) {
      return api.deleteDepartment(id);
    },

    async getDepartmentDetailById(id: string) {
      const res = await api.getDepartmentDetailById(id);
      this.department = res.data;
    },

    async deleteDepartmentEmployee(deptId: string, userId: string) {
      return api.deleteDepartmentEmployee(deptId, userId);
    },

    async bindEmployeeToDepartment(deptId: string, userIds: Array<string>) {
      return api.bindEmployeeToDepartment(deptId, userIds);
    },

    setDepartmentForm(department?: Partial<IDepartmentForm>) {
      this.departmentForm = department;
    }
  }
});
