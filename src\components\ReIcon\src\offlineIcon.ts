import { addIcon } from "@iconify/vue/dist/offline";

/**
 * 这里存放本地图标，在 src/layout/index.vue 文件中加载，避免在首启动加载
 */

// 本地菜单图标，后端在路由的icon中返回对应的图标字符串并且前端在此处使用addIcon添加即可渲染菜单图标
import PlatformFill from "@iconify-icons/ri/mac-fill";
import ListFill from "@iconify-icons/ep/list";
import GuideFill from "@iconify-icons/ri/guide-fill";
import ShieldFill from "@iconify-icons/ri/shield-check-fill";
import SettingFill from "@iconify-icons/ri/settings-4-fill";
import FlaskFill from "@iconify-icons/ri/flask-fill";
import BuildingFill from "@iconify-icons/ri/building-2-fill";
import AdminFill from "@iconify-icons/ri/admin-fill";
import InformationFill from "@iconify-icons/ri/information-fill";
import CalendarFill from "@iconify-icons/ri/calendar-fill";
import AlarmFill from "@iconify-icons/ri/alarm-fill";
import LineChartFill from "@iconify-icons/ri/line-chart-fill";
addIcon("platform", PlatformFill);
addIcon("list", ListFill);
addIcon("shield", ShieldFill);
addIcon("setting", SettingFill);
addIcon("flask", FlaskFill);
addIcon("building", BuildingFill);
addIcon("admin", AdminFill);
addIcon("calendar", CalendarFill);
addIcon("informationFill", InformationFill);
addIcon("alarm", AlarmFill);
addIcon("guide", GuideFill);
addIcon("lineChart", LineChartFill);
