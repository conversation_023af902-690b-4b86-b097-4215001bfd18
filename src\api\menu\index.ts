import { IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IMenu, IRoleMenu } from "@/models/menu";

export const queryMenuTree = () => {
  return http.get<void, IResponse<Array<IMenu>>>(withApiGateway("admin-api/system/menu/getMenus"));
};

export const editRoleMenu = (data: IRoleMenu) => {
  return http.post<IRoleMenu, IResponse<boolean>>(withApiGateway("admin-api/system/permission/saveRoleMenu"), { data });
};

export const getRoleMenuByRoleId = (roleId: string) => {
  return http.get<string, IResponse<Array<IMenu>>>(withApiGateway(`admin-api/system/permission/getMenuIds/${roleId}`));
};
