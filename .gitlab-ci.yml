variables:
  MODULE_NAME: cxist.ai.cablequotation.platform.frontend
  IMAGE_NAME: cable-quotation-web
  DEPLOY_CARGO_NAME: cx-ai-quotation-fe-$CI_PIPELINE_ID

  IMAGE_PREFIX: ai
  DEPLOY_DIR_BASE: byzan-deploy/$IMAGE_NAME

.before_script: &build_script |-
  echo "==> Setting up Kubernetes context (using YAML anchor)..."
  if [ -z "$KUBE_CONFIG" ]; then
    echo "Error: KUBE_CONFIG CI/CD variable is not set or empty."
    exit 1
  fi
  KUBE_CONFIG_DIR="${CI_PROJECT_DIR}/.kube"
  mkdir -p "$KUBE_CONFIG_DIR"
  KUBE_CONFIG_FILE="${KUBE_CONFIG_DIR}/config"
  echo "$KUBE_CONFIG" | base64 --decode > "$KUBE_CONFIG_FILE"
  chmod 600 "$KUBE_CONFIG_FILE"
  export KUBECONFIG="$KUBE_CONFIG_FILE"
  echo "Verifying kubectl contexts..."
  kubectl config get-contexts
  kubectl config use-context kubernetes-admin@kubernetes
  echo "==> Kubernetes context setup complete."

stages:
  - lint
  - build_app
  - build_image
  - deploy

lint_build:
  stage: lint
  interruptible: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      when: never
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: never
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
  tags:
    - node-18
  script:
    - node --version
    - pnpm --version
    - pnpm install
    - pnpm lint
    - pnpm typecheck
    - pnpm build

build_app:
  stage: build_app
  interruptible: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "alvin/ci"'
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: $CI_COMMIT_TAG
  tags:
    - node-18
  script:
    - node --version
    - pnpm --version
    - pnpm install
    - pnpm build
    - mkdir $DEPLOY_CARGO_NAME
    - cp -r ./dist $DEPLOY_CARGO_NAME
    - cp -r ./tools/* $DEPLOY_CARGO_NAME
    - tar -czf $DEPLOY_CARGO_NAME.tar.gz -C $DEPLOY_CARGO_NAME .
  artifacts:
    name: $DEPLOY_CARGO_NAME
    paths:
      - ./$DEPLOY_CARGO_NAME.tar.gz

build_image:
  after_script:
    - rm -rf ./dist
    - rm -rf ./$DEPLOY_CARGO_NAME
    - rm -rf ./$DEPLOY_CARGO_NAME.tar.gz
  stage: build_image
  interruptible: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: $CI_COMMIT_TAG
  tags:
    - docker-runner-dev
  variables:
    BASE_IMAGE: byzan/nginx:byz-nightly
    IMAGE_TAG: nightly
    PUSH_LATEST: "false"
  script:
    - if ! [ -e "./$DEPLOY_CARGO_NAME.tar.gz" ]; then exit 1; fi
    - if [ $CI_COMMIT_REF_NAME == master ]; then BASE_IMAGE=byzan/nginx:byz-prod; IMAGE_TAG=prod; PUSH_LATEST=true; fi
    - mkdir -p ./dist && rm -rf ./dist/*
    - tar -xzf ./$DEPLOY_CARGO_NAME.tar.gz -C ./dist
    - IMAGE_FULL_NAME=$IMAGE_PREFIX/$IMAGE_NAME
    - docker image build --tag $IMAGE_PREFIX/$IMAGE_NAME:$IMAGE_TAG --label commit_id=$CI_COMMIT_SHA --label pipeline_id=$CI_PIPELINE_ID ./dist
    - bash ./dist/push-image.sh --name $IMAGE_FULL_NAME --tag $IMAGE_TAG --latest $PUSH_LATEST --version $CI_PIPELINE_ID --registry "$REGISTRY_HOST_DEV"
  dependencies:
    - build_app

deploy_dev:
  stage: deploy
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
  needs:
    - build_image
  image:
    name: hub-dev.cxist.com/ai/kubectl:1.18.6
    entrypoint: [""]
  tags:
    - docker-executors
  before_script:
    - *build_script
  script:
    - echo "Deploying application to namespace ai-quotation-dev"
    - IMAGE_FULL_NAME=hub-dev.cxist.com/ai/cable-quotation-web:nightly-$CI_PIPELINE_ID
    - kubectl set image deployment/ai-quotation-web-deployment ai-quotation-web=$IMAGE_FULL_NAME --namespace ai-quotation-dev
    - echo "Waiting for deployment rollout to finish..."
    - kubectl rollout status deployment/ai-quotation-web-deployment --namespace ai-quotation-dev --timeout=120s
    - echo "Deployment successful. Getting service details:"
    - kubectl get service ai-quotation-web-svc --namespace ai-quotation-dev -o wide

deploy-master:
  stage: deploy
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  needs:
    - build_image
  image:
    name: hub-dev.cxist.com/ai/kubectl:1.18.6
    entrypoint: [""]
  tags:
    - docker-executors
  before_script:
    - *build_script
  script:
    - echo "Deploying application to namespace ai-quotation-test"
    - IMAGE_FULL_NAME=hub-dev.cxist.com/ai/cable-quotation-web:prod-$CI_PIPELINE_ID
    - kubectl set image deployment/ai-quotation-web-deployment ai-quotation-web=$IMAGE_FULL_NAME --namespace ai-quotation-test
    - echo "Waiting for deployment rollout to finish..."
    - kubectl rollout status deployment/ai-quotation-web-deployment --namespace ai-quotation-test --timeout=120s
    - echo "Deployment successful. Getting service details:"
    - kubectl get service ai-quotation-web-svc --namespace ai-quotation-test -o wide
