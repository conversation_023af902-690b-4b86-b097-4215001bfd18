/**
 * 上传文件之前校验文件
 * @params { raw: File, fileType: string[] maxSize?: number }
 * @return Function
 * <AUTHOR>
 * @Date 2023/12/9
 */

import { UploadFile } from "element-plus";
import { validUploadFileType } from "./uploadFiles";
import { UploadFileValidTypeEnum } from "@/enums/use-upload-validate";

export function useUploadFileValidate() {
  /** 校验文件格式 / 大小 */
  function validate(fileRaw: UploadFile, fileType: string[], maxSize = 20): { type: string; valid: boolean } {
    const validFileType = validUploadFileType(fileRaw?.raw.type, fileType);
    if (validFileType) {
      return {
        type: UploadFileValidTypeEnum.FILE_TYPE,
        valid: false
      };
    }

    // 校验上传文件的大小
    const limitSize = maxSize * 1024 * 1024;
    const fileSize = fileRaw.size;
    if (fileSize >= limitSize) {
      return {
        type: UploadFileValidTypeEnum.FILE_SIZE,
        valid: false
      };
    }

    return {
      type: UploadFileValidTypeEnum.PASS,
      valid: true
    };
  }
  return {
    validate
  };
}
