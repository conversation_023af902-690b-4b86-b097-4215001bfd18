import { IBase } from "@/models";

export interface IRiskRecord extends IBase {
  /**
   * 告警时间
   */
  alarmTime?: string;
  /**
   * 销售人员
   */
  salesPersonnel?: string;
  /**
   * 询价单位
   */
  inquiryOrganization?: string;
  /**
   * 相似度
   */
  similarity?: number;
  /**
   * 相似询价单数量
   */
  similarityQuantity?: string;
  /**
   * 关联销售
   */
  relatedSales?: string;
  /**
   * 处理时间
   */
  processingTime?: string;
  /**
   * 备注
   */
  processingRemark?: string;

  handler?: string;
}
