<template>
  <div class="flex flex-col justify-center items-center">
    <div class="flex items-center gap-3">
      <img :src="WeChatIcon" class="w-6 h-6" />
      <span>微信扫码验证</span>
    </div>
    <div class="mt-2">
      <span
        >请联系员工<templat v-if="props.employeeName" e>【{{ props.employeeName }}】</templat>扫码绑定</span
      >
    </div>
    <div class="qrcode mt-2" :style="{ background: `url(${weChatQRCode?.url}) no-repeat center center` }" />
  </div>
</template>

<script setup lang="ts">
import { getEmployeeDetail } from "@/api/enterprise/employee";
import { getWeChatBindQRCodeByAccountId } from "@/api/user-wechat";
import WeChatIcon from "@/assets/img/wechat.png";
import { IWeChatQRCode } from "@/models/user";
import { useIntervalFn } from "@vueuse/core";
import { ElMessage } from "element-plus";
import { ref, watch } from "vue";

const props = withDefaults(
  defineProps<{
    employeeId: string;
    wechatUnionId?: string;
    employeeName?: string;
  }>(),
  {}
);
const weChatQRCode = ref<IWeChatQRCode>();
const { pause: stopCheckWeChatBind, resume: startCheckWeChatBind } = useIntervalFn(handleCheckWeChatLogin, 1000, {
  immediate: false
});

watch(
  () => props.employeeId,
  async employeeId => {
    if (employeeId) {
      const { data } = await getWeChatBindQRCodeByAccountId(employeeId);
      weChatQRCode.value = data;
      startCheckWeChatBind();
    } else {
      weChatQRCode.value = {};
    }
  },
  {
    immediate: true
  }
);

async function handleCheckWeChatLogin(): Promise<void> {
  try {
    const { data } = await getEmployeeDetail(props.employeeId);

    if (props.wechatUnionId) {
      if (data && !data.wechatUnionId) {
        ElMessage.success("微信解绑成功");
        stopCheckWeChatBind();
      }
    } else {
      if (data && data.wechatUnionId) {
        ElMessage.success("微信绑定成功");
        stopCheckWeChatBind();
      }
    }
  } catch (error) {
    stopCheckWeChatBind();
    return;
  }
}
</script>

<style scoped lang="scss">
.qrcode {
  width: 180px;
  height: 180px;
  border-radius: 4px;
  background-size: contain !important;
}
</style>
