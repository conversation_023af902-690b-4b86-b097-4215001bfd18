<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color px-6 py-5 flex justify-between items-center">
      <div>{{ state.rawMaterialInformation?.type }}</div>
      <div class="flex gap-4">
        <el-button :icon="Back" @click="onBack()">返回</el-button>

        <AddEditRawMaterialInformationDialog
          mode="edit"
          :id="state.rawMaterialInformation?.id"
          @post-save-success="handleQuery()"
        >
          <template #trigger="{ openDialog }">
            <el-button :icon="Edit" type="primary" @click="openDialog">编辑</el-button>
          </template>
        </AddEditRawMaterialInformationDialog>
      </div>
    </div>
    <div class="flex flex-col p-5 overflow-hidden flex-1">
      <div class="p-5 bg-bg_color">
        <el-descriptions title="基础信息">
          <el-descriptions-item label="类型">{{ state.rawMaterialInformation?.type }}</el-descriptions-item>
          <el-descriptions-item label="型号">{{ state.rawMaterialInformation?.rawModel }}</el-descriptions-item>
          <el-descriptions-item label="规格">{{ state.rawMaterialInformation?.rawSpec }}</el-descriptions-item>
          <el-descriptions-item label="计算单位">{{
            state.rawMaterialInformation?.latestCost?.productUnit
          }}</el-descriptions-item>
          <el-descriptions-item label="单价成本"
            >￥{{ state.rawMaterialInformation?.latestCost?.price }}</el-descriptions-item
          >
          <el-descriptions-item label="更新人">{{ state.rawMaterialInformation?.updater }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ state.rawMaterialInformation?.updateTime }}</el-descriptions-item>
          <el-descriptions-item label="版本">{{
            state.rawMaterialInformation?.latestCost?.versionNum
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="bg-bg_color p-5 my-5 flex flex-col flex-1 overflow-hidden relative">
        <PureTable
          class="flex-1 overflow-hidden pagination"
          row-key="id"
          :data="state.list"
          :columns="columns"
          size="large"
          :loading="loading"
          showOverflowTooltip
        >
          <template #price="{ row }"> {{ row.price }}/{{ row.productUnit }} </template>
          <template #empty>
            <CxEmptyData />
          </template>
        </PureTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CxEmptyData from "@/components/CxEmpty";
import { Edit, Back } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { onMounted, reactive, ref } from "vue";
import { IRawMaterialInformation, IRawMaterialInformationCost } from "@/models/quote-basic-data";
import { useRoute, useRouter } from "vue-router";
import { getRawMaterialInformationById, getRawMaterialInformationCostListById } from "@/api/quote-basic-data";
import AddEditRawMaterialInformationDialog from "../add-edit-raw-material-information/dialog.vue";

const { columns } = useColumns();
const loading = ref(false);
const router = useRouter();
const route = useRoute();

let id: string;
const state = reactive<{
  list: Array<IRawMaterialInformationCost>;
  rawMaterialInformation: IRawMaterialInformation;
}>({
  list: [],
  rawMaterialInformation: {}
});

onMounted(() => {
  id = route.params.id as string;
  handleQuery();
});

const handleQuery = () => {
  handleGetRawMaterialInformationById();
  handlegetRawMaterialInformationCostListById();
};

const handleGetRawMaterialInformationById = async () => {
  const { data } = await getRawMaterialInformationById(id);
  state.rawMaterialInformation = data;
};

const handlegetRawMaterialInformationCostListById = async () => {
  const { data } = await getRawMaterialInformationCostListById(id);
  state.list = data;
};

const onBack = () => {
  router.back();
};
</script>

<style scoped lang="scss"></style>
