<template>
  <div class="bg-bg_color pt-[8px] px-6 flex justify-between">
    <ElForm ref="searchForm" :inline="true" :model="state.params" size="large" class="ml-6 flex-1">
      <ElFormItem label="综合搜索：" prop="userInformationKeyword" class="w-1/4">
        <ElInput
          class="w-full"
          clearable
          v-model="state.params.userInformationKeyword"
          placeholder="请输入用户名/姓名"
          @clear="onConfirmQuery"
        />
      </ElFormItem>

      <ElFormItem label="登录时间：" prop="loginTime" class="w-1/4">
        <el-date-picker
          v-model="state.params.loginTime"
          type="daterange"
          :defaultTime="defaultTime"
          range-separator="～"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </ElFormItem>

      <ElFormItem label="登录地址：" prop="ipAddress" class="w-[20%]">
        <ElInput clearable v-model="state.params.ipAddress" placeholder="请输入IP地址/城市" @clear="onConfirmQuery" />
      </ElFormItem>

      <ElFormItem>
        <ElButton type="primary" @click="onConfirmQuery()">搜索</ElButton>
        <ElButton @click="onResetQuery()">重置</ElButton>
      </ElFormItem>
    </ElForm>
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <PureTable
      class="flex-1 overflow-hidden pagination tooltip-max-w"
      row-key="id"
      size="large"
      :data="state.loginLogs"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="loading"
      @page-size-change="pageSizeChange()"
      @page-current-change="onPageCurrentChange"
      @sort-change="sortChange"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>

      <template #loginMethod="{ row }">
        <CXTag type="info">{{ LoginMethodEnumMapDesc[row.loginMethod] }} </CXTag>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { useColumns } from "./columns";
import { onMounted, reactive, ref } from "vue";
import { ILoginLogging, ILoginLogReq } from "@/models";
import { ElForm, ElFormItem, ElInput, ElButton } from "element-plus";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { FormInstance } from "element-plus";
import { queryLoginLog } from "@/api/logging/login";
import { handleOrderType } from "@/utils/sortByOrderType";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { LoginMethodEnumMapDesc } from "@/enums";
import CXTag from "@/components/CxTag/index.vue";

const searchForm = ref<FormInstance>();
const { columns } = useColumns();
const { pagination } = useTableConfig();
pagination.pageSize = 20;

const defaultTime = ref<[Date, Date]>([new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]);
const loading = ref(false);
const state = reactive<{
  params: ILoginLogReq;
  loginLogs: Array<ILoginLogging>;
}>({
  params: {
    userInformationKeyword: null,
    loginTime: null,
    ipAddress: null
  },
  loginLogs: []
});

onMounted(() => {
  requestList();
});

const onConfirmQuery = () => {
  pagination.currentPage = 1;
  requestList();
};

const onResetQuery = () => {
  pagination.currentPage = 1;
  searchForm.value?.resetFields();
  requestList();
};

const pageSizeChange = () => {
  pagination.currentPage = 1;
  requestList();
};

const onPageCurrentChange = () => {
  requestList();
};

/** 排序支持 */
const sortChange = ({ prop, order }) => {
  pagination.currentPage = 1;
  state.params.orderByField = prop;
  state.params.orderByType = handleOrderType(order);
  requestList();
};

const requestList = useLoadingFn(async () => {
  let params: ILoginLogReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (state.params && Object.keys(state.params).length) {
    params = { ...params, ...state.params };
  }

  const { data } = await queryLoginLog(params);
  state.loginLogs = data.list;
  pagination.total = data.total;
}, loading);
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
