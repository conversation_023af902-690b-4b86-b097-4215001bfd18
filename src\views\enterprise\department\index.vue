<template>
  <div class="flex h-full py-5 px-6 gap-5">
    <div class="w-[280px] bg-bg_color p-5 overflow-hidden">
      <div class="flex-bc add-department">
        <ElInput v-model="filterText" placeholder="输入部门名称" clearable>
          <template #prefix>
            <el-icon class="el-input__icon"><Search /></el-icon>
          </template>
        </ElInput>
        <el-button
          v-auth="PermissionKey.enterprise.enterpriseDepartmentCreate"
          @click="onAddDepartment()"
          class="add-button"
        >
          <el-icon><Plus /></el-icon>
        </el-button>
      </div>

      <el-scrollbar max-height="calc(100% - 40px)">
        <ElTree
          node-key="id"
          ref="treeRef"
          highlight-current
          :default-expanded-keys="state.treeExpandedKeys"
          :expand-on-click-node="false"
          default-expand-all
          :current-node-key="state.defaultCheckedCurrentNodeKey"
          :data="departmentStore.departmentTree"
          @current-change="onCurrentChange"
          @node-expand="onNodeExpand"
          @node-collapse="onNodeCollapse"
          :filter-node-method="filterNode"
        >
          <template #default="{ data }">
            <div
              :class="[
                'dept-custom-tree-node',
                { 'activate-operation': data.id === departmentState.activateDepartmentId }
              ]"
            >
              <div class="name">
                <span>{{ data.name }}</span>
                <span class="ml-1" v-if="data.userCount">（{{ data.userCount }}人）</span>
              </div>

              <el-popover
                placement="bottom"
                trigger="hover"
                @show="onDepartmentOperationShow(data)"
                @hide="onDepartmentOperationHide()"
              >
                <div class="flex flex-col">
                  <div class="flex items-center item-action mb-2" v-if="!data.status">
                    <el-icon><Plus /></el-icon>
                    <div
                      class="ml-2"
                      v-auth="PermissionKey.enterprise.enterpriseDepartmentCreate"
                      @click.stop="onAddSubDepartment(data)"
                    >
                      新增子部门
                    </div>
                  </div>
                  <div
                    v-auth="PermissionKey.enterprise.enterpriseDepartmentDelete"
                    class="flex items-center item-action"
                  >
                    <el-icon><Delete /></el-icon>
                    <div class="ml-2" @click.stop="onDeleteDepartment(data)">删除</div>
                  </div>
                </div>
                <template #reference>
                  <el-icon class="edit-icon"><MoreFilled /></el-icon>
                </template>
              </el-popover>
            </div>
          </template>
        </ElTree>
      </el-scrollbar>
    </div>
    <div class="bg-bg_color p-5 flex flex-col flex-1 overflow-hidden">
      <div>
        <div class="flex-bc mb-3">
          <div class="flex items-center">
            <IconifyIconOffline :icon="Chart" />
            <span class="ml-1 text-middle font-semibold">{{ departmentState.department?.name }}</span>
          </div>
          <div>
            <ElButton
              v-auth="PermissionKey.enterprise.enterpriseDepartmentDelete"
              :icon="Delete"
              @click="onDeleteDepartment(departmentState.department)"
              >删除</ElButton
            >
            <ElButton
              v-auth="PermissionKey.enterprise.enterpriseDepartmentEdit"
              :icon="Edit"
              @click="onEditDepartment(departmentState.department)"
              >编辑</ElButton
            >
            <ElButton
              v-auth="PermissionKey.enterprise.enterpriseDepartmentCreate"
              type="primary"
              :icon="Plus"
              @click.stop="onAddSubDepartment(departmentState.department)"
              :disabled="!!departmentState.department?.status"
              >新增子部门</ElButton
            >
          </div>
        </div>

        <ElDescriptions>
          <ElDescriptionsItem label="负责人">
            <CxTag type="custom" icon="icon-user-fill" v-if="departmentState.department?.leaderUser?.nickname">{{
              departmentState.department.leaderUser.nickname
            }}</CxTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="上级部门">
            {{ departmentState.department?.parentDepartment?.name }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="状态">
            <CxTag :type="departmentState.department?.status ? 'danger' : 'success'">
              {{ formatEnum(departmentState.department?.status, TenantStatusEnum, "StatusEnum") }}
            </CxTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem :span="3" label="部门简介">
            {{ departmentState.department?.introduction }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>
      <el-divider />
      <div class="flex-1 flex flex-col overflow-hidden">
        <div class="mb-4 flex justify-between items-center">
          <div class="flex items-center">
            <IconifyIconOffline :icon="Group" /> <span class="ml-1 text-middle font-semibold">部门员工</span>
          </div>
          <ElButton
            v-auth="PermissionKey.enterprise.enterpriseDepartmentUserCreate"
            type="primary"
            :icon="Plus"
            @click="onBindEmployeeToDepartment()"
            :disabled="!!departmentState.department?.status"
            >选择员工</ElButton
          >
        </div>

        <PureTable
          class="flex-1 overflow-hidden pagination"
          row-key="id"
          size="large"
          :data="employeeStore.employees"
          :columns="columns"
          showOverflowTooltip
          :pagination="pagination"
          :loading="employeeStore.loading"
          @page-size-change="onPageSizeChange()"
          @page-current-change="onPageCurrentPage()"
        >
          <template #operation="data">
            <ElButton
              link
              v-auth="PermissionKey.enterprise.enterpriseDepartmentUserRemove"
              type="danger"
              v-if="getCanRemoveDepartmentEmployee(data.row)"
              @click="onRemoveDepartmentEmployee(data.row)"
              >移除</ElButton
            >
          </template>
          <template #empty>
            <CxEmpty />
          </template>
        </PureTable>
      </div>
    </div>
  </div>
  <ElDialog
    title="选择员工"
    align-center
    class="middle"
    destroy-on-close
    v-model="employeeState.modalVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onCloseSelectEmployeeModal()"
  >
    <EmployeeSelect
      ref="employeeSelectRef"
      @onSelectEmployee="handleSelectEmployee($event)"
      :params="state.queryEmployeeParams"
    />
    <template #footer>
      <el-button @click="onCancelSelectEmployeeModal()">取消</el-button>
      <el-button
        type="primary"
        :loading="saveBindEmployeeLoading"
        :disabled="state.selectedEmployees?.length === 0"
        @click="handleBindEmployee()"
        >保存</el-button
      >
    </template>
  </ElDialog>

  <ElDialog
    :title="departmentModalTitle"
    align-center
    destroy-on-close
    class="small"
    v-model="departmentState.departmentModalVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onCloseAddDepartmentFormModal()"
  >
    <DepartmentForm ref="createDepartmentFormRef" />

    <template #footer>
      <el-button @click="onCancelAddDepartmentModal()">取消</el-button>
      <el-button type="primary" :loading="saveDepartmentLoading" @click="handleSaveDepartment()">保存</el-button>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
import { ElButton, ElDescriptions, ElDescriptionsItem, ElTree, ElInput, ElIcon, ElMessage } from "element-plus";
import { useTableConfig } from "@/utils/useTableConfig";
import { useColumns } from "./columns";
import CxEmpty from "@/components/CxEmpty";
import CxTag from "@/components/CxTag/index.vue";
import { computed, reactive, ref, watch, onMounted } from "vue";
import Chart from "@iconify-icons/ri/organization-chart";
import Group from "@iconify-icons/ri/group-line";
import EmployeeSelect from "@/views/components/employee-select/index.vue";
import { useDepartmentStore, useEmployeeStore } from "@/store/modules";
import DepartmentForm from "./department-form.vue";
import { IDepartment, IDepartmentForm, IEmployee, IEmployeeReq } from "@/models";
import { Edit, Plus, Delete, MoreFilled, Search } from "@element-plus/icons-vue";
import { TenantStatusEnum } from "@/enums";
import { formatEnum } from "@/utils/format";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useEmployeeHook, useDepartmentHook } from "./hooks";
import { useConfirm } from "@/utils/useConfirm";
import { PermissionKey } from "@/consts";

interface Tree {
  id: number;
  label: string;
  children?: Tree[];
}

usePageStoreHook().setTitle((useRoute().meta?.title as string) || "部门管理");

const {
  employeeState,
  deleteDepartmentEmployee,
  bindEmployeeToDepartment,
  onCloseSelectEmployeeModal,
  onBindEmployeeToDepartmentModalVis
} = useEmployeeHook();

const {
  departmentState,
  onDepartmentOperationShow,
  onDepartmentOperationHide,
  onConfirmSaveDepartment,
  onCancelAddDepartmentModal,
  onCloseAddDepartmentFormModal,
  onDeleteDepartment,
  onAddSubDepartment,
  onEditDepartment,
  onChooseDepartment,
  onAddDepartment
} = useDepartmentHook();

const employeeStore = useEmployeeStore();
const departmentStore = useDepartmentStore();
const { columns } = useColumns();
const { pagination } = useTableConfig();
let treePathIds: Array<string> = [];

const state = reactive<{
  selectedEmployees: Array<IEmployee>;
  queryEmployeeParams: IEmployeeReq;
  treeExpandedKeys: Array<string>;
  defaultCheckedCurrentNodeKey: string;
}>({
  selectedEmployees: [],
  queryEmployeeParams: {
    excludeDept: true
  },
  treeExpandedKeys: [],
  defaultCheckedCurrentNodeKey: undefined
});

departmentStore.queryDepartmentTree();
const filterText = ref("");

const employeeSelectRef = ref<InstanceType<typeof EmployeeSelect>>();
const saveBindEmployeeLoading = ref<boolean>(false);
const createDepartmentFormRef = ref<InstanceType<typeof DepartmentForm>>();
const saveDepartmentLoading = ref<boolean>(false);

const handleSaveDepartment = useLoadingFn(onSaveDepartment, saveDepartmentLoading);
const handleBindEmployee = useLoadingFn(onConfirmBindEmployee, saveBindEmployeeLoading);

const treeRef = ref<InstanceType<typeof ElTree>>();
watch(filterText, val => {
  treeRef.value!.filter(val);
});

watch(
  () => departmentState.department?.id,
  deptId => {
    if (departmentState.department?.id) {
      employeeStore.queryEmployee({ deptId, pageSize: pagination.pageSize });
    }
  }
);

watch(
  () => departmentStore.departmentTree,
  () => {
    if (departmentState.department?.id) {
      return;
    }

    if (!Array.isArray(departmentStore.departmentTree) || departmentStore.departmentTree.length === 0) {
      return;
    }
    onChooseDepartment(departmentStore.departmentTree[0]);
    state.defaultCheckedCurrentNodeKey = departmentStore.departmentTree[0].id;
  },
  { immediate: true }
);

watch(
  () => employeeStore.total,
  total => {
    pagination.total = total;
  }
);

onMounted(() => {
  employeeStore.$reset();
});

const departmentModalTitle = computed(() => (departmentStore.departmentForm?.id ? "编辑部门" : "新增部门"));

const getCanRemoveDepartmentEmployee = (employee: IEmployee) => {
  if (!departmentState.department || Object.keys(departmentState.department).length === 0) {
    return false;
  }
  return employee.deptIds?.includes(departmentState.department.id);
};

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};

const onCurrentChange = (department: IDepartment, node: { parent: { data: IDepartment } }) => {
  onChooseDepartment(department, node);
};

const onNodeExpand = (data: IDepartment) => {
  if (!state.treeExpandedKeys.includes(data.id)) {
    state.treeExpandedKeys.push(data.id);
    treePathIds.push(data.path);
  }
};
const onNodeCollapse = (data: IDepartment) => {
  if (treePathIds.includes(data.id)) {
    const collapseIds: Array<string> = treePathIds
      .filter(path => path.indexOf(data.id) >= 0)
      .join(",")
      .split(",");
    state.treeExpandedKeys = state.treeExpandedKeys.filter(x => !collapseIds.includes(x));
    treePathIds = treePathIds.filter(p => p !== data.path);
  }
};

const onCancelSelectEmployeeModal = () => {
  state.selectedEmployees = [];
  onCloseSelectEmployeeModal();
};

const onBindEmployeeToDepartment = () => {
  if (!departmentState.department || Object.keys(departmentState.department).length === 0) {
    ElMessage.warning("请选择部门");
    return;
  }

  state.queryEmployeeParams.deptId = departmentState.department.id;
  onBindEmployeeToDepartmentModalVis();
};

const onRemoveDepartmentEmployee = async (employee: IEmployee) => {
  if (!departmentState.department || Object.keys(departmentState.department).length === 0) {
    ElMessage.warning("请选择部门");
    return;
  }

  if (!employee.deptIds.includes(departmentState.department.id)) {
    ElMessage.warning("当前员工不直属于本部门，无法移除");
    return;
  }

  if (!(await useConfirm("确认移除后，数据将无法恢复", "确认移除"))) {
    return;
  }
  await deleteDepartmentEmployee(departmentState.department.id, employee.id);
  employeeStore.queryEmployee({ deptId: departmentState.department.id });
  departmentStore.queryDepartmentTree();
  treeRef.value.setCurrentKey(departmentState.department?.id);
};

const onPageSizeChange = () => {
  employeeStore.queryEmployee(filterParams());
};

const onPageCurrentPage = () => {
  employeeStore.queryEmployee(filterParams());
};

const filterParams = () => {
  return {
    deptId: departmentState.department.id,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };
};

const handleSelectEmployee = (employees: Array<IEmployee>) => {
  state.selectedEmployees = employees;
};

async function onConfirmBindEmployee() {
  const selectedEmployeeIds: Array<string> = state.selectedEmployees?.map(x => x.id);
  if (!Array.isArray(selectedEmployeeIds) || selectedEmployeeIds.length === 0) {
    return;
  }
  await bindEmployeeToDepartment(departmentState.department.id, selectedEmployeeIds);
  employeeStore.queryEmployee({ deptId: departmentState.department.id });
  departmentStore.queryDepartmentTree();
}

async function onSaveDepartment() {
  const department: IDepartmentForm | false = await createDepartmentFormRef.value.getValidValue().catch(() => false);
  if (!department) {
    return;
  }
  filterText.value = "";
  await onConfirmSaveDepartment(department);
  treeRef.value.setCurrentKey(departmentState.department?.id);
}
</script>

<style scoped lang="scss">
.add-department {
  padding-bottom: 12px;

  .add-button {
    margin-left: 4px;
    padding: 5px 8px;
  }
}

.dept-custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  width: 100%;
  line-height: 2;
  overflow: hidden;

  .edit-icon {
    visibility: hidden;
    position: absolute;
    z-index: 2;
    right: 8px;
  }

  &:hover {
    .edit-icon {
      visibility: visible;
      color: var(--el-color-primary);
    }
  }

  &.activate-operation {
    .edit-icon {
      visibility: visible;
      color: var(--el-color-primary);
    }
  }
}

.item-action {
  &:hover {
    cursor: pointer;
    color: var(--el-color-primary);
  }
}

.el-tree-node__content {
  padding: 8px 0;
}

:deep(.el-tree-node__content) {
  height: 28px;
}
</style>
