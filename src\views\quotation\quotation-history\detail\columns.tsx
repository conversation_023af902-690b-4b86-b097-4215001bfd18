import { ColumnWidth } from "@/enums";
import { Column } from "element-plus";
import { FixedDir } from "element-plus/es/components/table-v2/src/constants";

export function useColumns() {
  const columns: Array<Column> = [
    {
      title: "序号",
      key: "rowIndex",
      dataKey: "rowIndex",
      width: 60
    },
    {
      title: "原始位置",
      key: "excelRowIndex",
      dataKey: "excelRowIndex",
      width: 80
    },
    {
      title: "原始数据",
      key: "columnRawContent",
      dataKey: "columnRawContent",
      width: 250
    },
    {
      title: "型号",
      key: "modelName",
      dataKey: "modelName",
      width: ColumnWidth.Char12
    },
    {
      title: "规格",
      key: "specification",
      dataKey: "specification",
      width: ColumnWidth.Char10
    },
    {
      title: "单位",
      key: "unit",
      dataKey: "unit",
      width: ColumnWidth.Char4
    },
    {
      title: "数量",
      key: "quantity",
      dataKey: "quantity",
      width: ColumnWidth.Char4
    },
    {
      title: "短米折扣 (%)",
      key: "shortMeterDiscount",
      dataKey: "shortMeterDiscount",
      width: 105
    },
    {
      title: "折扣1 (%)",
      key: "discount1",
      dataKey: "discount1",
      width: 80
    },
    {
      title: "折扣2 (%)",
      key: "discount2",
      dataKey: "discount2",
      width: 80
    },
    {
      title: "折扣3 (%)",
      key: "discount3",
      dataKey: "discount3",
      width: 80
    },
    {
      title: "折扣4 (%)",
      key: "discount4",
      dataKey: "discount4",
      width: 80
    },
    {
      title: "含税单价",
      key: "taxUnitPrice",
      dataKey: "taxUnitPrice",
      width: 100
    },
    {
      title: "含税合计",
      key: "taxTotalAmount",
      dataKey: "taxTotalAmount",
      align: "center",
      width: ColumnWidth.Char6
    },
    {
      title: "匹配说明",
      key: "priceMatchRecord",
      dataKey: "priceMatchRecord",
      width: 120
    },
    // {
    //   title: "解析状态",
    //   key: "parseStatus",
    //   dataKey: "parseStatus",
    //   fixed: FixedDir.RIGHT,
    //   width: ColumnWidth.Char5
    // },
    {
      title: "匹配状态",
      key: "matchStatus",
      dataKey: "matchStatus",
      fixed: FixedDir.RIGHT,
      width: ColumnWidth.Char5
    }
  ];

  const originalColumns: Array<Column> = [
    {
      title: "Sheet",
      key: "excelSheet",
      dataKey: "excelSheet",
      width: ColumnWidth.Char4
    },
    {
      title: "原始行号",
      key: "excelRowIndex",
      dataKey: "excelRowIndex",
      width: ColumnWidth.Char4
    },
    {
      title: "原始数据",
      key: "columnRawContent",
      dataKey: "columnRawContent",
      width: ColumnWidth.Char16
    }
  ];

  return { columns, originalColumns };
}
