import { ProductUnitEnum } from "@/enums/quote-basic-data";
import { IBomMaterialQuotaFormExt } from "../bom-material-quota";

export interface IBomInformationForm {
  id?: string;
  /**
   * 产品分类
   */
  productCategory?: string;

  /**
   * 产品编号
   */
  productCode?: string;
  /**
   * 产品名称
   */
  productName?: string;
  /**
   * 型号
   */
  model?: string;
  /**
   * 规格
   */
  specification?: string;

  /**
   * 电压等级
   */
  voltage?: string;
  /**
   *  计量单位
   */
  productUnit?: ProductUnitEnum;

  /** 材料定额信息 */
  bomMaterialQuotaList?: Array<IBomMaterialQuotaFormExt>;
}
