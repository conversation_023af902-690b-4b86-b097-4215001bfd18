export const EMAIL_REGEXP =
  /^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
export const MOBILE_REGEXP = /^(?:(?:\+|00)86)?1(?:3\d|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8\d|9[189])\d{8}$/;

export const USER_NAME_REGEXP = /^[a-zA-Z0-9@_.]{3,20}$/;
export const PASSWORD_REGEXP = /^.{4,16}$/;
/** 字母数字正则 */
export const ALPHA_NUM_REGEXP = /^[a-zA-Z0-9]*$/;

/** 试验编号，过程检测编号 */
export const SPECIAL_CHARACTER_REGEXP = /^.*$/;

/** 供货单明细 计量单位 */
export const MEAS_UNIT_REGEXP = /(^[\u4e00-\u9fa5]+$)|(^[A-Za-z]+$)/;

/** 最高强度密码正则 */
export const HEIGHEST_PASSWORD_STRENGTH = /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{8,}$/;
/** 中等强度密码正则 */
export const MEDIUM_PASSWORD_STRENGTH =
  /^((?=.*[0-9])(?=.*[a-zA-Z])|(?=.*[0-9])(?=.*[\W_])|(?=.*[a-zA-Z])(?=.*[\W_])).{8,}$/;
/** 低强度密码正则 */
export const LOW_PASSWORD_STRENGTH = /^\d$/;
