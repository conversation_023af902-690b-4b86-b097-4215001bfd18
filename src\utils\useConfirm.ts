import { ElMessageBox, ElMessageBoxOptions } from "element-plus";

type Message = ElMessageBoxOptions["message"];
type Title = ElMessageBoxOptions["title"];
const defaultOptions: ElMessageBoxOptions = {
  confirmButtonText: "确定",
  cancelButtonText: "取消",
  type: "warning"
};

export function useConfirm(message: Message, title?: Title, options?: ElMessageBoxOptions): Promise<boolean> {
  title = title ?? "提示";
  options = options ? { ...defaultOptions, ...options } : defaultOptions;
  return ElMessageBox.confirm(message, title, options)
    .then(() => true)
    .catch(() => false);
}
