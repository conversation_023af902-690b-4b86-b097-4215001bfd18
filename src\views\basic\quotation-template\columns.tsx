import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "模板编号",
      prop: "templateCode"
    },
    {
      label: "状态",
      prop: "status",
      slot: "status"
    },

    {
      label: "创建时间",
      prop: "createTime"
    },
    {
      label: "创建人",
      prop: "creatorName"
    },
    {
      label: "更新时间",
      prop: "updateTime"
    },
    {
      label: "更新人",
      prop: "updaterName"
    },
    {
      label: "报价单模板",
      prop: "inquiryAttachmentName",
      slot: "inquiryAttachmentName"
    },
    {
      label: "备注",
      prop: "remark"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char6
    }
  ];
  return { columns };
}
