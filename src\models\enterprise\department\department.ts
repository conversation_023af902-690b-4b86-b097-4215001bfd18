import { TenantStatusEnum } from "@/enums";
import { IEmployee } from "../employee";

export interface IDepartment {
  id: string;
  name: string;
  code: string;
  parentId: string;
  sort: number;
  /** 负责人 */
  leaderUserId: string;
  leaderUser: IEmployee;
  status: TenantStatusEnum;
  /** 企业简介 */
  introduction: string;
  createTime: Date;

  /** 上级部门 */
  parentDepartment?: IDepartment;
  userCount: number;
  path: string;
}
