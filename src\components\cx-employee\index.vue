<template>
  <div v-if="props.name" class="employee">
    <el-icon color="#a8abb2"><UserFilled /></el-icon>
    <span class="name">{{ props.name }}</span>
  </div>
</template>

<script setup lang="ts">
import { UserFilled } from "@element-plus/icons-vue";

const props = withDefaults(
  defineProps<{
    name?: string;
  }>(),
  {
    name: ""
  }
);
</script>

<style scoped lang="scss">
.employee {
  padding: 2px 10px;
  background: #f4f4f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
  width: fit-content;

  .name {
    font-size: 13px;
    letter-spacing: 0;
    color: #606266;
  }
}
</style>
