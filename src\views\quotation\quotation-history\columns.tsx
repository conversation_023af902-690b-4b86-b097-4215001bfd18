import { ColumnWidth } from "@/enums";
export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "报价批次号",
      prop: "quotationBatchNo",
      slot: "quotationBatchNo",
      width: ColumnWidth.Char10
    },
    {
      label: "销售人员",
      prop: "operatorName",
      slot: "operatorName",
      width: ColumnWidth.Char6
    },
    {
      label: "所属部门",
      prop: "deptNames",
      width: ColumnWidth.Char8
    },
    {
      label: "询价时间",
      prop: "inquiryTime",
      width: ColumnWidth.Char12
    },
    {
      label: "解析完成时间",
      prop: "parseFinishTime",
      width: ColumnWidth.Char12
    },
    {
      label: "询价单位",
      prop: "inquiryOrgName",
      width: ColumnWidth.Char8
    },
    {
      label: "已解析数量",
      prop: "parsedInquiryCount",
      width: ColumnWidth.Char5,
      align: "center"
    },
    {
      label: "解析总数",
      prop: "totalInquiryParseCount",
      width: ColumnWidth.Char4,
      align: "center"
    },
    {
      label: "token用量",
      prop: "parseTokenUsage",
      width: ColumnWidth.Char4,
      align: "center",
      slot: "parseTokenUsage"
    },
    {
      label: "询价单",
      prop: "inquiryAttachmentName",
      slot: "inquiryAttachmentName",
      showOverflowTooltip: false,
      width: ColumnWidth.Char10
    },
    {
      label: "报价金额",
      align: "right",
      prop: "quotationAmount",
      slot: "quotationAmount",
      width: ColumnWidth.Char7
    },
    {
      label: "报价单",
      prop: "quotationAttachmentId",
      slot: "quotationAttachmentId",
      width: ColumnWidth.Char10
    },
    {
      label: "状态",
      prop: "parseStatus",
      slot: "parseStatus",
      width: ColumnWidth.Char6
    },
    {
      label: "中止原因",
      prop: "terminationReason",
      width: ColumnWidth.Char12
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char4
    }
  ];
  return { columns };
}
