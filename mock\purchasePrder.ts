import { MockMethod } from "vite-plugin-mock";

export default [
  {
    url: "/api/purchase-order/:id",
    method: "get",
    response: ({ query }) => {
      return {
        code: "",
        msg: "",
        data: {
          id: query.id,
          poNo: "4700213920",
          conCode: "S220723T2",
          conName: "铝包钢绞线,JLB20A,80...",
          subClassName: "电缆终端",
          buyerName: "江苏国网中心",
          sellerConCode: "SGXZWZ00HTMM20...",
          prjName: "上海奉贤新",
          sellerSignTime: "2022-01-18",
          fillingStep: "处理销售订单",
          syncResult: "同步成功",
          isNew: true,
          linkStep: "1",
          synType: true
        }
      };
    }
  }
] as MockMethod[];
