<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="left">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="单价成本" prop="price">
          <el-input-number
            class="!w-full"
            v-model="form.price"
            clearable
            controls-position="right"
            placeholder="请输入单价成本"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="计量单位" prop="productUnit">
          <el-input v-model="form.productUnit" clearable placeholder="请输入计量单位" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IRawMaterialCostForm } from "@/models";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});

const form = reactive<IRawMaterialCostForm>({});
const formRef = ref<FormInstance>();

const rules: FormRules = {
  price: [{ required: true, trigger: "change", message: "单价成本不能为空" }],
  productUnit: [{ required: true, trigger: "change", message: "计量单位不能为空" }]
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IRawMaterialCostForm) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}
</script>

<style scoped></style>
