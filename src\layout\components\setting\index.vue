<script setup lang="ts">
import { ref, unref, watch, reactive, computed, nextTick, onBeforeMount, watchEffect } from "vue";
import { useDark, debounce, useGlobal, storageLocal, storageSession } from "@pureadmin/utils";
import { getConfig } from "@/config";
import panel from "../panel/index.vue";
import { emitter } from "@/utils/mitt";
import { useNav } from "@/layout/hooks/useNav";
import { useAppStoreHook } from "@/store/modules/app";
import { toggleTheme } from "@pureadmin/theme/dist/browser-utils";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import Check from "@iconify-icons/ep/check";
import Logout from "@iconify-icons/ri/logout-circle-r-line";
import { useSystemAuthStore } from "@/store/modules";
import { getToken } from "@/utils/auth";
import { useUserStoreHook } from "@/store/modules/user";

const { isDark } = useDark();
const { device, tooltipEffect } = useNav();
const { $storage } = useGlobal<GlobalPropertiesApi>();
const mixRef = ref();
const verticalRef = ref();
const horizontalRef = ref();
const enterpriseRef = ref<boolean>();
const { layoutTheme, themeColors, dataThemeChange, setEpThemeColor, setLayoutThemeColor } = useDataThemeChange();
const systemAuthStore = useSystemAuthStore();

/* body添加layout属性，作用于src/style/sidebar.scss */
if (unref(layoutTheme)) {
  const layout = unref(layoutTheme).layout;
  const theme = unref(layoutTheme).theme;
  toggleTheme({
    scopeName: `layout-theme-${theme}`
  });
  setLayoutModel(layout);
}

const settings = reactive({
  greyVal: $storage.configure.grey,
  weakVal: $storage.configure.weak,
  tabsVal: $storage.configure.hideTabs,
  showLogo: $storage.configure.showLogo,
  showModel: $storage.configure.showModel,
  multiTagsCache: $storage.configure.multiTagsCache
});
const getThemeColorStyle = computed(() => {
  return color => {
    return { background: color };
  };
});
/** 当网页为暗黑模式时不显示亮白色切换选项 */
const showThemeColors = computed(() => {
  return themeColor => {
    return themeColor === "light" && isDark.value ? false : true;
  };
});
function storageConfigureChange<T>(key: string, val: T): void {
  const storageConfigure = $storage.configure;
  storageConfigure[key] = val;
  $storage.configure = storageConfigure;
}
function toggleClass(flag: boolean, clsName: string, target?: HTMLElement) {
  const targetEl = target || document.body;
  let { className } = targetEl;
  className = className.replace(clsName, "").trim();
  targetEl.className = flag ? `${className} ${clsName} ` : className;
}
const tagsChange = () => {
  const showVal = settings.tabsVal;
  storageConfigureChange("hideTabs", showVal);
  emitter.emit("tagViewsChange", showVal as unknown as string);
};
/** 清空缓存并返回登录页 */
function onReset() {
  storageLocal().clear();
  storageSession().clear();
  const { Grey, Weak, MultiTagsCache, EpThemeColor, Layout } = getConfig();
  useAppStoreHook().setLayout(Layout);
  setEpThemeColor(EpThemeColor);
  useMultiTagsStoreHook().multiTagsCacheChange(MultiTagsCache);

  toggleClass(Grey, "html-grey", document.querySelector("html"));
  toggleClass(Weak, "html-weakness", document.querySelector("html"));
  useUserStoreHook().logOut();
}

function setFalse(Doms): any {
  Doms.forEach(v => {
    toggleClass(false, "is-select", unref(v));
  });
}
/** 主题色 激活选择项 */
const getThemeColor = computed(() => {
  return current => {
    if (current === layoutTheme.value.theme && layoutTheme.value.theme !== "light") {
      return "#fff";
    } else if (current === layoutTheme.value.theme && layoutTheme.value.theme === "light") {
      return "#1d2b45";
    } else {
      return "transparent";
    }
  };
});

/** 设置导航模式 */
function setLayoutModel(layout: string) {
  layoutTheme.value.layout = layout;
  window.document.body.setAttribute("layout", layout);
  $storage.layout = {
    layout,
    theme: layoutTheme.value.theme,
    darkMode: $storage.layout?.darkMode,
    sidebarStatus: $storage.layout?.sidebarStatus,
    epThemeColor: $storage.layout?.epThemeColor
  };
  useAppStoreHook().setLayout(layout);
}
watch($storage, ({ layout }) => {
  switch (layout["layout"]) {
    case "vertical":
      toggleClass(true, "is-select", unref(verticalRef));
      debounce(setFalse([horizontalRef]), 50);
      debounce(setFalse([mixRef]), 50);
      break;
    case "horizontal":
      toggleClass(true, "is-select", unref(horizontalRef));
      debounce(setFalse([verticalRef]), 50);
      debounce(setFalse([mixRef]), 50);
      break;
    case "mix":
      toggleClass(true, "is-select", unref(mixRef));
      debounce(setFalse([verticalRef]), 50);
      debounce(setFalse([horizontalRef]), 50);
      break;
  }
});
onBeforeMount(() => {
  dataThemeChange();
  /* 初始化项目配置 */
  nextTick(() => {
    settings.greyVal && document.querySelector("html")?.setAttribute("class", "html-grey");
    settings.weakVal && document.querySelector("html")?.setAttribute("class", "html-weakness");
    settings.tabsVal && tagsChange();
  });
});

watchEffect(async () => {
  if (getToken()) {
    const isEnterprise = await systemAuthStore.getIsEnterprise;
    enterpriseRef.value = isEnterprise;
    if (enterpriseRef.value) {
      themeColors.value = themeColors.value.filter(x => x.themeColor !== "greyblue");
    }
  }
});
</script>

<template>
  <panel>
    <div class="panel-box">
      <el-scrollbar>
        <div class="panel-items">
          <div class="panel-item" v-if="enterpriseRef">
            <div class="px-4 font-semibold">主题色</div>
            <ul class="theme-color grid gap-x-2 gap-y-2 grid-cols-4">
              <li
                v-for="(item, index) in themeColors"
                :key="index"
                v-show="showThemeColors(item.themeColor)"
                :style="getThemeColorStyle(item.color)"
                @click="setLayoutThemeColor(item.themeColor)"
              >
                <el-icon style="margin: 20% auto" :size="24" :color="getThemeColor(item.themeColor)">
                  <IconifyIconOffline :icon="Check" />
                </el-icon>
              </li>
            </ul>
          </div>
          <el-divider v-if="enterpriseRef" />

          <div class="panel-item" v-if="enterpriseRef">
            <div class="px-4 font-semibold">导航栏模式</div>
            <ul class="pure-theme">
              <el-tooltip
                :effect="tooltipEffect"
                class="item"
                content="左侧模式"
                placement="bottom"
                popper-class="pure-tooltip"
              >
                <li
                  :class="layoutTheme.layout === 'vertical' ? 'is-select' : ''"
                  ref="verticalRef"
                  @click="setLayoutModel('vertical')"
                >
                  <div />
                  <div />
                </li>
              </el-tooltip>

              <el-tooltip
                v-if="device !== 'mobile'"
                :effect="tooltipEffect"
                class="item"
                content="顶部模式"
                placement="bottom"
                popper-class="pure-tooltip"
              >
                <li
                  :class="layoutTheme.layout === 'horizontal' ? 'is-select' : ''"
                  ref="horizontalRef"
                  @click="setLayoutModel('horizontal')"
                >
                  <div />
                  <div />
                </li>
              </el-tooltip>

              <el-tooltip
                v-if="device !== 'mobile'"
                :effect="tooltipEffect"
                class="item"
                content="混合模式"
                placement="bottom"
                popper-class="pure-tooltip"
              >
                <li
                  :class="layoutTheme.layout === 'mix' ? 'is-select' : ''"
                  ref="mixRef"
                  @click="setLayoutModel('mix')"
                >
                  <div />
                  <div />
                </li>
              </el-tooltip>
            </ul>
          </div>
        </div>
      </el-scrollbar>
      <el-button type="danger" size="large" style="width: 90%; margin-left: 5%" @click="onReset">
        <IconifyIconOffline :icon="Logout" width="15" height="15" style="margin-right: 4px" />
        清空缓存并返回登录页
      </el-button>
    </div>
  </panel>
</template>

<style lang="scss" scoped>
.panel-box {
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .panel-item {
    margin-top: 20px;
  }
}

:deep(.el-divider__text) {
  @apply font-semibold;
  font-size: 16px;
}

.is-select {
  border: 2px solid var(--el-color-primary);
}

.setting {
  width: 100%;

  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 25px;
  }
}

.pure-datatheme {
  width: 100%;
  height: 50px;
  text-align: center;
  display: block;
  padding-top: 25px;
}

.pure-theme {
  margin-top: 25px;
  width: 100%;
  height: 50px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;

  li {
    width: 18%;
    height: 45px;
    background: #f0f2f5;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border-radius: 4px;
    box-shadow: 0 1px 2.5px 0 rgb(0 0 0 / 18%);

    &:nth-child(1) {
      div {
        &:nth-child(1) {
          width: 30%;
          height: 100%;
          background: #1b2a47;
        }

        &:nth-child(2) {
          width: 70%;
          height: 30%;
          top: 0;
          right: 0;
          background: #fff;
          box-shadow: 0 0 1px #888;
          position: absolute;
        }
      }
    }

    &:nth-child(2) {
      div {
        &:nth-child(1) {
          width: 100%;
          height: 30%;
          background: #1b2a47;
          box-shadow: 0 0 1px #888;
        }
      }
    }

    &:nth-child(3) {
      div {
        &:nth-child(1) {
          width: 100%;
          height: 30%;
          background: #1b2a47;
          box-shadow: 0 0 1px #888;
        }

        &:nth-child(2) {
          width: 30%;
          height: 70%;
          bottom: 0;
          left: 0;
          background: #1b2a47;
          box-shadow: 0 0 1px #888;
          position: absolute;
        }
      }
    }
  }
}

.theme-color {
  width: 70%;
  margin: 0 auto;
  height: 180px;
  margin-top: 20px;

  li {
    float: left;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    font-weight: 600;
    text-align: center;
    cursor: pointer;

    &:last-of-type {
      border: 1px solid #ddd;
    }
  }
}

.font-theme {
  width: 70%;
  margin: 0 auto;
  height: 80px;
  margin-top: 20px;

  :deep(.el-slider .el-slider__stop) {
    border: 1px solid var(--el-slider-runway-bg-color);
  }
}
</style>
