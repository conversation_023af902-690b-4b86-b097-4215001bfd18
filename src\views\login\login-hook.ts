import { FormInstance } from "element-plus";
import { reactive, ref } from "vue";
import { useUserStoreHook } from "@/store/modules/user";
import { useSystemAuthStore } from "@/store/modules";
import { initRouter } from "@/router/utils";
import { useRoute, useRouter } from "vue-router";
import { message } from "@/utils/message";
// import { usePermissionStore } from "@/store/modules/permission";
import { router as routerInst } from "@/router";
import { postMessage } from "@/utils/browsingContextsCommunication";
import { TopicEnum } from "@/utils/browsingContextsCommunication/topic.enum";

export const useLoginHook = () => {
  const loading = ref(false);
  const systemAuthStore = useSystemAuthStore();
  const userStoreHook = useUserStoreHook();
  // const permissionStore = usePermissionStore();
  const router = useRouter();
  const route = useRoute();
  const form = reactive({
    username: "",
    password: ""
  });

  const login = async (formEl: FormInstance | undefined) => {
    loading.value = true;
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
      if (!valid) {
        loading.value = false;
        return fields;
      }
      const { username, password } = form;
      userStoreHook
        .loginByUsername(username, password)
        .then(() => {
          /** clear user profile if exist */
          userStoreHook.$reset();
          // 获取后端路由
          initRouter().then(async () => {
            message("登录成功", { type: "success" });
            postMessage(TopicEnum.LOGIN);
            // 查询授权信息
            await systemAuthStore.getBusinessLicenseAuth();
            routerInst.isReady().then(() => router.push(_getReturnPagePath()));
          });
        })
        .catch(() => (loading.value = false));
    });
  };

  function _getReturnPagePath(): string {
    const returnUrl: string = route.query.returnUrl as string;
    if (returnUrl) {
      return decodeURIComponent(returnUrl);
    }
    // console.log('permissionStore.wholeMenus', permissionStore.wholeMenus);
    // const { children, path } = permissionStore.wholeMenus?.[0] || { children: [], path: "/" };
    return "/home"; // children?.[0]?.path || path;
  }

  return { login, loading, form };
};
