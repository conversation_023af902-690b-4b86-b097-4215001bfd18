#!/bin/sh

set -e # 脚本在遇到任何非零退出状态时立即退出。强烈建议在启动脚本中使用，因为它可以显著提高脚本的可靠性和错误处理能力。

echo "Starting up..."
echo "Current directory: $(pwd)"
echo "Contents of current directory: $(ls -la)"

jq --version

JSON=$(cat /usr/share/nginx/html/serverConfig.json)
if [ ! -z "$JSON" ]; then
  echo $JSON

  if [ ! -z "$MODE" ]; then
    JSON=$(echo $JSON | jq --arg val "$MODE" '. + {"Mode": $val}')
  fi

  if [ ! -z "$LAYOUT" ]; then
    JSON=$(echo $JSON | jq --arg val "$LAYOUT" '. + {"Layout": $val}')
  fi

  if [ ! -z "$THEME" ]; then
    JSON=$(echo $JSON | jq --arg val "$THEME" '. + {"Theme": $val}')
  fi

  if [ ! -z "$TITLE" ]; then
    JSON=$(echo $JSON | jq --arg val "$TITLE" '. + {"Title": $val}')
  fi

  if [ ! -z "$VERSION" ]; then
    JSON=$(echo $JSON | jq --arg val "$VERSION" '. + {"Version": $val}')
  fi

  if [ ! -z "$AppId" ]; then
    JSON=$(echo $JSON | jq --arg val "$AppId" '. + {"AppId": $val}')
  fi

  echo $JSON

  echo $JSON >/usr/share/nginx/html/serverConfig.json
fi

# 检查是否存在挂载的 default.conf 文件
if [ -f /etc/nginx/conf.d/default.custom ] && [ ! -L /etc/nginx/conf.d/default.custom ]; then
  echo "使用挂载的 default.conf 文件"
  CONFIG_FILE="/etc/nginx/conf.d/default.custom"
else
  echo "使用默认的 default.template 文件"
  CONFIG_FILE="/etc/nginx/conf.d/default.template"
fi

echo "Using config file: $CONFIG_FILE"
echo "Config file contents:"
cat $CONFIG_FILE

# 执行环境变量替换
echo "Performing environment variable substitution..."
envsubst '${API_HOST} ${WEBSOCKET_HOST}' <$CONFIG_FILE >/etc/nginx/conf.d/default.conf.tmp
mv /etc/nginx/conf.d/default.conf.tmp /etc/nginx/conf.d/default.conf

echo "Final config file contents:"
cat /etc/nginx/conf.d/default.conf

# 启动 Nginx
echo "Starting Nginx..."
exec nginx -g 'daemon off;'
