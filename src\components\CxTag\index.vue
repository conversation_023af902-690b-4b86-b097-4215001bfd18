<template>
  <el-tag :class="clazz" :disable-transitions="true" :size="size" :closable="closable" @close="onClose()">
    <FontIcon class="mr-1" v-if="props.icon" :icon="props.icon" />
    <IconifyIconOffline class="mr-1" v-if="props.iconify" :icon="props.iconify" />
    <slot />
  </el-tag>
</template>
<script setup lang="ts">
import { computed } from "vue";

const emits = defineEmits<{
  (e: "close"): void;
}>();

const props = defineProps<{
  type?: "success" | "warning" | "info" | "danger" | "primary" | string;
  icon?: string;
  iconify?: any;
  size?: "large" | "small" | "default";
  closable?: boolean;
}>();

const clazz = computed(() => `el-tag--${props.type}`);

const onClose = () => {
  emits("close");
};
</script>
<style scoped lang="scss">
:deep(.el-tag__content) {
  display: inline-flex;
  align-items: center;
  line-height: 24px;
}
</style>
