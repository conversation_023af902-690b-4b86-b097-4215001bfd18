<template>
  <div class="bg-bg_color flex items-center">
    <p
      v-if="title"
      class="px-6 py-2 text-xl text-primaryText font-semibold whitespace-normal break-words overflow-hidden"
    >
      {{ title }}
    </p>
    <div v-if="componentCmp">
      <component :is="componentCmp" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePageStoreHook } from "@/store/modules/page";
import { computed } from "vue";

const pageStoreHook = usePageStoreHook();

const title = computed(() => pageStoreHook.title);
const componentCmp = computed(() => pageStoreHook.component);
</script>

<style scoped></style>
