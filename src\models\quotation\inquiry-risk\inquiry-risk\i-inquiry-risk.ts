import { RiskStatusEnum } from "@/enums";

export interface IInquiryRisk {
  riskbatchNo?: string;
  /**
   * 最高相似度
   */
  highRiskSimilarity?: number;
  /**
   * 风险ID
   */
  id?: number;
  /**
   * 询价单位ID
   */
  inquiryOrgId?: number;
  /**
   * 询价单位名称
   */
  inquiryOrgName?: number;
  /**
   * 询价任务id
   */
  inquiryTaskId?: number;
  /**
   * 询价单操作人ID
   */
  operatorId?: number;
  /**
   * 询价单操作人名称
   */
  operatorName?: string;

  /** 关联销售 */
  similarityInquiryOperatorName?: Array<string>;
  /**
   * 处理人
   */
  riskDisposeOperatorId?: Date;
  /**
   * 处理人
   */
  riskDisposeOperatorName?: Date;
  /**
   * 风险处理时间
   */
  riskDisposeTime?: Date;
  /**
   * 处理备注
   */
  riskRemark?: string;
  /**
   * 风险状态
   */
  riskStatus?: RiskStatusEnum;
  /**
   * 风险时间
   */
  riskTime?: Date;
  /**
   * 相似询价单数量
   */
  similarityInquiryTaskSize?: number;

  inquiryAttachmentUrl?: string;
  inquiryAttachmentId?: string;
  inquiryAttachmentName?: string;
}
