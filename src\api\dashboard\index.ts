import {
  IResponse,
  ISalespersonRank,
  IQuotationAnalysisReq,
  IQuotationAnalysis,
  IDateRange,
  IDataStatistics,
  IStatisticsInquiryOrganization
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

/** 销售人员数据排行 */
export const getSalespersonRankList = (data?: IDateRange) => {
  return http.post<IDateRange, IResponse<Array<ISalespersonRank>>>(
    withApiGateway("admin-api/business/dashboard/getSalespersonRankList"),
    { data }
  );
};

/** 报价单分析 */
export const getQuotationAnalysisList = (data: IQuotationAnalysisReq) => {
  return http.post<IQuotationAnalysisReq, IResponse<Array<IQuotationAnalysis>>>(
    withApiGateway("admin-api/business/dashboard/getQuotationAnalysisList"),
    { data }
  );
};

/** 询价单位数量Top */
export const getInquiryOrganizationList = (data: IDateRange) => {
  return http.post<IDateRange, IResponse<Array<IStatisticsInquiryOrganization>>>(
    withApiGateway("admin-api/business/dashboard/getInquiryOrganizationList"),
    { data }
  );
};

/** 近7日在线人数趋势 */
export const getOnlineData = () => {
  return http.get<void, IResponse<Array<number>>>(withApiGateway("admin-api/business/dashboard/onlineData"));
};

/** 获取数据统计 */
export const getDataStatistics = () => {
  return http.get<void, IResponse<IDataStatistics>>(withApiGateway("admin-api/business/dashboard/dataStatistics"));
};
