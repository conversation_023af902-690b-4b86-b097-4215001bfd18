<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="bg-bg_color pr-6 px-6 py-3">
      <div class="flex items-center pb-4">
        <div class="flex items-center" @click="onBack()">
          <el-icon color="#30313"><Back /></el-icon>
          <div class="back">返回</div>
        </div>
        <div class="risk-no">询价风险 {{ state.inquiryRisk?.riskbatchNo }}</div>
        <CXTag :type="RiskStatusEnumMapColor[state.inquiryRisk?.riskStatus]">{{
          RiskStatusEnumMapDesc[state.inquiryRisk?.riskStatus]
        }}</CXTag>
      </div>
      <div class="flex items-center justify-between">
        <el-descriptions class="risk-info" :column="4">
          <el-descriptions-item class-name="value" label-class-name="label" label="告警时间">{{
            state.inquiryRisk?.riskTime
          }}</el-descriptions-item>
          <el-descriptions-item class-name="value" label-class-name="label" label="询价单位">{{
            state.inquiryRisk?.inquiryOrgName
          }}</el-descriptions-item>
          <el-descriptions-item class-name="value" label-class-name="label" label="销售人员"
            ><CxEmployee :name="state.inquiryRisk?.operatorName"
          /></el-descriptions-item>
          <el-descriptions-item class-name="value" label-class-name="label" label="询价单">
            <div class="flex items-center gap-1 cursor-pointer">
              <CXDownload
                iconType="excel"
                :name="state.inquiryRisk.inquiryAttachmentName"
                :id="state.inquiryRisk.inquiryAttachmentId"
              />
            </div>
          </el-descriptions-item>
          <el-descriptions-item class-name="value" label-class-name="label" label="处理时间">{{
            state.inquiryRisk?.riskDisposeTime
          }}</el-descriptions-item>
          <el-descriptions-item class-name="value" label-class-name="label" label="处理备注">{{
            state.inquiryRisk?.riskRemark
          }}</el-descriptions-item>
        </el-descriptions>
        <ElButton
          v-auth="PermissionKey.quotation.quotationRiskHandle"
          v-if="state.inquiryRisk?.riskStatus === RiskStatusEnum.UNDISPOSED"
          type="primary"
          @click="onHandleRiskRecord()"
          >处理</ElButton
        >
      </div>
    </div>

    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <div class="detail">相似询价记录</div>
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="onPageCurrentChange"
        @page-size-change="onPageSizeChange"
      >
        <template #operatorName="{ row }">
          <CxEmployee :name="row.operatorName" />
        </template>

        <template #riskSimilarity="{ row }">
          <template v-if="row.riskSimilarity">{{ row.riskSimilarity }}%</template>
        </template>
        <template #inquiryItemSize="{ row }">
          <template v-if="row.riskSimilarity">{{ row.inquiryItemSize }}条</template>
        </template>
        <template #inquiryAttachmentName="{ row }">
          <CXDownload :name="row.inquiryAttachmentName" :id="row.inquiryAttachmentId" />
        </template>

        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
    <RiskRecordHandleDialog
      :id="state.id"
      :ids="state.ids"
      v-model="state.riskRecordHandleDialogVisible"
      @onSaveSuccess="handleRiskSuccess()"
    />
  </div>
</template>

<script setup lang="ts" name="riskRecord">
import { onMounted, ref, reactive } from "vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IInquiryRisk, IInquiryRiskDetail, IInquiryRiskReq } from "@/models";
import { ElButton } from "element-plus";
import CxEmployee from "@/components/cx-employee/index.vue";
import { Back } from "@element-plus/icons-vue";
import RiskRecordHandleDialog from "../handle/dialog.vue";
import { getInquiryRiskDetailById, queryInquiryRiskDetailList } from "@/api/quotation/inquiry-risk/inquiry-risk";
import { useRoute, useRouter } from "vue-router";
import { RiskStatusEnumMapDesc, RiskStatusEnumMapColor, RiskStatusEnum } from "@/enums";
import CXDownload from "@/components/cx-download/index.vue";
import CXTag from "@/components/CxTag/index.vue";
import { PermissionKey } from "@/consts/permission-key";

const { pagination } = useTableConfig();
const { columns } = useColumns();

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const state = reactive<{
  list: Array<IInquiryRiskDetail>;
  params: IInquiryRiskReq;
  id?: string;
  ids: Array<string>;
  riskRecordHandleDialogVisible: boolean;
  inquiryRisk: IInquiryRisk;
}>({
  list: [],
  params: {},
  riskRecordHandleDialogVisible: false,
  inquiryRisk: {},
  ids: []
});

onMounted(() => {
  state.id = route.params.id as string;
  handleGetRiskRecordDetail(state.id);
  handleQueryInquiryRiskDetailList();
});

const onHandleRiskRecord = () => {
  state.riskRecordHandleDialogVisible = true;
  state.ids = [state.id];
};

const onPageCurrentChange = () => {
  handleQueryInquiryRiskDetailList();
};

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  handleQueryInquiryRiskDetailList();
};

const handleQueryInquiryRiskDetailList = useLoadingFn(async () => {
  state.params.pageNo = pagination.currentPage;
  state.params.pageSize = pagination.pageSize;
  state.params.riskId = state.id;
  const { data } = await queryInquiryRiskDetailList(state.params);
  state.list = data.list;
  pagination.total = data.total;
}, loading);

const handleGetRiskRecordDetail = async id => {
  const { data } = await getInquiryRiskDetailById(id);
  state.inquiryRisk = data;
};

const handleRiskSuccess = () => {
  handleGetRiskRecordDetail(state.id);
  handleQueryInquiryRiskDetailList();
};

const onBack = () => {
  router.back();
};
</script>

<style lang="scss" scoped>
.back {
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0;
  color: #909399;
  padding: 0 10px 0 12px;
  position: relative;
  cursor: pointer;
  margin-right: 25px;

  &::after {
    content: "";
    position: absolute;
    height: 15px;
    top: 3px;
    width: 2px;
    right: 0;
    background-color: #909399;
  }
}

.detail {
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
  letter-spacing: 0;
  color: #303133;
  margin-bottom: 20px;
}

.risk-no {
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
  letter-spacing: 0;
  color: #303133;
  margin-right: 10px;
}

.risk-info {
  flex: 1;

  :deep(.el-descriptions__body) {
    .el-descriptions__table {
      tbody {
        tr {
          td {
            width: 25% !important;

            .label {
              font-size: 14px;
              font-weight: normal;
              line-height: 22px;
              letter-spacing: 0;
              color: #303133;
              margin-right: 16px;
            }

            .value {
              font-size: 14px;
              line-height: 22px;
              letter-spacing: 0;
              color: #606266;
            }
          }
        }

        tr:nth-of-type(2) {
          display: block;
          padding-top: 15px;

          td:nth-of-type(2) {
            width: 75% !important;
          }
        }
      }
    }
  }
}
</style>
