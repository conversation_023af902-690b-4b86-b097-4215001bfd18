<template>
  <div class="absolute wave-progress" :class="status">
    <canvas id="canvas" :width="width" :height="height" />
    <div class="flex-c w-full h-full font-semibold absolute z-10 text top-0 left-0">
      <span class="text-4xl font-barlow" :class="{ '!text-white': percentage > 80 }">
        {{ percentage || 0 }}
      </span>
      <span class="inline-block absolute right-4 text-base translate-y-1/4" :class="{ '!text-white': percentage > 80 }"
        >%</span
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { hexToRgb } from "@pureadmin/utils";
import { computed, onMounted, onUnmounted, watchEffect } from "vue";
import { WaveGroup } from "./wave-group";

const props = withDefaults(
  defineProps<{
    percentage: number;
    status?: "success" | "exception" | "";
  }>(),
  {
    percentage: 0
  }
);

let ctx: CanvasRenderingContext2D;
const width = 100;
const height = 100;
const drawStep = 0.3;
let offsetY: number = height;
let primaryColor: string;
let dangerColor: string;
const waveGroup = new WaveGroup();
let requestAnimationFrameHandle: number;

const total = computed(() => (height * (100 - (props.percentage >= 100 ? 100 : props.percentage))) / 100);

onMounted(async () => {
  const canvas = document.getElementById("canvas") as HTMLCanvasElement;
  const style: CSSStyleDeclaration = getComputedStyle(canvas);
  primaryColor = hexToRgb(style.getPropertyValue("--el-color-primary")).toString();
  dangerColor = hexToRgb(style.getPropertyValue("--el-color-danger")).toString();
  ctx = canvas.getContext("2d");
  waveGroup.resize(width, height);

  watchEffect(() => draw());
  listenStatusChange();
});

onUnmounted(() => {
  cancelAnimationFrame(requestAnimationFrameHandle);
});

function draw() {
  cancelAnimationFrame(requestAnimationFrameHandle);
  ctx.clearRect(0, 0, width, height);
  if (total.value === height) {
    return;
  }
  offsetY = Math.max(offsetY - drawStep, total.value);
  waveGroup.setOffsetY(offsetY);
  waveGroup.draw(ctx, offsetY === 0);
  if (offsetY === total.value && offsetY === 0) {
    return;
  }
  requestAnimationFrameHandle = requestAnimationFrame(draw);
}

function listenStatusChange() {
  watchEffect(() => {
    const baseColor =
      {
        success: primaryColor,
        exception: dangerColor
      }[props.status] || primaryColor;
    const color = `rgba(${baseColor}, .4)`;
    waveGroup.setColor(color);
    draw();
  });
}
</script>

<style scoped>
#canvas {
  background: #fff;
  border-radius: 50%;
  border: 2px solid #ccf0e4;
  outline: 2px solid #ccf0e4;
  box-shadow: 0px 4px 20px 0px rgba(0, 114, 75, 0.3);
}

.wave-progress {
  --color: var(--el-color-primary);
}

.exception {
  --color: var(--el-color-danger);
}

.text {
  color: var(--color);
}
</style>
