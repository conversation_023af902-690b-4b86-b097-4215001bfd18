<template>
  <div
    class="batch-button text-base flex-c border-[1px] border-solid border-[#dcdfe6]"
    :class="{ 'action-disabled': props.button.disabled }"
    v-auth="button.permission || ''"
    @click="props.button.action"
  >
    <span class="truncate max-w-[100px]">{{ props.button.name }}</span>
  </div>
</template>
<script setup lang="ts">
const props = defineProps<{
  button: {
    name: string;
    disabled: boolean;
    action: () => void;
    // 修改为支持权限绑定
    permission?: string;
  };
}>();
</script>

<style scoped>
.txt {
  color: var(--el-text-color-placeholder);
}

.close {
  color: var(--el-text-color-placeholder);
  cursor: pointer;
}

.batch-button {
  border-radius: 4px;
  padding: 5px 14px;
  cursor: pointer;
  color: var(--text-color-primary);
}

.action-disabled {
  color: var(--disabled-text-color);
  cursor: not-allowed;
}
</style>
