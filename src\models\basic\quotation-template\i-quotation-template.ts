import { StatusEnum } from "@/enums";
import { IBase } from "@/models";

export interface IQuotationTemplate extends IBase {
  id?: string;
  /**
   * 模板编号
   */
  templateCode?: string;
  /**
   * 启用状态
   */
  status?: StatusEnum;
  /**
   * 备注
   */
  remark?: string;

  /**询价单附件 */
  inquiryAttachmentId?: string;

  /** 询价单附件名称  */
  inquiryAttachmentName?: string;

  /** 询价单模版URL */
  inquiryAttachmentUrl?: string;
}
