import { IBase, IProduct } from "@/models";

export interface IProductInfo extends IBase {
  /** 版本 */
  versionId: string;

  /*** 产品分类名称 */
  categoryName?: string;

  /** 型号名称 */
  modelName?: string;

  /** 电压等级 */
  voltage?: string;

  /** 单价 */
  price?: number;

  /**  材料成本 成本价 */
  costPrice?: number;

  /** 备注 （单价信息的备注） */
  remark?: string;

  /** 产品 */
  product?: IProduct;

  /** 规格 */
  specification?: string;
}
