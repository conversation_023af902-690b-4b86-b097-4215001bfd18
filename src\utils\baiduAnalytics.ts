export function initBaiduAnalytics() {
  // 防止重复加载
  if (window._hmt) return;

  window._hmt = window._hmt || [];
  (function () {
    const hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?9f279528b37f6a8d2bf19bf14cf3a66a";
    const s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
  })();
}

// 单页面应用路由变化时上报
export function trackPageView(to) {
  if (window._hmt && to.path) {
    window._hmt.push(["_trackPageview", to.path]);
  }
}
