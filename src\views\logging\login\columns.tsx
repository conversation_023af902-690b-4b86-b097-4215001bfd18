import { TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "用户名",
      prop: "username",
      width: TableWidth.name
    },
    {
      label: "姓名",
      prop: "nickName",
      width: TableWidth.name
    },
    {
      label: "登录时间",
      prop: "loginTime",
      width: TableWidth.dateRanger,
      sortable: "custom",
      formatter: dateFormatter()
    },
    {
      label: "IP地址",
      prop: "ipAddress",
      width: TableWidth.name
    },
    {
      label: "城市",
      prop: "ipCity",
      width: TableWidth.name
    },
    {
      label: "登录方式",
      prop: "loginMethod",
      width: TableWidth.name,
      slot: "loginMethod"
    },
    {
      label: "登录设备",
      prop: "loginDevice",
      minWidth: TableWidth.largeName
    }
  ];
  return { columns };
}
